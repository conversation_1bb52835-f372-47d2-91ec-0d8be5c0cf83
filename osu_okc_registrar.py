#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSU OKC 自动化注册机
基于chrome_final_working.py的Chrome配置
集成邮箱PIN码获取和数据处理功能
"""

import os
import random
import time
import json
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys

# 导入自定义模块
from email_utils import get_pin_code, generate_temp_email
from data_utils import get_random_person, generate_email_for_person, get_formatted_birthdate

# 输出目录
REGISTRATION_OUTPUT_DIR = "registrations"

# 配置：是否在成功后保持浏览器打开（用于调试）
KEEP_BROWSER_OPEN_AFTER_SUCCESS = False
AUTO_CLOSE_DELAY_SECONDS = 2

# 线程本地上下文
context = {"hs_grad_year": None, "high_school_name": "", "phone": ""}
_thread_ctx = threading.local()

def init_context_for_thread():
    _thread_ctx.data = dict(context)

def set_ctx(key, value):
    if not hasattr(_thread_ctx, 'data'):
        init_context_for_thread()
    _thread_ctx.data[key] = value

def get_ctx(key, default=None):
    if hasattr(_thread_ctx, 'data') and key in _thread_ctx.data:
        return _thread_ctx.data[key]
    return context.get(key, default)

def get_chrome_path():
    """自动检测Chrome路径（从chrome_final_working.py复制）"""
    username = os.getenv('USERNAME', 'Administrator')
    possible_paths = [
        rf"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe",
        rf"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None

def setup_chrome_driver(headless: bool = False):
    """设置Chrome驱动（基于chrome_final_working.py），支持无头模式"""
    try:
        # 1. 配置代理环境
        os.environ['NO_PROXY'] = 'localhost,127.0.0.1,::1'
        print("✅ 代理环境已配置")
        
        # 2. 检测Chrome路径
        chrome_path = get_chrome_path()
        if not chrome_path:
            print("❌ 未找到Chrome浏览器")
            return None
        print(f"✅ 找到Chrome: {chrome_path}")
        
        # 3. 配置Chrome选项
        chrome_options = Options()
        chrome_options.binary_location = chrome_path
        chrome_options.add_argument('--proxy-bypass-list=localhost,127.0.0.1,::1')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--window-size=1200,800')
        chrome_options.add_argument('--remote-debugging-port=0')
        if headless:
            # 使用新版Headless以更好兼容性
            chrome_options.add_argument('--headless=new')
            chrome_options.add_argument('--disable-gpu')
        
        # 禁用自动化检测
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        print("✅ Chrome选项已配置")
        
        # 4. 启动Chrome
        chromedriver_path = os.path.join(os.getcwd(), "chromedriver.exe")
        if not os.path.exists(chromedriver_path):
            print("❌ 当前目录没有chromedriver.exe")
            return None
        
        port = random.randint(9000, 9999)
        service = Service(chromedriver_path, port=port)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("✅ Chrome有头模式启动成功！" if not headless else "✅ Chrome无头模式启动成功！")
        print(f"🔧 使用端口: {port}")
        
        # 5. 执行反检测脚本
        try:
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✅ 反检测脚本执行成功")
        except Exception as e:
            print(f"⚠️ 反检测脚本失败: {e}")
        
        # 6. 设置超时
        driver.implicitly_wait(10)
        driver.set_page_load_timeout(30)
        
        return driver
        
    except Exception as e:
        print(f"❌ Chrome启动失败: {e}")
        return None

def wait_for_element(driver, by, value, timeout=10):
    """等待元素可见（优化速度）"""
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.element_to_be_clickable((by, value))
        )
        return element
    except TimeoutException:
        print(f"⏰ 等待元素超时: {value}")
        return None

def wait_for_element_present(driver, by, value, timeout=8):
    """等待元素出现（更快的检查）"""
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
        return element
    except TimeoutException:
        print(f"⏰ 等待元素出现超时: {value}")
        return None

def safe_click(driver, element):
    """安全点击元素（优化速度）"""
    try:
        driver.execute_script("arguments[0].scrollIntoView(true);", element)
        WebDriverWait(driver, 3).until(EC.element_to_be_clickable(element))
        element.click()
        time.sleep(0.3)  # 减少等待时间
        return True
    except Exception as e:
        print(f"❌ 点击失败: {e}")
        return False

def safe_fill_input(driver, element, text):
    """安全填写输入框（优化速度）"""
    try:
        element.clear()
        element.send_keys(text)
        time.sleep(0.1)  # 大幅减少等待时间
        return True
    except Exception as e:
        print(f"❌ 填写失败: {e}")
        return False

def step1_visit_application_page(driver):
    """步骤1: 访问OSU OKC申请页面"""
    print("\n🌐 步骤1: 访问OSU OKC申请页面...")
    try:
        driver.get("https://apply.osuokc.edu/apply/")
        # 等待页面关键元素加载，而不是固定时间
        wait_for_element_present(driver, By.XPATH, "//a[contains(@href, '/account/register')]", 15)
        print(f"✅ 页面标题: {driver.title}")
        return True
    except Exception as e:
        print(f"❌ 访问页面失败: {e}")
        return False

def step2_click_create_account(driver):
    """步骤2: 点击Create an account链接"""
    print("\n👆 步骤2: 点击Create an account...")
    try:
        # 查找Create an account链接
        create_account_link = wait_for_element(driver, By.XPATH, "//a[contains(@href, '/account/register')]", 8)
        if not create_account_link:
            return False
        
        if safe_click(driver, create_account_link):
            # 等待表单页面加载
            wait_for_element_present(driver, By.ID, "email", 10)
            print("✅ 成功点击Create an account")
            return True
        return False
    except Exception as e:
        print(f"❌ 点击Create an account失败: {e}")
        return False

def step3_fill_registration_form(driver, person_info, email):
    """步骤3: 填写注册表单"""
    print("\n📝 步骤3: 填写注册表单...")
    try:
        # 填写邮箱
        email_input = wait_for_element(driver, By.ID, "email", 8)
        if not email_input or not safe_fill_input(driver, email_input, email):
            return False
        print(f"✅ 填写邮箱: {email}")
        
        # 填写名字
        first_input = wait_for_element(driver, By.ID, "first", 5)
        if not first_input or not safe_fill_input(driver, first_input, person_info['firstname']):
            return False
        print(f"✅ 填写名字: {person_info['firstname']}")
        
        # 填写姓氏
        last_input = wait_for_element(driver, By.ID, "last", 5)
        if not last_input or not safe_fill_input(driver, last_input, person_info['lastname']):
            return False
        print(f"✅ 填写姓氏: {person_info['lastname']}")
        
        # 填写生日
        birthdate = get_formatted_birthdate()
        
        # 月份
        month_select = Select(wait_for_element(driver, By.ID, "birthdate_m", 5))
        month_select.select_by_value(birthdate['month'])
        print(f"✅ 选择月份: {birthdate['month']}")
        
        # 日期
        day_select = Select(wait_for_element(driver, By.ID, "birthdate_d", 5))
        day_select.select_by_value(birthdate['day'])
        print(f"✅ 选择日期: {birthdate['day']}")
        
        # 年份
        year_select = Select(wait_for_element(driver, By.ID, "birthdate_y", 5))
        year_select.select_by_value(birthdate['year'])
        print(f"✅ 选择年份: {birthdate['year']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 填写表单失败: {e}")
        return False

def step4_submit_registration(driver):
    """步骤4: 提交注册表单"""
    print("\n🚀 步骤4: 提交注册表单...")
    try:
        # 查找Continue按钮
        continue_btn = wait_for_element(driver, By.XPATH, "//button[@type='submit' and contains(text(), 'Continue')]", 8)
        if not continue_btn:
            return False
        
        if safe_click(driver, continue_btn):
            # 等待成功提示或下一页面
            try:
                # 尝试等待成功提示
                WebDriverWait(driver, 8).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.XPATH, "//p[contains(@class, 'success')]")),
                        EC.presence_of_element_located((By.ID, "password"))  # PIN码输入页面
                    )
                )
                print("✅ 注册表单提交成功，等待PIN码邮件...")
                return True
            except TimeoutException:
                print("⚠️ 未找到明确提示，但可能已提交")
                return True
        return False
    except Exception as e:
        print(f"❌ 提交表单失败: {e}")
        return False

def step5_get_pin_code(email):
    """步骤5: 获取PIN码"""
    print("\n📧 步骤5: 获取邮件PIN码...")
    try:
        pin_code = get_pin_code(email)
        if pin_code:
            print(f"✅ 获取到PIN码: {pin_code}")
            return pin_code
        else:
            print("❌ 未能获取PIN码")
            return None
    except Exception as e:
        print(f"❌ 获取PIN码失败: {e}")
        return None

def step6_activate_account(driver, pin_code):
    """步骤6: 激活账户"""
    print("\n🔐 步骤6: 使用PIN码激活账户...")
    try:
        # 填写PIN码
        pin_input = wait_for_element(driver, By.ID, "password", 8)
        if not pin_input or not safe_fill_input(driver, pin_input, pin_code):
            return False
        print(f"✅ 填写PIN码: {pin_code}")
        
        # 填写生日验证
        birthdate = get_formatted_birthdate()
        
        # 月份
        month_select = Select(wait_for_element(driver, By.ID, "birthdate_m", 5))
        month_select.select_by_value(birthdate['month'])
        
        # 日期
        day_select = Select(wait_for_element(driver, By.ID, "birthdate_d", 5))
        day_select.select_by_value(birthdate['day'])
        
        # 年份
        year_select = Select(wait_for_element(driver, By.ID, "birthdate_y", 5))
        year_select.select_by_value(birthdate['year'])
        
        print("✅ 填写生日验证完成")
        
        # 点击Login按钮
        login_btn = wait_for_element(driver, By.XPATH, "//button[@type='submit' and contains(text(), 'Login')]", 8)
        if not login_btn:
            return False
        
        if safe_click(driver, login_btn):
            # 等待密码设置页面
            wait_for_element_present(driver, By.ID, "p1", 10)
            print("✅ 账户激活提交成功")
            return True
        return False
        
    except Exception as e:
        print(f"❌ 激活账户失败: {e}")
        return False

def step7_set_password(driver):
    """步骤7: 设置密码"""
    print("\n🔑 步骤7: 设置账户密码...")
    try:
        password = "Huangkun729."
        
        # 新密码
        p1_input = wait_for_element(driver, By.ID, "p1", 8)
        if not p1_input or not safe_fill_input(driver, p1_input, password):
            return False
        print("✅ 填写新密码")
        
        # 确认密码
        p2_input = wait_for_element(driver, By.ID, "p2", 5)
        if not p2_input or not safe_fill_input(driver, p2_input, password):
            return False
        print("✅ 确认密码")
        
        # 点击Set Password按钮
        set_btn = wait_for_element(driver, By.XPATH, "//button[@type='submit' and contains(text(), 'Set Password')]", 8)
        if not set_btn:
            return False
        
        if safe_click(driver, set_btn):
            # 等待主页面加载
            wait_for_element_present(driver, By.ID, "start_application_link", 12)
            print("✅ 密码设置成功")
            return True
        return False
        
    except Exception as e:
        print(f"❌ 设置密码失败: {e}")
        return False

def step8_start_application(driver):
    """步骤8: 开始申请流程"""
    print("\n🎓 步骤8: 开始申请流程...")
    try:
        # 查找Start New Application链接
        start_link = wait_for_element(driver, By.ID, "start_application_link", 10)
        if not start_link:
            # 尝试滚动到页面底部
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)
            start_link = wait_for_element(driver, By.ID, "start_application_link", 5)
            if not start_link:
                return False
        
        if safe_click(driver, start_link):
            # 等待弹窗加载
            period_select_element = wait_for_element(driver, By.ID, "period", 8)
            if not period_select_element:
                return False
            
            # 选择2026 Applications
            period_select = Select(period_select_element)
            period_select.select_by_visible_text("2026 Applications")
            print("✅ 选择2026 Applications")
            time.sleep(0.5)  # 短暂等待子选项加载
            
            # 选择Fall 2026 Application
            round_select = Select(wait_for_element(driver, By.XPATH, "//select[@id='round' and @name='round']", 5))
            round_select.select_by_visible_text("Fall 2026 Application")
            print("✅ 选择Fall 2026 Application")
            
            # 点击Create Application
            create_btn = wait_for_element(driver, By.XPATH, "//button[contains(text(), 'Create Application')]", 8)
            if create_btn and safe_click(driver, create_btn):
                # 等待下一个弹窗
                open_btn = wait_for_element(driver, By.XPATH, "//button[contains(text(), 'Open Application')]", 8)
                if open_btn and safe_click(driver, open_btn):
                    print("✅ 申请流程启动成功！")
                    return True
        return False
        
    except Exception as e:
        print(f"❌ 启动申请失败: {e}")
        return False

def step9_fill_personal_info(driver, person_info):
    """步骤9: 填写地址和个人信息表单"""
    print("\n📋 步骤9: 填写地址和个人信息表单...")
    
    try:
        # 等待地址表单页面加载
        time.sleep(3)
        
        # 预定义电话号码
        phone_numbers = [
            "2542492894",  # +1(254)249-2894
            "2542492895",  # +1(254)249-2895  
            "6478128893",  # +1(647)812-8893
            "2894278884"   # +1(289)427-8884
        ]
        selected_phone = random.choice(phone_numbers)
        set_ctx("phone", selected_phone)
        print(f"📞 选择电话号码: {selected_phone}")
        
        # 1. 添加第一个地址 (Mailing Address)
        if not add_address(driver, person_info, "mailing", selected_phone):
            return False
            
        # 2. 添加第二个地址 (Permanent Address)  
        if not add_address(driver, person_info, "permanent", selected_phone):
            return False
            
        # 3. 填写电话号码
        if not fill_phone_numbers(driver, selected_phone):
            return False
            
        # 4. 填写个人信息
        if not fill_personal_details(driver, person_info):
            return False
            
        # 5. 点击Continue按钮
        continue_btn = wait_for_element(driver, By.XPATH, "//button[contains(text(), 'Continue')]", 10)
        if not continue_btn:
            return False
            
        if safe_click(driver, continue_btn):
            print("✅ 个人信息表单提交成功")
            time.sleep(3)
            return True
        return False
        
    except Exception as e:
        print(f"❌ 填写个人信息失败: {e}")
        return False

def add_address(driver, person_info, address_type, phone):
    """添加地址信息"""
    print(f"\n📍 添加{address_type}地址...")
    
    try:
        # 点击Add Address按钮
        add_address_btn = wait_for_element(driver, By.XPATH, "//a[contains(@class, 'widget_add') and contains(text(), 'Add Address')]", 10)
        if not add_address_btn:
            return False
            
        if not safe_click(driver, add_address_btn):
            return False
        print("✅ 点击Add Address按钮成功")
        
        # 等待地址弹窗加载
        time.sleep(2)
        
        # 选择地址类型
        if address_type == "mailing":
            address_radio = wait_for_element(driver, By.ID, "form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_1", 8)
        else:  # permanent
            address_radio = wait_for_element(driver, By.ID, "form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_2", 8)
            
        if not address_radio or not safe_click(driver, address_radio):
            return False
        print(f"✅ 选择{address_type}地址类型")
        
        # 选择国家 (United States)
        country_select = Select(wait_for_element(driver, By.ID, "form_3569c020-19d2-41e9-870b-2d81b1812820_country", 5))
        country_select.select_by_value("US")
        print("✅ 选择国家: United States")
        time.sleep(0.2)  # 延迟确保字段处理完成
        
        # 填写街道地址
        street_input = wait_for_element(driver, By.ID, "form_3569c020-19d2-41e9-870b-2d81b1812820_street", 5)
        if not street_input or not safe_fill_input(driver, street_input, person_info.get('address', '123 Main St')):
            return False
        print(f"✅ 填写街道地址: {person_info.get('address', '123 Main St')}")
        time.sleep(0.2)  # 延迟确保字段处理完成
        
        # 填写城市
        city_input = wait_for_element(driver, By.ID, "form_3569c020-19d2-41e9-870b-2d81b1812820_city", 5)
        if not city_input or not safe_fill_input(driver, city_input, person_info.get('city', 'Oklahoma City')):
            return False
        print(f"✅ 填写城市: {person_info.get('city', 'Oklahoma City')}")
        time.sleep(0.2)  # 延迟确保字段处理完成
        
        # 选择州
        state_select = Select(wait_for_element(driver, By.ID, "form_3569c020-19d2-41e9-870b-2d81b1812820_region", 5))
        state_value = person_info.get('state', 'OK')
        state_select.select_by_value(state_value)
        print(f"✅ 选择州: {state_value}")
        time.sleep(0.2)  # 延迟确保字段处理完成
        
        # 填写邮编
        postal_input = wait_for_element(driver, By.ID, "form_3569c020-19d2-41e9-870b-2d81b1812820_postal", 5)
        if not postal_input or not safe_fill_input(driver, postal_input, person_info.get('zipcode', '73107')):
            return False
        print(f"✅ 填写邮编: {person_info.get('zipcode', '73107')}")
        time.sleep(0.2)  # 延迟确保字段处理完成
        
        # 点击Save按钮
        save_btn = wait_for_element(driver, By.XPATH, "//button[contains(text(), 'Save')]", 8)
        if not save_btn or not safe_click(driver, save_btn):
            return False
        print("✅ 点击Save按钮")
        
        # 优化的地址验证弹窗处理 - 根据地址类型使用对应的选择器
        time.sleep(3)  # 等待弹窗出现
        
        if address_type == "mailing":
            # 第一次地址 - 使用选择器1
            selector = "//button[contains(text(), 'Skip Validation')]"
        else:
            # 第二次地址 - 使用选择器5
            selector = "//div[contains(@class, 'dialog_host')]//button[contains(text(), 'Skip Validation')]"
        
        try:
            skip_btn = WebDriverWait(driver, 8).until(
                EC.element_to_be_clickable((By.XPATH, selector))
            )
            if skip_btn:
                driver.execute_script("arguments[0].click();", skip_btn)
                time.sleep(0.3)
                print("✅ 跳过地址验证")
        except TimeoutException:
            # 如果主选择器失败，使用备用选择器
            backup_selectors = [
                "//button[contains(text(), 'Skip Validation')]",
                "//div[contains(@class, 'dialog_host')]//button[contains(text(), 'Skip Validation')]",
                "//div[@class='action']//button[not(contains(@class, 'default'))]"
            ]
            
            validation_handled = False
            for backup_selector in backup_selectors:
                try:
                    skip_btn = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, backup_selector))
                    )
                    if skip_btn:
                        driver.execute_script("arguments[0].click();", skip_btn)
                        time.sleep(0.3)
                        print("✅ 通过备用选择器跳过地址验证")
                        validation_handled = True
                        break
                except TimeoutException:
                    continue
            
            if not validation_handled:
                print("⚠️ 未检测到地址验证弹窗，可能已自动通过")
        
        time.sleep(2)
        return True
        
    except Exception as e:
        print(f"❌ 添加{address_type}地址失败: {e}")
        return False

def fill_phone_numbers(driver, phone):
    """填写电话号码"""
    print(f"\n📞 填写电话号码: {phone}...")
    
    try:
        # 填写Home电话
        home_phone_input = wait_for_element(driver, By.ID, "form_782d4000-f89e-4bbc-b634-e858e7129d39", 10)
        if not home_phone_input or not safe_fill_input(driver, home_phone_input, phone):
            return False
        print(f"✅ 填写Home电话: {phone}")
        
        # 填写Cell电话
        cell_phone_input = wait_for_element(driver, By.ID, "form_9b26e3f3-b9a5-41b6-b182-4075a9796da8", 5)
        if not cell_phone_input or not safe_fill_input(driver, cell_phone_input, phone):
            return False
        print(f"✅ 填写Cell电话: {phone}")
        
        return True
        
    except Exception as e:
        print(f"❌ 填写电话号码失败: {e}")
        return False

def fill_personal_details(driver, person_info):
    """填写个人详细信息"""
    print("\n👤 填写个人详细信息...")
    
    try:
        # 短信联系权限选择 No
        text_permission_select = Select(wait_for_element(driver, By.ID, "form_57e3fec1-4310-409f-b785-f8d54d561ebf", 8))
        text_permission_select.select_by_value("0")  # No
        print("✅ 短信联系权限: No")
        
        # 选择性别
        gender_select = Select(wait_for_element(driver, By.ID, "form_16584d8b-9358-48c1-924d-6c81a75dcd3d", 5))
        gender_value = "M" if person_info.get('gender', '').lower() == 'male' else "F"
        gender_select.select_by_value(gender_value)
        print(f"✅ 选择性别: {gender_value}")
        
        # 选择出生国家 (United States)
        birth_country_select = Select(wait_for_element(driver, By.ID, "form_5c8dc896-17fe-4964-97d8-3d41292ffb40", 5))
        birth_country_select.select_by_value("US")
        print("✅ 选择出生国家: United States")
        time.sleep(0.3)
        
        # 新增：选择出生州（使用库中对应州缩写）
        birth_state_select = Select(wait_for_element(driver, By.ID, "form_661b1fb2-5da5-4d6d-8935-1e88a9f215a1", 8))
        birth_state_value = person_info.get('state', 'OK')
        try:
            birth_state_select.select_by_value(birth_state_value)
        except Exception:
            # 如果库中州不在列表中，回退为Oklahoma
            birth_state_select.select_by_value("OK")
            birth_state_value = "OK"
        print(f"✅ 出生州: {birth_state_value}")
        time.sleep(0.3)
        
        # 公民身份状态
        citizenship_select = Select(wait_for_element(driver, By.ID, "form_3ca01355-cb44-4e4e-ac25-8b9665284bc2", 5))
        citizenship_select.select_by_value("a1ab9237-9968-49ee-84c4-73e584abfc5c")  # I was born in the United States
        print("✅ 选择公民身份状态: 美国出生公民")
        
        # 双重国籍 No
        dual_citizenship_select = Select(wait_for_element(driver, By.ID, "form_f0032eea-5928-4221-af1f-0e1e8ea78efe", 5))
        dual_citizenship_select.select_by_value("0")  # No
        print("✅ 双重国籍: No")
        
        # 填写社会保险号 (去掉连字符)
        ssn = person_info.get('ssn', '123456789').replace('-', '').replace(' ', '')
        ssn_input = wait_for_element(driver, By.ID, "form_dbfafeb1-662e-4081-906a-526729f20163", 5)
        if not ssn_input or not safe_fill_input(driver, ssn_input, ssn):
            return False
        print(f"✅ 填写社会保险号: {ssn}")
        
        # 西班牙裔/拉丁裔 No
        hispanic_select = Select(wait_for_element(driver, By.ID, "form_bf0347dc-27cb-4226-aa7f-71bd269f6307", 5))
        hispanic_select.select_by_value("0")  # No
        print("✅ 西班牙裔/拉丁裔: No")
        
        # 种族选择 White - 提速：等待可点击或遮罩消失，直接JS点击
        print("🔍 正在快速选择种族...")
        white_checkbox = wait_for_element(driver, By.ID, "form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_5", 5)
        if white_checkbox:
            try:
                driver.execute_script("arguments[0].scrollIntoView({block:'center'});", white_checkbox)
                WebDriverWait(driver, 3).until(EC.any_of(
                    EC.element_to_be_clickable((By.ID, "form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_5")),
                    EC.invisibility_of_element_located((By.CLASS_NAME, "progress_dialog_div"))
                ))
            except TimeoutException:
                pass
            try:
                driver.execute_script("arguments[0].click();", white_checkbox)
                time.sleep(0.2)
                print("✅ 选择种族: White")
            except Exception as e:
                print(f"⚠️ 快速选择种族失败: {e}")
         
        return True
        
    except Exception as e:
        print(f"❌ 填写个人详细信息失败: {e}")
        return False

def step10_fill_residency_info(driver):
    """步骤10: 填写俄克拉荷马居住信息"""
    print("\n🏠 步骤10: 填写俄克拉荷马居住信息...")
    
    try:
        # 等待页面加载
        time.sleep(2)
        
        # 1. 是否是俄克拉荷马居民 - 选择Yes
        resident_select = Select(wait_for_element(driver, By.ID, "form_e6a5af0a-c68f-46ba-9f0d-fa170da8fcde", 10))
        resident_select.select_by_value("1")  # Yes
        print("✅ 俄克拉荷马居民: Yes")
        time.sleep(0.3)
        
        # 2. 在俄克拉荷马住了多长时间 - 随机选择1-8年
        residence_time_options = [
            ("f534766c-0b6d-4722-bf7b-c4be2137cbc3", "1 - 2 years"),
            ("018b3251-bbfc-4678-a0c8-4eecf83c9101", "2 - 3 years"),
            ("45bb9b18-5685-4783-8dd8-7d8b86cfc1b1", "3 - 4 years"),
            ("527e9257-b0de-4929-8986-05942bf37e78", "4 - 5 years"),
            ("e673f12f-5129-47f7-b9d6-96fac25eaecd", "5 - 6 years"),
            ("23912f8e-ba51-42a5-aa98-6fce5b0d5e9d", "6 - 7 years"),
            ("5bc8e211-a25b-49ca-b220-064a837f8113", "7 - 8 years")
        ]
        
        selected_time = random.choice(residence_time_options)
        residence_time_select = Select(wait_for_element(driver, By.ID, "form_7667c3fe-4226-4a2a-99ff-20263d2b8c4c", 5))
        residence_time_select.select_by_value(selected_time[0])
        print(f"✅ 居住时长: {selected_time[1]}")
        time.sleep(0.3)
        
        # 3. 搬到俄克拉荷马的主要原因 - 固定选择 "To attend School"
        move_reason_select = Select(wait_for_element(driver, By.ID, "form_0d5050d1-8142-4a91-a24d-c751a8fe198f", 5))
        move_reason_select.select_by_value("d4e08ef0-4685-4b96-bacb-c962d0e7df3d")
        print("✅ 搬家原因: To attend School")
        time.sleep(0.3)
        
        # 4. 当前居住的县 - 随机选择一个可用县
        county_select = Select(wait_for_element(driver, By.ID, "form_f0c4f1e3-b8db-4581-b3b5-da4a36e10bc4", 5))
        # 过滤掉空值选项，随机选择
        valid_options = [opt for opt in county_select.options if opt.get_attribute("value")]  
        selected_opt = random.choice(valid_options)
        county_value = selected_opt.get_attribute("value")
        county_text = selected_opt.text.strip()
        county_select.select_by_value(county_value)
        print(f"✅ 居住县: {county_text}")
        time.sleep(0.3)
        
        # 5. 是否有军事关联 - 选择No
        military_select = Select(wait_for_element(driver, By.ID, "form_5d7d29f0-a5b7-486c-b964-4fd5c3e3aca7", 5))
        military_select.select_by_value("0")  # No
        print("✅ 军事关联: No")
        time.sleep(0.3)
        
        # 6. 是否计划使用VA教育福利 - 选择No
        va_benefits_select = Select(wait_for_element(driver, By.ID, "form_c3333039-6f72-4a06-9bf7-4271cd47863b", 5))
        va_benefits_select.select_by_value("0")  # No
        print("✅ VA教育福利: No")
        time.sleep(0.3)
        
        # 7. 点击Continue按钮
        continue_btn = wait_for_element(driver, By.XPATH, "//button[contains(text(), 'Continue')]", 10)
        if not continue_btn:
            return False
            
        if safe_click(driver, continue_btn):
            print("✅ 居住信息表单提交成功")
            time.sleep(3)
            return True
        return False
        
    except Exception as e:
        print(f"❌ 填写居住信息失败: {e}")
        return False

def step11_fill_education_background(driver):
    """步骤11: 填写教育背景与在读状态"""
    print("\n🎓 步骤11: 填写教育背景与在读状态...")
    try:
        # 等待页面加载到第一个问题
        first_select = wait_for_element(driver, By.ID, "form_9ce31871-86e6-40de-9c61-78a208eb7750", 12)
        if not first_select:
            return False

        # 1) Have you graduated high school? -> 选择 No, I am currently in high school.
        hs_grad_select = Select(first_select)
        hs_grad_select.select_by_value("0e3a79bb-026e-465a-a010-03796d88ae28")
        print("✅ 高中毕业状态: No, I am currently in high school.")
        time.sleep(0.3)

        # 2) Have you ever taken classes at OSU-Oklahoma City? -> 选择 No
        readmit_select = Select(wait_for_element(driver, By.ID, "form_297ce475-09b6-43f0-95c8-904781b0e81f", 8))
        readmit_select.select_by_value("3ee216d1-af15-4af0-bd78-c77663762b25")
        print("✅ 既往就读OSU-OKC: No, I have not previously attended OSU-Oklahoma City")
        time.sleep(0.3)

        # 3) What year have you or will you graduate high school? -> 2025 或 2026 随机
        grad_year_select = Select(wait_for_element(driver, By.ID, "form_1d8ce90c-ca39-4b45-a7cb-ed61438c4396", 8))
        grad_year_options = [
            ("cb53e0a0-40f7-4ad9-8111-07a5f7b24fd2", "2025"),
            ("85fb25c1-6a7c-402e-ac28-6dd81366fd1e", "2026"),
        ]
        selected_year = random.choice(grad_year_options)
        grad_year_select.select_by_value(selected_year[0])
        print(f"✅ 高中毕业年份: {selected_year[1]}")
        set_ctx("hs_grad_year", int(selected_year[1]))
        time.sleep(0.3)

        # 4) Education Goal -> 选择 I DO NOT plan to pursue a certificate or degree
        edu_goal_select = Select(wait_for_element(driver, By.ID, "form_a8910a84-7232-40c2-a0d2-ff4e99ea38d8", 8))
        edu_goal_select.select_by_value("be061155-fe5a-4ed3-ae16-151569ff2d74")
        print("✅ 教育目标: I DO NOT plan to pursue a certificate or degree")
        time.sleep(0.3)

        # 5) Highest education attained -> No College 或 Some College 随机
        highest_edu_select = Select(wait_for_element(driver, By.ID, "form_1a33ccf8-8c79-480a-9578-fcf05d828aa7", 8))
        highest_edu_options = [
            ("88ec4a62-c92b-4ee0-976f-52ac49479f1f", "No College"),
            ("bc00f5bd-bd45-400d-b6ec-48ad765f232c", "Some College"),
        ]
        selected_highest = random.choice(highest_edu_options)
        highest_edu_select.select_by_value(selected_highest[0])
        print(f"✅ 最高学历: {selected_highest[1]}")
        time.sleep(0.3)

        # Continue 按钮
        continue_btn = wait_for_element(driver, By.XPATH, "//button[contains(text(), 'Continue')]", 10)
        if not continue_btn:
            return False
        if safe_click(driver, continue_btn):
            print("✅ 教育与状态页面提交成功")
            time.sleep(3)
            return True
        return False
    except Exception as e:
        print(f"❌ 填写教育背景失败: {e}")
        return False


def step12_fill_academic_history(driver):
    """步骤12: Career Tech选择No并添加一所学校（包含Suggest下拉选择）"""
    print("\n📚 步骤12: 填写Academic History...")
    try:
        # Career Tech 问题 -> No
        career_tech_select = Select(wait_for_element(driver, By.ID, "form_d681951f-77f0-4586-827b-b378130d0d74", 10))
        career_tech_select.select_by_value("0")
        print("✅ Career Tech: No")
        time.sleep(0.3)

        # 点击 Add School 打开弹窗
        add_school_btn = wait_for_element(driver, By.XPATH, "//a[contains(@class,'widget_add') and contains(text(),'Add School')]")
        if not add_school_btn:
            return False
        if not safe_click(driver, add_school_btn):
            return False
        print("✅ 打开Add School弹窗")
        time.sleep(1)

        # 等待 Institution 输入框
        inst_input = wait_for_element(driver, By.ID, "form_a3e6d925-5740-7e51-f8a1-9f4ed538a326", 12)
        if not inst_input:
            return False

        # 输入关键词触发Suggest（随机从候选中选择）
        seeds = ["alv", "alb", "p", "c"]
        seed = random.choice(seeds)
        # 优先使用键盘选择，避免点击丢失
        if choose_autocomplete_with_keyboard(driver, inst_input, seed, 6):
            print("✅ 已通过键盘选择建议学校")
        else:
            # 回退到容器点击方案
            inst_input.clear()
            inst_input.send_keys(seed)
            time.sleep(0.6)  # 等待下拉生成

            # 通过 aria-controls 获取下拉容器id
            controls_id = inst_input.get_attribute("aria-controls") or inst_input.get_attribute("aria-owns")
            suggest_items = []
            if controls_id:
                # 常见结构: div#suggest_xxx .suggest_item
                try:
                    container = wait_for_element_present(driver, By.ID, controls_id, 8)
                    if container:
                        # 优先找 class=suggest_item，其次任何可点击的子项
                        suggest_items = container.find_elements(By.CSS_SELECTOR, ".suggest_item")
                        if not suggest_items:
                            suggest_items = container.find_elements(By.XPATH, ".//*[contains(@class,'suggest') and (self::div or self::li)]")
                        if not suggest_items:
                            suggest_items = container.find_elements(By.XPATH, ".//*[self::div or self::li][@role='option' or contains(@class,'item') or contains(@class,'row')]")
                except Exception:
                    pass

            # 如果还没拿到，退而用全局查找
            if not suggest_items:
                suggest_items = driver.find_elements(By.XPATH, "//*[starts-with(@id,'suggest_')]//*[contains(@class,'suggest_item')]")

            if not suggest_items:
                print("⚠️ 未找到建议列表，尝试回车选择首项")
                inst_input.send_keys("\n")
            else:
                # 从前10个内随机选一个
                pick = random.choice(suggest_items[:min(10, len(suggest_items))])
                driver.execute_script("arguments[0].scrollIntoView({block:'center'});", pick)
                time.sleep(0.2)
                driver.execute_script("arguments[0].click();", pick)
                time.sleep(0.3)
                print("✅ 已选择建议学校（点击方式）")

        # 再次稳健校验：若仍为空，尝试使用通用关键字回退（如 GED / Home Schooled / International High Schools 代码）
        final_val = (inst_input.get_attribute("value") or "").strip()
        if not final_val:
            for fallback in ["GED", "DH0002", "Home Schooled", "DH0004", "International High Schools", "FH0023"]:
                if choose_autocomplete_with_keyboard(driver, inst_input, fallback, 3):
                    print(f"✅ 通过回退关键字选择学校: {fallback}")
                    break
                time.sleep(0.2)
            final_val = (inst_input.get_attribute("value") or "").strip()
            if not final_val:
                print("❌ Institution 仍为空，终止该步")
                return False
        # 记录高中名称到上下文
        set_ctx("high_school_name", final_val)

        # Level of Study -> High School
        los_select = Select(wait_for_element(driver, By.ID, "form_75448f10-0e05-8259-eff2-985b08e2e245", 8))
        los_select.select_by_value("H")
        print("✅ Level of Study: High School")
        time.sleep(0.3)

        # 起始时间：2021/2022年9月
        start_year = random.choice([2021, 2022])
        start_month_select = Select(wait_for_element(driver, By.ID, "form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_m", 8))
        start_month_select.select_by_value("09")
        start_year_select = Select(wait_for_element(driver, By.ID, "form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_y", 8))
        start_year_select.select_by_value(str(start_year))
        print(f"✅ Start Date: 09/{start_year}")
        time.sleep(0.3)

        # 结束时间 = 毕业时间（与前面步骤11的毕业年份一致），月份=06
        grad_year = get_ctx("hs_grad_year") or random.choice([2025, 2026])
        end_month_select = Select(wait_for_element(driver, By.ID, "form_7de0c966-ad67-f20d-f0b0-43eb5ce6b436_m", 8))
        end_month_select.select_by_value("06")
        end_year_select = Select(wait_for_element(driver, By.ID, "form_7de0c966-ad67-f20d-f0b0-43eb5ce6b436_y", 8))
        end_year_select.select_by_value(str(grad_year))
        print(f"✅ End Date: 06/{grad_year}")
        time.sleep(0.3)

        # Graduation Date 同 End Date（06 + 毕业年份）
        conf_m_select = Select(wait_for_element(driver, By.ID, "form_a984087c-3536-82c6-0d10-a07f43c320f1_m", 8))
        conf_m_select.select_by_value("06")
        conf_y_select = Select(wait_for_element(driver, By.ID, "form_a984087c-3536-82c6-0d10-a07f43c320f1_y", 8))
        conf_y_select.select_by_value(str(grad_year))
        print(f"✅ Graduation Date: 06/{grad_year}")
        time.sleep(0.3)

        # Most Recently Attended -> Yes
        recent_yes = wait_for_element(driver, By.ID, "form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce_1", 8)
        if not recent_yes:
            return False
        driver.execute_script("arguments[0].click();", recent_yes)
        time.sleep(0.3)
        print("✅ Most Recently Attended: Yes")

        # Save 弹窗
        save_btn = wait_for_element(driver, By.XPATH, "//div[@role='dialog']//button[contains(text(),'Save')]")
        if not save_btn:
            return False
        if safe_click(driver, save_btn):
            print("✅ 学校记录已保存")
            # 等待弹窗关闭
            try:
                WebDriverWait(driver, 10).until_not(
                    EC.presence_of_element_located((By.XPATH, "//div[@role='dialog' and contains(@aria-label,'Academic History')]"))
                )
            except TimeoutException:
                pass
            time.sleep(0.5)
            # 点击页面的Continue进入下一步
            cont_btn = wait_for_element(driver, By.XPATH, "//button[contains(text(),'Continue')]", 6)
            if cont_btn and safe_click(driver, cont_btn):
                print("✅ Academic History 提交并继续")
                # 不做长时间固定等待，快速返回由下一步骤接管
                return True
            return False

    except Exception as e:
        print(f"❌ 填写Academic History失败: {e}")
        return False

def choose_autocomplete_with_keyboard(driver, input_element, seed_text, max_moves: int = 5) -> bool:
    """在带Suggest的输入框中通过键盘选择一条建议。
    流程：输入seed -> 等待下拉 -> 随机下移1~max_moves次 -> 回车。
    返回是否疑似成功（基于输入值变化或隐藏key字段变化）。"""
    try:
        input_element.clear()
        input_element.send_keys(seed_text)
        time.sleep(0.6)
        input_element.click()
        moves = random.randint(1, max(1, max_moves))
        for _ in range(moves):
            input_element.send_keys(Keys.ARROW_DOWN)
            time.sleep(0.15)
        input_element.send_keys(Keys.ENTER)
        time.sleep(0.3)
        # 成功判定：值发生变化或隐藏key被赋值
        val = input_element.get_attribute("value") or ""
        key_candidates = driver.find_elements(By.XPATH, "//input[contains(@id,'4c9ad284') and @type='text' and @size='8']")
        key_ok = any((kc.get_attribute('value') or '').strip() for kc in key_candidates)
        return (val.strip() and val.strip().lower() != seed_text.lower()) or key_ok
    except Exception:
        return False

def step13_sign_and_confirm(driver, person_info):
    """步骤13: 电子签名并确认提交"""
    print("\n✍️ 步骤13: 电子签名并确认...")
    try:
        # 滚动到页面底部，寻找签名输入框
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(0.5)

        # 填写签名（法定全名）
        full_name = person_info.get('full_name') or f"{person_info.get('firstname','').strip()} {person_info.get('lastname','').strip()}".strip()
        signature_input = wait_for_element(driver, By.XPATH, "//input[@name='signature']", 12)
        if not signature_input:
            # 再尝试基于标签文本定位附近的输入框
            signature_input = wait_for_element(driver, By.XPATH, "//span[contains(text(),'In place of your signature')]/following::input[@name='signature'][1]", 6)
        if not signature_input:
            return False
        if not safe_fill_input(driver, signature_input, full_name):
            return False
        print(f"✅ 已填写签名: {full_name}")
        time.sleep(0.3)

        # 点击 Confirm 按钮
        confirm_btn = wait_for_element(driver, By.XPATH, "//button[contains(text(),'Confirm')]", 12)
        if not confirm_btn:
            # 兼容大小写或空格差异
            confirm_btn = wait_for_element(driver, By.XPATH, "//div[@class='action']//button[contains(translate(normalize-space(text()), 'CONFIRM', 'confirm'),'confirm')]", 5)
        if not confirm_btn:
            return False
        if not safe_click(driver, confirm_btn):
            return False
        print("✅ 签名提交成功，等待下一页...")

        # 简单等待页面切换
        time.sleep(3)
        return True
    except Exception as e:
        print(f"❌ 签名确认失败: {e}")
        return False

def step14_submit_application(driver):
    """步骤14: 提交申请，自动确认浏览器弹窗"""
    print("\n📨 步骤14: 提交申请...")
    try:
        # 滚动到页面底部
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(0.3)

        # 覆盖 confirm 以自动确认
        try:
            driver.execute_script("window.confirm = function(){return true;}")
        except Exception:
            pass

        # 定位提交按钮
        submit_btn = wait_for_element(
            driver,
            By.XPATH,
            "//button[contains(text(),'Submit Application') or (@data-confirm and contains(@onclick,'Postback'))]",
            12,
        )
        if not submit_btn:
            return False

        if not safe_click(driver, submit_btn):
            return False

        # 若仍出现原生alert，尝试接受
        try:
            WebDriverWait(driver, 5).until(EC.alert_is_present())
            alert = driver.switch_to.alert
            alert.accept()
            time.sleep(0.3)
        except Exception:
            pass

        print("✅ 已点击提交，等待结果...")
        # 等待最终状态标题出现：Application Status for ...
        try:
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.XPATH, "//h1[starts-with(normalize-space(.), 'Application Status for ')]"))
            )
            print("🎉 检测到状态标题，注册/提交成功！")
        except TimeoutException:
            print("⚠️ 未在预期时间内检测到状态标题，可能页面加载较慢")
        return True
    except Exception as e:
        print(f"❌ 提交申请失败: {e}")
        return False

def save_registration_record(person_info, email):
    """保存注册成功信息为JSON文件"""
    try:
        os.makedirs(REGISTRATION_OUTPUT_DIR, exist_ok=True)
        birthdate_dict = get_formatted_birthdate()
        birthdate_str = f"{birthdate_dict['month']}/{birthdate_dict['day']}/{birthdate_dict['year']}"
        record = {
            "full_name": (person_info.get('full_name') or f"{person_info.get('firstname','').strip()} {person_info.get('lastname','').strip()}" ).strip(),
            "first_name": person_info.get('firstname', ''),
            "last_name": person_info.get('lastname', ''),
            "email": email,
            "phone": get_ctx("phone", ""),
            "birthdate": birthdate_str,
            "ssn": (person_info.get('ssn', '') or '').replace('-', '').strip(),
            "gender": person_info.get('gender', ''),
            "address": person_info.get('address', ''),
            "city": person_info.get('city', ''),
            "state": person_info.get('state', ''),
            "zipcode": person_info.get('zipcode', ''),
            "high_school_name": get_ctx("high_school_name", ""),
            "hs_grad_year": get_ctx("hs_grad_year"),
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        }
        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{email.replace('@','_at_')}.json"
        path = os.path.join(REGISTRATION_OUTPUT_DIR, filename)
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(record, f, ensure_ascii=False, indent=2)
        print(f"💾 已保存注册信息: {path}")
    except Exception as e:
        print(f"⚠️ 保存注册信息失败: {e}")

# 单次注册完整流程（供线程调用）

def perform_registration(person_info, email, headless: bool) -> bool:
    init_context_for_thread()
    print(f"\n🧵 开始注册: {person_info.get('firstname','')} {person_info.get('lastname','')} | {email}")
    driver = setup_chrome_driver(headless=headless)
    if not driver:
        print("❌ Chrome启动失败")
        return False
    try:
        if not step1_visit_application_page(driver):
            return False
        if not step2_click_create_account(driver):
            return False
        if not step3_fill_registration_form(driver, person_info, email):
            return False
        if not step4_submit_registration(driver):
            return False
        pin_code = step5_get_pin_code(email)
        if not pin_code:
            return False
        if not step6_activate_account(driver, pin_code):
            return False
        if not step7_set_password(driver):
            return False
        if not step8_start_application(driver):
            return False
        if not step9_fill_personal_info(driver, person_info):
            return False
        if not step10_fill_residency_info(driver):
            return False
        if not step11_fill_education_background(driver):
            return False
        if not step12_fill_academic_history(driver):
            return False
        if not step13_sign_and_confirm(driver, person_info):
            return False
        if not step14_submit_application(driver):
            return False
        save_registration_record(person_info, email)
        print("✅ 单次流程完成")
        return True
    except Exception as e:
        print(f"❌ 注册流程异常: {e}")
        return False
    finally:
        try:
            driver.quit()
        except Exception:
            pass

# 交互式入口，支持并发

def main():
    print("🚀 OSU OKC 自动化注册机启动")
    print("=" * 50)
    try:
        # 交互输入
        raw_count = input("请输入注册数量(默认1): ").strip()
        raw_threads = input("请输入并发线程数(默认5): ").strip()
        mode = input("选择浏览器模式 [1] 有头 / [2] 无头 (默认1): ").strip()
        try:
            total = int(raw_count) if raw_count else 1
            if total <= 0:
                total = 1
        except Exception:
            total = 1
        try:
            workers = int(raw_threads) if raw_threads else 5
            if workers <= 0:
                workers = 5
        except Exception:
            workers = 5
        use_headless = True if (mode == '2') else False
        print(f"🧾 计划注册数量: {total} | 并发线程: {workers} | 模式: {'无头' if use_headless else '有头'}")

        # 预分配人员与邮箱，避免并发读写冲突
        jobs = []
        for i in range(total):
            person_info = get_random_person()
            if not person_info:
                print("❌ 无法获取人员信息，跳过")
                continue
            email = generate_email_for_person(person_info)
            if not email:
                print("❌ 无法生成邮箱，跳过")
                continue
            jobs.append((person_info, email))
        if not jobs:
            print("❌ 无可用任务")
            return False

        # 并发执行
        success = 0
        with ThreadPoolExecutor(max_workers=workers) as executor:
            future_to_job = {executor.submit(perform_registration, p, e, use_headless): (p, e) for (p, e) in jobs}
            for future in as_completed(future_to_job):
                ok = False
                try:
                    ok = bool(future.result())
                except Exception as e:
                    print(f"❌ 线程异常: {e}")
                if ok:
                    success += 1
        print(f"\n✅ 完成: {success}/{len(jobs)}")
        return True
    except KeyboardInterrupt:
        print("\n⛔ 已中断")
        return False

if __name__ == "__main__":
    main() 