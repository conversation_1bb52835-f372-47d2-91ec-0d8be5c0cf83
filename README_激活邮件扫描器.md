# OSU OKC 激活邮件扫描器使用说明

## 📋 功能概述

这个激活邮件扫描器专门用于扫描和提取 OSU OKC 账户激活邮件的关键信息，包括：

- 🔗 **激活链接** - 账户激活的完整URL
- 🔢 **临时验证码** - 用于激活的数字验证码
- 📧 **邮件元数据** - 发送方、收件方、主题、时间等信息
- 💾 **数据存储** - 自动保存和管理扫描结果

## 🏗️ 系统架构

```
激活邮件扫描系统
├── activation_email_scanner.py     # 核心扫描器
├── activation_data_manager.py      # 数据管理器
├── scan_activation_emails.py       # 主执行程序
└── activation_emails/              # 数据存储目录
    ├── daily/                      # 按日期存储的邮件数据
    ├── summary/                    # 统计摘要数据
    └── raw/                        # 原始邮件内容
```

## 🚀 快速开始

### 1. 扫描所有激活邮件

```bash
python scan_activation_emails.py --scan-all
```

这会扫描邮箱中的所有邮件，识别并提取OSU OKC激活邮件信息。

### 2. 监控单个邮箱

```bash
python scan_activation_emails.py --monitor "<EMAIL>" --timeout 120
```

监控指定邮箱，等待激活邮件到达（最多等待120秒）。

### 3. 批量监控多个邮箱

```bash
python scan_activation_emails.py --batch-monitor "<EMAIL>,<EMAIL>,<EMAIL>"
```

同时监控多个邮箱的激活邮件。

## 📊 数据管理

### 查看扫描摘要

```bash
python scan_activation_emails.py --summary
```

显示总扫描次数、邮件数量、每日统计等信息。

### 搜索激活邮件

```bash
# 根据验证码搜索
python scan_activation_emails.py --search code "*********"

# 根据激活链接内容搜索
python scan_activation_emails.py --search link "activate"
```

### 导出所有数据

```bash
# 导出到默认文件
python scan_activation_emails.py --export

# 导出到指定文件
python scan_activation_emails.py --export "my_activation_emails.json"
```

## 🔍 扫描原理

### 激活邮件识别规则

系统通过以下规则识别OSU OKC激活邮件：

1. **发送方匹配**：
   - 包含关键词：`oklahoma`, `okstate`, `osu`, `edu`, `okc.admissions`

2. **主题匹配**：
   - 包含关键词：`activate`, `activation`, `account`, `verify`, `verification`

3. **内容匹配**：
   - HTML或文本内容包含：`activate`, `activation`, `temporary code`

### 信息提取模式

#### 激活链接提取
```regex
<a[^>]+href="([^"]*activate[^"]*)"[^>]*>
href="([^"]*activate[^"]*)"
(https://[^"\s]*activate[^"\s]*)
```

#### 临时验证码提取
```regex
temporary code.*?(\d{6,12})
code.*?(\d{6,12})
<strong>(\d{6,12})</strong>
<b>(\d{6,12})</b>
```

## 📁 数据存储格式

### 处理后的邮件数据 (daily/*.json)

```json
{
  "scan_timestamp": "2025-08-13T12:00:00",
  "total_emails": 1,
  "emails": [
    {
      "activation_link": "https://mx.technolutions.net/ss/c/...",
      "temporary_code": "*********",
      "extraction_timestamp": "2025-08-13T12:00:00",
      "email_metadata": {
        "subject": "Activate your OSU application account",
        "from_mail": "<EMAIL>",
        "to_mail": "<EMAIL>",
        "mail_id": "12345",
        "date": "Wed, 13 Aug 2025 02:31:20 +0000"
      },
      "raw_email_file": "raw_email_20250813_120000_1.json"
    }
  ]
}
```

### 统计摘要数据 (summary/activation_emails_summary.json)

```json
{
  "total_scans": 5,
  "total_emails": 12,
  "last_scan": "2025-08-13T12:00:00",
  "daily_stats": {
    "2025-08-13": {
      "scans": 3,
      "emails": 8
    }
  }
}
```

## 🔧 高级使用

### 显示详细信息

```bash
python scan_activation_emails.py --scan-all --show-details
```

显示完整的邮件详细信息，包括所有元数据。

### 自定义超时时间

```bash
python scan_activation_emails.py --monitor "<EMAIL>" --timeout 300
```

设置监控超时时间为300秒（5分钟）。

### 程序集成

可以在你的注册脚本中集成激活邮件扫描功能：

```python
from activation_email_scanner import ActivationEmailScanner
from activation_data_manager import ActivationDataManager

# 创建扫描器和数据管理器
scanner = ActivationEmailScanner()
data_manager = ActivationDataManager()

# 监控特定邮箱的激活邮件
def get_activation_info(email_address, timeout=120):
    activation_info = scanner.scan_single_email(email_address, timeout)
    
    if activation_info:
        # 保存数据
        data_manager.save_activation_emails([activation_info])
        
        # 返回激活链接和验证码
        return {
            'activation_link': activation_info.get('activation_link'),
            'temporary_code': activation_info.get('temporary_code')
        }
    
    return None

# 在注册流程中使用
registered_email = "<EMAIL>"
activation_data = get_activation_info(registered_email)

if activation_data:
    print(f"激活链接: {activation_data['activation_link']}")
    print(f"验证码: {activation_data['temporary_code']}")
```

## 🎯 典型使用场景

### 场景1：批量注册后扫描激活邮件

```bash
# 1. 运行批量注册（你的现有脚本）
python osu_okc_registrar.py

# 2. 扫描所有激活邮件
python scan_activation_emails.py --scan-all --show-details

# 3. 导出结果用于后续处理
python scan_activation_emails.py --export "batch_activation_$(date +%Y%m%d).json"
```

### 场景2：实时监控单个注册

```bash
# 在一个终端运行监控
python scan_activation_emails.py --monitor "<EMAIL>" --timeout 300

# 在另一个终端运行注册
python osu_okc_registrar.py --single-user
```

### 场景3：数据分析和统计

```bash
# 查看历史统计
python scan_activation_emails.py --summary

# 搜索特定验证码
python scan_activation_emails.py --search code "123456789"

# 查找包含特定域名的激活链接
python scan_activation_emails.py --search link "technolutions.net"
```

## ⚠️ 注意事项

1. **邮件API限制**：避免过于频繁的请求，系统已内置适当的延迟
2. **存储空间**：原始邮件内容会占用一定磁盘空间，定期清理旧数据
3. **网络连接**：确保网络连接稳定，系统会自动重试失败的请求
4. **邮箱配置**：确保邮箱API配置正确（target_email和epin）

## 🛠️ 故障排除

### 常见问题

1. **找不到激活邮件**
   - 检查邮件是否已到达邮箱
   - 确认发送方和主题是否符合识别规则
   - 增加监控超时时间

2. **提取不到激活链接或验证码**
   - 检查邮件HTML格式是否有变化
   - 查看原始邮件内容（raw目录下的文件）
   - 可能需要调整提取的正则表达式

3. **数据保存失败**
   - 检查磁盘空间是否足够
   - 确认activation_emails目录权限正确

### 调试模式

修改`activation_email_scanner.py`中的打印输出，获取更详细的调试信息：

```python
# 在is_activation_email方法中添加
print(f"调试信息:")
print(f"  from_email: {from_email}")
print(f"  subject: {subject}")
print(f"  html_content: {html_content[:200]}...")
```

## 📈 性能优化建议

1. **批量处理**：尽量使用批量监控而不是多次单独监控
2. **定期清理**：定期清理老旧的原始邮件文件
3. **索引优化**：对于大量数据，考虑建立索引加速搜索
4. **并发控制**：不要设置过高的并发数，避免被邮件服务限制

---

这个激活邮件扫描器与你的现有注册系统完全兼容，可以无缝集成到你的工作流程中！ 