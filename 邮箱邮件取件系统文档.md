# 邮箱邮件取件系统文档

## 📋 目录

1. [🚀 5分钟快速上手](#5分钟快速上手)
2. [系统概述](#系统概述)
3. [架构设计](#架构设计)
4. [核心模块详解](#核心模块详解)
5. [配置说明](#配置说明)
6. [集成指南](#集成指南)
7. [API参考](#api参考)
8. [故障排除](#故障排除)
9. [扩展开发](#扩展开发)

---

## 🚀 5分钟快速上手

> **📖 新工程师入门指南** - 无需阅读全文档，5分钟内让邮件取件系统运行起来！

### 🚀 快速开始 - 拷贝即用

创建项目文件夹并复制以下文件内容，立即可用：

#### 步骤1：创建项目结构

```bash
mkdir email_verification_demo && cd email_verification_demo
mkdir -p email_system config
touch email_system/__init__.py config/__init__.py
```

#### 步骤2：创建配置文件 `config/email_config.py`

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""邮件系统配置模块"""

CLOUDFLARE_DOMAIN = "caosi.fun"
TEMPMAIL_TARGET_EMAIL = "<EMAIL>"
TEMPMAIL_PLUS_EPIN = ""

TEMPMAIL_LIST_URL = f"https://tempmail.plus/api/mails?email={TEMPMAIL_TARGET_EMAIL}&first_id=0&epin={TEMPMAIL_PLUS_EPIN}"
TEMPMAIL_DETAIL_URL_TEMPLATE = "https://tempmail.plus/api/mails/{mail_id}?email=" + TEMPMAIL_TARGET_EMAIL + "&epin=" + TEMPMAIL_PLUS_EPIN

EMAIL_VERIFICATION_SENDER = "warp"
EMAIL_VERIFICATION_SUBJECT_KEYWORDS = ["sign in", "login", "authentication", "warp", "team"]
EMAIL_POLL_INTERVAL = 1
EMAIL_POLL_TIMEOUT = 60

WARP_EMAIL_PATTERNS = {
    "firebase_auth": [
        r'href="(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^"]*)"',
        r'(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^\s\'"<>]+)',
        r'href="([^"]*firebaseapp\.com[^"]*)"',
        r'(https://[^.\s]*\.firebaseapp\.com[^\s\'"<>]+)',
    ]
}

def get_email_config():
    return {
        'domain': CLOUDFLARE_DOMAIN,
        'target_email': TEMPMAIL_TARGET_EMAIL,
        'epin': TEMPMAIL_PLUS_EPIN,
        'list_url': TEMPMAIL_LIST_URL,
        'detail_url_template': TEMPMAIL_DETAIL_URL_TEMPLATE,
        'sender': EMAIL_VERIFICATION_SENDER,
        'subject_keywords': EMAIL_VERIFICATION_SUBJECT_KEYWORDS,
        'poll_interval': EMAIL_POLL_INTERVAL,
        'poll_timeout': EMAIL_POLL_TIMEOUT,
        'patterns': WARP_EMAIL_PATTERNS
    }
```

#### 步骤3：创建邮箱管理器 `email_system/temp_email_manager.py`

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""临时邮箱管理模块"""

import random
import string
import threading

class TempEmailManager:
    def __init__(self, domain):
        self.domain = domain
        self.used_emails = set()
        self.allocated_emails = set()
        self.email_lock = threading.Lock()

    def generate_temp_email(self, thread_id):
        common_prefixes = [
            "john", "mike", "david", "chris", "alex", "ryan", "kevin", "brian", "jason", "mark",
            "sarah", "lisa", "amy", "jen", "kate", "anna", "emma", "mary", "laura", "susan",
            "user", "student", "test", "demo", "admin", "info", "contact", "support", "mail"
        ]
        
        prefix = random.choice(common_prefixes)
        
        if random.choice([True, False]):
            number_suffix = str(random.randint(1, 999))
            username = f"{prefix}{number_suffix}"
        else:
            extra_chars = ''.join(random.choice(string.ascii_lowercase) for _ in range(random.randint(1, 3)))
            username = f"{prefix}{extra_chars}"

        if len(username) < 6:
            username += ''.join(random.choice(string.ascii_lowercase + string.digits) for _ in range(6 - len(username)))

        return f"{username}@{self.domain}"

    def get_email(self, thread_id):
        with self.email_lock:
            for attempt in range(10):
                email = self.generate_temp_email(thread_id)
                if email not in self.used_emails and email not in self.allocated_emails:
                    self.allocated_emails.add(email)
                    print(f"[{thread_id}] 🎯 生成临时邮箱：{email}")
                    return email
            print(f"[{thread_id}] ❌ 无法生成唯一的邮箱地址")
            return None

    def confirm_email_used(self, email, thread_id):
        with self.email_lock:
            if email in self.allocated_emails:
                self.allocated_emails.remove(email)
                self.used_emails.add(email)
                print(f"[{thread_id}] ✅ 确认邮箱 {email} 使用成功")

    def release_email(self, email, thread_id):
        with self.email_lock:
            if email in self.allocated_emails:
                self.allocated_emails.remove(email)
                print(f"[{thread_id}] 🔄 释放邮箱 {email}")
```

#### 步骤4：创建邮件轮询器 `email_system/email_polling.py`

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""邮件轮询和处理模块"""

import requests
import time
import re
import html
from urllib.parse import unquote

def get_verification_email_tempmail(registered_email_address, expected_sender, subject_keywords, 
                                  poll_interval, poll_timeout, thread_id, 
                                  tempmail_list_url, tempmail_detail_url_template):
    print(f"[{thread_id}] 📧 开始轮询验证邮件...")
    start_time = time.time()
    checked_mail_ids = set()
    poll_count = 0

    while time.time() - start_time < poll_timeout:
        try:
            poll_count += 1
            elapsed_time = int(time.time() - start_time)
            print(f"[{thread_id}] 🔍 第 {poll_count} 次轮询 (已等待 {elapsed_time} 秒)...")
            
            response = requests.get(tempmail_list_url, timeout=15)
            response.raise_for_status()
            mail_data = response.json()
            
            if mail_data.get("result") and mail_data.get("mail_list"):
                print(f"[{thread_id}] 📬 找到 {len(mail_data['mail_list'])} 封邮件")
                for mail_item in mail_data["mail_list"]:
                    mail_id = mail_item.get("mail_id")
                    
                    if mail_id in checked_mail_ids:
                        continue

                    detail_url = tempmail_detail_url_template.format(mail_id=mail_id)
                    detail_response = requests.get(detail_url, timeout=15)
                    detail_response.raise_for_status()
                    email_detail = detail_response.json()
                    checked_mail_ids.add(mail_id)

                    if email_detail.get("result"):
                        original_from_mail = email_detail.get("from_mail", "").lower()
                        original_subject = email_detail.get("subject", "").lower()
                        to_field = email_detail.get("to", "").lower()
                        
                        print(f"[{thread_id}] 📩 检查邮件: {email_detail.get('subject', 'N/A')}")

                        sender_match = expected_sender.lower() in original_from_mail
                        subject_match = any(keyword.lower() in original_subject for keyword in subject_keywords)
                        recipient_match = registered_email_address.lower() in to_field

                        if sender_match and subject_match and recipient_match:
                            print(f"[{thread_id}] ✅ 成功匹配到验证邮件！")
                            return email_detail
            else:
                print(f"[{thread_id}] 📭 邮箱中暂无邮件")

        except Exception as e:
            print(f"[{thread_id}] ⚠️ 轮询错误: {e}")

        time.sleep(poll_interval)

    print(f"[{thread_id}] ⏰ 超时：未找到验证邮件")
    return None

def extract_warp_activation_link_from_email(email_content_html, email_content_text):
    print("🔍 开始提取激活链接...")

    link_patterns = [
        r'href="(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^"]*)"',
        r'(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^\s\'"<>]+)',
        r'href="([^"]*firebaseapp\.com[^"]*)"',
        r'(https://[^.\s]*\.firebaseapp\.com[^\s\'"<>]+)',
    ]

    for content in [email_content_html, email_content_text]:
        if content:
            cleaned_content = unquote(content).replace('\\n', '').replace('\\r', '')
            
            for pattern in link_patterns:
                matches = re.findall(pattern, cleaned_content, re.IGNORECASE)
                if matches:
                    activation_link = html.unescape(matches[0])
                    print(f"✅ 成功提取激活链接: {activation_link}")
                    return activation_link

    print("❌ 未找到激活链接")
    return None

def perform_email_activation_tempmail(email_address, expected_sender, subject_keywords, 
                                    poll_interval, poll_timeout, thread_id,
                                    tempmail_list_url, tempmail_detail_url_template):
    print(f"[{thread_id}] 🚀 开始激活邮箱: {email_address}")

    verification_email_detail = get_verification_email_tempmail(
        email_address, expected_sender, subject_keywords,
        poll_interval, poll_timeout, thread_id,
        tempmail_list_url, tempmail_detail_url_template
    )

    if not verification_email_detail:
        return False, None

    email_html = verification_email_detail.get("html", "")
    email_text = verification_email_detail.get("text", "")
    activation_link = extract_warp_activation_link_from_email(email_html, email_text)

    if activation_link:
        print(f"[{thread_id}] ✅ 邮箱激活链接获取成功！")
        return True, activation_link
    else:
        return False, None
```

#### 步骤5：创建测试程序 `demo.py`

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""5分钟快速上手演示"""

import sys
import os
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.email_config import get_email_config
from email_system.temp_email_manager import TempEmailManager
from email_system.email_polling import perform_email_activation_tempmail

def demo_api_test():
    """API连接测试"""
    print("🔧 API连接测试")
    print("=" * 50)
    
    try:
        import requests
        email_config = get_email_config()
        response = requests.get(email_config['list_url'], timeout=10)
        response.raise_for_status()
        data = response.json()
        
        print("✅ API连接成功！")
        print(f"📊 API响应: result={data.get('result')}, count={data.get('count', 0)}")
        
        if data.get('mail_list'):
            print(f"📬 当前有 {len(data['mail_list'])} 封邮件")
        else:
            print("📭 邮箱为空")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")

def demo_warp_registration():
    """Warp注册完整演示"""
    print("🚀 Warp.dev 注册演示")
    print("=" * 50)
    
    email_config = get_email_config()
    email_manager = TempEmailManager(email_config['domain'])
    
    email_address = email_manager.get_email("WarpDemo")
    if not email_address:
        print("❌ 生成邮箱失败")
        return
    
    print(f"🎯 生成的邮箱: {email_address}")
    print("📝 请访问: https://app.warp.dev/team/NsDNjPIm6cShlRPZa7emfl")
    print(f"📧 使用邮箱: {email_address}")
    
    input("\n✅ 邮箱提交完成后，按回车开始获取验证邮件...")
    
    success, activation_link = perform_email_activation_tempmail(
        email_address, "warp", ["sign in", "login", "authentication", "warp", "team"],
        1, 120, "WarpDemo", email_config['list_url'], email_config['detail_url_template']
    )
    
    if success:
        print("🎉 Warp验证邮件获取成功！")
        print(f"🔗 验证链接: {activation_link}")
        email_manager.confirm_email_used(email_address, "WarpDemo")
        
        with open('warp_demo_result.txt', 'w', encoding='utf-8') as f:
            f.write(f"邮箱: {email_address}\n验证链接: {activation_link}\n时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        print("📄 结果已保存到 warp_demo_result.txt")
    else:
        print("❌ 未获取到验证邮件")
        email_manager.release_email(email_address, "WarpDemo")

if __name__ == "__main__":
    print("🚀 邮件验证系统 - 5分钟快速上手")
    print("\n请选择:")
    print("1. 🧪 API连接测试")
    print("2. 🚀 Warp注册演示")
    
    choice = input("\n请输入 (1-2): ").strip()
    
    if choice == "1":
        demo_api_test()
    elif choice == "2":
        demo_warp_registration()
    else:
        print("❌ 无效选择")
```

#### 步骤6：运行测试

```bash
pip install requests
python demo.py
```

### 🎯 通用业务模板

需要适配其他服务？复制这个模板：

```python
from config.email_config import get_email_config
from email_system.temp_email_manager import TempEmailManager
from email_system.email_polling import perform_email_activation_tempmail

def verify_any_service(sender_keyword, subject_keywords):
    """通用邮件验证模板 - 适配任何服务"""
    config = get_email_config()
    manager = TempEmailManager(config['domain'])
    
    email = manager.get_email("Service")
    print(f"🎯 使用邮箱: {email}")
    
    # 在这里加入你的业务逻辑（提交邮箱到目标服务）
    input("请用邮箱完成注册，然后按回车...")
    
    success, link = perform_email_activation_tempmail(
        email, sender_keyword, subject_keywords,
        1, 60, "Service", config['list_url'], config['detail_url_template']
    )
    
    if success:
        manager.confirm_email_used(email, "Service")
        return {"email": email, "link": link}
    else:
        manager.release_email(email, "Service")
        return None

# 使用示例 - 适配GitHub
result = verify_any_service("github", ["verify", "confirmation"])

# 使用示例 - 适配Discord  
result = verify_any_service("discord", ["verify", "welcome"])
```

### ✅ 成功标志

看到这些输出说明系统正常工作：

```
✅ API连接成功！
🎯 生成临时邮箱：<EMAIL>
📧 开始轮询验证邮件...
✅ 成功匹配到验证邮件！
✅ 成功提取激活链接: https://...
🎉 验证邮件获取成功！
```

**🚀 恭喜！系统已经可以使用了！** 下面是详细的技术文档供深入了解。

---

## 🎯 系统概述

### 系统简介

这是一个基于 **TempMail.plus + Cloudflare Email Routing** 的自动化邮件取件系统，专为自动化注册、验证和登录场景设计。系统具有高度的模块化特性，可以轻松集成到任何需要邮件验证的项目中。

### 核心特性

- 🔄 **自动邮箱生成** - 基于真实域名的临时邮箱
- 📧 **智能邮件轮询** - 支持多种匹配策略的邮件获取
- 🔗 **链接自动提取** - 支持多种正则模式的链接提取
- 🛡️ **线程安全设计** - 支持多线程并发操作
- ⚙️ **配置驱动** - 灵活的配置管理系统
- 🔧 **高度可扩展** - 模块化设计，易于定制

### 技术栈

- **Python 3.7+**
- **requests** - HTTP请求处理
- **threading** - 线程安全管理
- **re** - 正则表达式匹配
- **json** - JSON数据处理

---

## 🏗️ 架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────┐
│                 邮箱邮件取件系统                      │
├─────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                         │
│  ┌─────────────────┐  ┌─────────────────┐          │
│  │  业务逻辑接口   │  │   错误处理接口   │          │
│  └─────────────────┘  └─────────────────┘          │
├─────────────────────────────────────────────────────┤
│  核心层 (Core Layer)                                │
│  ┌─────────────────┐  ┌─────────────────┐          │
│  │ TempEmailManager│  │  EmailPolling   │          │
│  │  - 邮箱生成     │  │  - 邮件轮询     │          │
│  │  - 生命周期管理 │  │  - 链接提取     │          │
│  └─────────────────┘  └─────────────────┘          │
├─────────────────────────────────────────────────────┤
│  配置层 (Configuration Layer)                       │
│  ┌─────────────────┐  ┌─────────────────┐          │
│  │   邮件配置      │  │   匹配规则      │          │
│  │  - API配置      │  │  - 正则模式     │          │
│  │  - 轮询参数     │  │  - 匹配策略     │          │
│  └─────────────────┘  └─────────────────┘          │
├─────────────────────────────────────────────────────┤
│  服务层 (Service Layer)                             │
│  ┌─────────────────┐  ┌─────────────────┐          │
│  │ TempMail.plus   │  │ Cloudflare      │          │
│  │ API Service     │  │ Email Routing   │          │
│  └─────────────────┘  └─────────────────┘          │
└─────────────────────────────────────────────────────┘
```

### 工作流程

```mermaid
graph TB
    A[开始] --> B[生成临时邮箱]
    B --> C[提交邮箱到目标服务]
    C --> D[开始邮件轮询]
    D --> E{收到邮件?}
    E -->|否| F[等待轮询间隔]
    F --> D
    E -->|是| G[验证邮件匹配]
    G --> H{匹配成功?}
    H -->|否| F
    H -->|是| I[提取激活链接]
    I --> J[返回结果]
    J --> K[确认邮箱使用]
    K --> L[结束]
```

---

## 🔧 核心模块详解

### 1. TempEmailManager 模块

#### 功能概述
负责临时邮箱的生成、分配、回收和生命周期管理。

#### 核心特性
- **唯一性保证** - 确保生成的邮箱地址不重复
- **线程安全** - 使用锁机制保证多线程安全
- **智能生成** - 基于真实用户名模式的邮箱生成
- **状态管理** - 跟踪邮箱的分配、使用和释放状态

#### 关键方法

```python
class TempEmailManager:
    def __init__(self, domain):
        """初始化邮箱管理器
        
        Args:
            domain (str): 邮箱域名
        """
        
    def get_email(self, thread_id):
        """获取一个新的临时邮箱
        
        Args:
            thread_id (str): 线程标识符
            
        Returns:
            str: 邮箱地址，失败返回None
        """
        
    def confirm_email_used(self, email, thread_id):
        """确认邮箱使用成功"""
        
    def release_email(self, email, thread_id):
        """释放邮箱（失败时调用）"""
```

### 2. EmailPolling 模块

#### 功能概述
负责邮件的轮询获取、内容解析和链接提取。

#### 核心特性
- **智能轮询** - 支持自定义轮询间隔和超时时间
- **多重匹配** - 支持发件人、主题、收件人多维度匹配
- **链接提取** - 支持多种正则模式的链接提取
- **异常处理** - 完善的网络异常和解析异常处理

#### 关键方法

```python
def get_verification_email_tempmail(registered_email_address, expected_sender, 
                                  subject_keywords, poll_interval, poll_timeout, 
                                  thread_id, tempmail_list_url, 
                                  tempmail_detail_url_template):
    """获取验证邮件
    
    Args:
        registered_email_address (str): 注册邮箱地址
        expected_sender (str): 期望的发件人关键词
        subject_keywords (list): 主题关键词列表
        poll_interval (int): 轮询间隔（秒）
        poll_timeout (int): 超时时间（秒）
        thread_id (str): 线程标识符
        tempmail_list_url (str): TempMail API列表URL
        tempmail_detail_url_template (str): 邮件详情URL模板
        
    Returns:
        dict: 邮件详情，失败返回None
    """

def extract_warp_activation_link_from_email(email_content_html, email_content_text):
    """从邮件内容中提取激活链接
    
    Args:
        email_content_html (str): 邮件HTML内容
        email_content_text (str): 邮件文本内容
        
    Returns:
        str: 激活链接，失败返回None
    """

def perform_email_activation_tempmail(email_address, expected_sender, subject_keywords, 
                                    poll_interval, poll_timeout, thread_id,
                                    tempmail_list_url, tempmail_detail_url_template):
    """执行完整的邮件激活流程
    
    Returns:
        tuple: (成功状态, 激活链接)
    """
```

### 3. EmailConfig 模块

#### 功能概述
统一管理系统的配置参数，包括API配置、轮询参数和匹配规则。

#### 配置结构

```python
# Cloudflare Email Routing + TempMail.plus 配置
CLOUDFLARE_DOMAIN = "caosi.fun"
TEMPMAIL_TARGET_EMAIL = "<EMAIL>"
TEMPMAIL_PLUS_EPIN = ""  # 如果设置了PIN码请填写

# TempMail.plus API配置
TEMPMAIL_LIST_URL = f"https://tempmail.plus/api/mails?email={TEMPMAIL_TARGET_EMAIL}&first_id=0&epin={TEMPMAIL_PLUS_EPIN}"
TEMPMAIL_DETAIL_URL_TEMPLATE = "https://tempmail.plus/api/mails/{mail_id}?email=" + TEMPMAIL_TARGET_EMAIL + "&epin=" + TEMPMAIL_PLUS_EPIN

# Warp邮件验证相关配置
EMAIL_VERIFICATION_SENDER = "warp"  # Warp的发件人域名关键词
EMAIL_VERIFICATION_SUBJECT_KEYWORDS = ["sign in", "login", "authentication", "warp", "team"]
EMAIL_POLL_INTERVAL = 1  # 1秒检查一次
EMAIL_POLL_TIMEOUT = 120  # 2分钟超时

# 邮件内容匹配配置
WARP_EMAIL_PATTERNS = {
    "firebase_auth": [
        r'href="(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^"]*)"',
        r'(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^\s\'"<>]+)',
        r'href="([^"]*firebaseapp\.com[^"]*)"',
        r'(https://[^.\s]*\.firebaseapp\.com[^\s\'"<>]+)',
    ]
}
```

---

## ⚙️ 配置说明

### 基础配置

#### 邮件服务配置

```python
# 1. Cloudflare Email Routing 配置
CLOUDFLARE_DOMAIN = "caosi.fun"  # 实际使用的域名

# 2. TempMail.plus 配置  
TEMPMAIL_TARGET_EMAIL = "<EMAIL>"  # 实际的TempMail接收邮箱
TEMPMAIL_PLUS_EPIN = ""  # PIN码（如果设置了PIN则必填）
```

#### 轮询参数配置

```python
EMAIL_POLL_INTERVAL = 1    # 轮询间隔：1秒检查一次
EMAIL_POLL_TIMEOUT = 120   # 超时时间：2分钟后放弃
```

#### 匹配规则配置

```python
EMAIL_VERIFICATION_SENDER = "warp"  # Warp发件人关键词
EMAIL_VERIFICATION_SUBJECT_KEYWORDS = [
    "sign in", "login", "authentication", "warp", "team"
]
```

### 高级配置

#### 自定义链接提取模式

```python
# 添加新的链接提取模式（基于实际的Firebase认证模式）
CUSTOM_EMAIL_PATTERNS = {
    "warp_firebase": [
        r'href="(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^"]*)"',
        r'(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^\s\'"<>]+)',
        r'href="([^"]*firebaseapp\.com[^"]*)"',
        r'(https://[^.\s]*\.firebaseapp\.com[^\s\'"<>]+)',
    ],
    "your_service": [
        r'href="(https://your-service\.com/verify[^"]*)"',
        r'(https://your-service\.com/verify[^\s\'"<>]+)',
    ]
}
```

#### 邮箱生成策略配置

```python
# 在 TempEmailManager 中自定义邮箱生成策略
common_prefixes = [
    "john", "mike", "david", "chris", "alex", "ryan", "kevin", "brian", "jason", "mark",
    "sarah", "lisa", "amy", "jen", "kate", "anna", "emma", "mary", "laura", "susan",
    "user", "student", "test", "demo", "admin", "info", "contact", "support", "mail"
]
```

---

## 🚀 集成指南

### 快速开始

#### 1. 环境准备

```bash
# 安装依赖
pip install requests

# 创建项目目录结构
your_project/
├── email_system/
│   ├── __init__.py
│   ├── temp_email_manager.py
│   └── email_polling.py
└── config/
    ├── __init__.py
    └── email_config.py
```

#### 2. 复制核心文件

将以下文件复制到你的项目中：
- `email_system/temp_email_manager.py`
- `email_system/email_polling.py`
- `config/email_config.py`

#### 3. 修改配置

```python
# config/email_config.py
CLOUDFLARE_DOMAIN = "caosi.fun"  # 替换为你的域名
TEMPMAIL_TARGET_EMAIL = "<EMAIL>"  # 替换为你的TempMail邮箱
```

#### 4. 基础集成示例

```python
#!/usr/bin/env python3
import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.email_config import get_email_config
from email_system.temp_email_manager import TempEmailManager
from email_system.email_polling import perform_email_activation_tempmail

class EmailVerificationSystem:
    """邮件验证系统示例"""
    
    def __init__(self):
        self.email_config = get_email_config()
        self.email_manager = TempEmailManager(self.email_config['domain'])
    
    def verify_email_workflow(self, thread_id="Main"):
        """完整的邮件验证工作流程"""
        
        # 1. 生成临时邮箱
        email_address = self.email_manager.get_email(thread_id)
        if not email_address:
            return {"success": False, "error": "生成邮箱失败"}
        
        try:
            # 2. 这里插入你的业务逻辑
            # 例如：将邮箱提交到需要验证的服务
            print(f"请在你的业务逻辑中使用邮箱: {email_address}")
            
            # 3. 等待并获取验证邮件
            success, activation_link = perform_email_activation_tempmail(
                email_address,
                self.email_config['sender'],
                self.email_config['subject_keywords'],
                self.email_config['poll_interval'],
                self.email_config['poll_timeout'],
                thread_id,
                self.email_config['list_url'],
                self.email_config['detail_url_template']
            )
            
            if success:
                # 4. 确认邮箱使用成功
                self.email_manager.confirm_email_used(email_address, thread_id)
                return {
                    "success": True,
                    "email": email_address,
                    "activation_link": activation_link
                }
            else:
                raise Exception("获取验证邮件失败")
                
        except Exception as e:
            # 5. 失败时释放邮箱
            self.email_manager.release_email(email_address, thread_id)
            return {
                "success": False,
                "email": email_address,
                "error": str(e)
            }

# 使用示例
if __name__ == "__main__":
    system = EmailVerificationSystem()
    result = system.verify_email_workflow()
    
    if result["success"]:
        print(f"✅ 验证成功: {result['email']}")
        print(f"🔗 激活链接: {result['activation_link']}")
    else:
        print(f"❌ 验证失败: {result['error']}")
```

### 高级集成

#### 多线程并发支持

```python
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class ConcurrentEmailSystem:
    """支持并发的邮件验证系统"""
    
    def __init__(self):
        self.email_config = get_email_config()
        self.email_manager = TempEmailManager(self.email_config['domain'])
        self.file_lock = threading.Lock()
    
    def process_single_verification(self, thread_id):
        """处理单个验证任务"""
        system = EmailVerificationSystem()
        return system.verify_email_workflow(f"Thread-{thread_id}")
    
    def batch_verification(self, num_verifications, max_workers=3):
        """批量邮件验证"""
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(self.process_single_verification, i): i 
                for i in range(num_verifications)
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result['success']:
                        # 线程安全地保存结果
                        with self.file_lock:
                            with open('verification_results.txt', 'a', encoding='utf-8') as f:
                                f.write(f"邮箱: {result['email']}\n")
                                f.write(f"链接: {result['activation_link']}\n")
                                f.write("-" * 50 + "\n")
                                
                except Exception as e:
                    print(f"线程 {index} 异常: {e}")
                    results.append({
                        'success': False,
                        'error': str(e)
                    })
        
        return results

# 使用示例
if __name__ == "__main__":
    concurrent_system = ConcurrentEmailSystem()
    results = concurrent_system.batch_verification(5, max_workers=3)
    
    success_count = sum(1 for r in results if r['success'])
    print(f"总计: {len(results)} 个任务")
    print(f"成功: {success_count} 个")
    print(f"失败: {len(results) - success_count} 个")
```

#### 自定义链接提取

```python
import re
import html
from urllib.parse import unquote

def extract_custom_links(email_html, email_text, patterns):
    """自定义链接提取函数"""
    
    # 检查HTML内容
    if email_html:
        html_cleaned = unquote(email_html).replace('\\n', '').replace('\\r', '')
        for pattern in patterns:
            matches = re.findall(pattern, html_cleaned, re.IGNORECASE)
            if matches:
                link = html.unescape(matches[0])
                return link
    
    # 检查文本内容
    if email_text:
        text_cleaned = email_text.replace('\\n', '').replace('\\r', '')
        for pattern in patterns:
            matches = re.findall(pattern, text_cleaned, re.IGNORECASE)
            if matches:
                return html.unescape(matches[0])
    
    return None

# 使用自定义提取器
class CustomEmailSystem(EmailVerificationSystem):
    """支持自定义链接提取的系统"""
    
    def __init__(self, custom_patterns=None):
        super().__init__()
        self.custom_patterns = custom_patterns or [
            r'href="(https://your-service\.com/verify[^"]*)"',
            r'(https://your-service\.com/verify[^\s\'"<>]+)',
        ]
    
    def extract_verification_link(self, email_detail):
        """使用自定义模式提取验证链接"""
        email_html = email_detail.get("html", "")
        email_text = email_detail.get("text", "")
        
        return extract_custom_links(email_html, email_text, self.custom_patterns)
```

---

## 📚 API参考

### TempEmailManager 类

#### 构造函数

```python
def __init__(self, domain)
```

**参数：**
- `domain` (str): 邮箱域名

**示例：**
```python
manager = TempEmailManager("caosi.fun")
```

#### 方法

##### get_email(thread_id)

获取一个新的临时邮箱地址。

**参数：**
- `thread_id` (str): 线程标识符

**返回值：**
- `str`: 邮箱地址，失败返回 `None`

**示例：**
```python
email = manager.get_email("Thread-1")
if email:
    print(f"获取邮箱: {email}")
```

##### confirm_email_used(email, thread_id)

确认邮箱使用成功。

**参数：**
- `email` (str): 邮箱地址
- `thread_id` (str): 线程标识符

**示例：**
```python
manager.confirm_email_used("<EMAIL>", "Thread-1")
```

##### release_email(email, thread_id)

释放邮箱（失败时调用）。

**参数：**
- `email` (str): 邮箱地址
- `thread_id` (str): 线程标识符

**示例：**
```python
manager.release_email("<EMAIL>", "Thread-1")
```

### EmailPolling 函数

#### get_verification_email_tempmail()

从 TempMail.plus 获取验证邮件。

**参数：**
- `registered_email_address` (str): 注册邮箱地址
- `expected_sender` (str): 期望的发件人关键词
- `subject_keywords` (list): 主题关键词列表
- `poll_interval` (int): 轮询间隔（秒）
- `poll_timeout` (int): 超时时间（秒）
- `thread_id` (str): 线程标识符
- `tempmail_list_url` (str): TempMail API列表URL
- `tempmail_detail_url_template` (str): 邮件详情URL模板

**返回值：**
- `dict`: 邮件详情，失败返回 `None`

**邮件详情结构：**
```python
{
    "mail_id": "12345",
    "from_mail": "<EMAIL>",
    "subject": "Email Verification",
    "to": "<EMAIL>",
    "date": "2024-01-01 12:00:00",
    "html": "<html>...</html>",
    "text": "Plain text content..."
}
```

#### extract_warp_activation_link_from_email()

从邮件内容中提取激活链接。

**参数：**
- `email_content_html` (str): 邮件HTML内容
- `email_content_text` (str): 邮件文本内容

**返回值：**
- `str`: 激活链接，失败返回 `None`

#### perform_email_activation_tempmail()

执行完整的邮件激活流程。

**参数：**
（与 `get_verification_email_tempmail` 相同）

**返回值：**
- `tuple`: `(成功状态, 激活链接)`
  - `成功状态` (bool): `True` 表示成功，`False` 表示失败
  - `激活链接` (str): 提取到的激活链接，失败时为 `None`

**示例：**
```python
success, link = perform_email_activation_tempmail(
    email_address="<EMAIL>",
    expected_sender="warp",
    subject_keywords=["sign in", "login", "authentication", "warp", "team"],
    poll_interval=1,
    poll_timeout=60,
    thread_id="Main",
    tempmail_list_url="https://tempmail.plus/api/mails?email=<EMAIL>&first_id=0&epin=",
    tempmail_detail_url_template="https://tempmail.plus/api/mails/{mail_id}?email=<EMAIL>&epin="
)

if success:
    print(f"激活链接: {link}")
else:
    print("获取激活链接失败")
```

### 配置函数

#### get_email_config()

获取完整的邮件配置字典。

**返回值：**
```python
{
    'domain': 'caosi.fun',
    'target_email': '<EMAIL>',
    'epin': '',
    'list_url': 'https://tempmail.plus/api/mails?email=<EMAIL>&first_id=0&epin=',
    'detail_url_template': 'https://tempmail.plus/api/mails/{mail_id}?email=<EMAIL>&epin=',
    'sender': 'warp',
    'subject_keywords': ['sign in', 'login', 'authentication', 'warp', 'team'],
    'poll_interval': 1,
    'poll_timeout': 120,
    'patterns': {
        'firebase_auth': [
            r'href="(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^"]*)"',
            r'(https://astral-field-294621\.firebaseapp\.com/__/auth/action[^\s\'"<>]+)',
            r'href="([^"]*firebaseapp\.com[^"]*)"',
            r'(https://[^.\s]*\.firebaseapp\.com[^\s\'"<>]+)',
        ]
    }
}
```

---

## 🛠️ 故障排除

### 常见问题

#### 1. 无法获取邮件

**问题描述：** 轮询超时，无法获取到验证邮件

**可能原因：**
- TempMail.plus API响应异常
- Cloudflare Email Routing配置错误
- 邮件匹配规则过于严格

**解决方案：**

```python
# 1. 检查API配置
import requests

def test_tempmail_api(config):
    """测试TempMail API连接"""
    try:
        response = requests.get(config['list_url'], timeout=10)
        response.raise_for_status()
        data = response.json()
        print(f"API测试成功: {data}")
        return True
    except Exception as e:
        print(f"API测试失败: {e}")
        return False

# 2. 放宽匹配条件
config = get_email_config()
config['subject_keywords'] = []  # 移除主题关键词限制
config['sender'] = ""           # 移除发件人限制

# 3. 增加轮询时间
config['poll_timeout'] = 300    # 增加到5分钟
```

#### 2. 邮箱生成重复

**问题描述：** 多线程环境下生成重复邮箱

**解决方案：**

```python
# 增强邮箱生成唯一性
import uuid
import time

class EnhancedTempEmailManager(TempEmailManager):
    def generate_temp_email(self, thread_id):
        """增强的邮箱生成方法"""
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]
        username = f"user_{timestamp}_{unique_id}"
        return f"{username}@{self.domain}"
```

#### 3. 链接提取失败

**问题描述：** 无法从邮件中提取激活链接

**解决方案：**

```python
def debug_email_content(email_detail):
    """调试邮件内容"""
    print("=== 邮件内容调试 ===")
    print(f"发件人: {email_detail.get('from_mail')}")
    print(f"主题: {email_detail.get('subject')}")
    print(f"HTML长度: {len(email_detail.get('html', ''))}")
    print(f"文本长度: {len(email_detail.get('text', ''))}")
    
    # 显示HTML内容前500字符
    html_content = email_detail.get('html', '')
    if html_content:
        print(f"HTML预览: {html_content[:500]}")
    
    # 搜索所有链接
    import re
    all_links = re.findall(r'https?://[^\s\'"<>]+', html_content)
    print(f"找到的所有链接: {all_links}")

# 使用调试函数
email_detail = get_verification_email_tempmail(...)
if email_detail:
    debug_email_content(email_detail)
```

#### 4. 线程安全问题

**问题描述：** 多线程环境下出现竞态条件

**解决方案：**

```python
import threading
from contextlib import contextmanager

class ThreadSafeTempEmailManager(TempEmailManager):
    def __init__(self, domain):
        super().__init__(domain)
        self.email_lock = threading.RLock()  # 使用可重入锁
        
    @contextmanager
    def thread_safe_operation(self):
        """线程安全操作上下文管理器"""
        with self.email_lock:
            yield
    
    def get_email(self, thread_id):
        with self.thread_safe_operation():
            return super().get_email(thread_id)
```

### 性能优化

#### 1. 减少API调用频率

```python
# 使用指数退避策略
import time
import random

def exponential_backoff_poll(base_interval, max_interval, attempt):
    """指数退避轮询策略"""
    interval = min(base_interval * (2 ** attempt), max_interval)
    jitter = random.uniform(0, 0.1) * interval
    return interval + jitter

# 在轮询中使用
attempt = 0
while time.time() - start_time < poll_timeout:
    # ... 轮询逻辑 ...
    
    interval = exponential_backoff_poll(1, 10, attempt)
    time.sleep(interval)
    attempt += 1
```

#### 2. 连接池优化

```python
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

def create_http_session():
    """创建优化的HTTP会话"""
    session = requests.Session()
    
    # 重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    
    # 连接池适配器
    adapter = HTTPAdapter(
        pool_connections=10,
        pool_maxsize=20,
        max_retries=retry_strategy
    )
    
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    return session

# 在邮件轮询中使用
session = create_http_session()
response = session.get(tempmail_list_url, timeout=15)
```

---

## 🔧 扩展开发

### 添加新的邮件服务支持

#### 1. 创建新的邮件轮询器

```python
class CustomEmailPoller:
    """自定义邮件轮询器"""
    
    def __init__(self, api_config):
        self.api_config = api_config
        self.session = create_http_session()
    
    def get_emails(self, email_address):
        """获取邮件列表"""
        url = self.api_config['list_url'].format(email=email_address)
        response = self.session.get(url)
        return response.json()
    
    def get_email_detail(self, email_id):
        """获取邮件详情"""
        url = self.api_config['detail_url'].format(id=email_id)
        response = self.session.get(url)
        return response.json()
    
    def poll_for_email(self, email_address, match_criteria, timeout=120):
        """轮询邮件"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            emails = self.get_emails(email_address)
            
            for email in emails.get('emails', []):
                detail = self.get_email_detail(email['id'])
                
                if self.matches_criteria(detail, match_criteria):
                    return detail
            
            time.sleep(self.api_config.get('poll_interval', 1))
        
        return None
    
    def matches_criteria(self, email_detail, criteria):
        """检查邮件是否匹配条件"""
        sender_match = criteria['sender'] in email_detail.get('from', '').lower()
        subject_match = any(
            keyword in email_detail.get('subject', '').lower() 
            for keyword in criteria['subject_keywords']
        )
        return sender_match and subject_match
```

#### 2. 集成新的邮件服务

```python
class UniversalEmailSystem:
    """通用邮件系统，支持多种邮件服务"""
    
    def __init__(self, service_type="tempmail"):
        self.service_type = service_type
        self.email_manager = TempEmailManager("caosi.fun")
        
        if service_type == "tempmail":
            self.poller = TempMailPoller()
        elif service_type == "custom":
            self.poller = CustomEmailPoller(custom_config)
        else:
            raise ValueError(f"不支持的服务类型: {service_type}")
    
    def verify_email(self, match_criteria):
        """通用邮件验证方法"""
        email_address = self.email_manager.get_email("Main")
        
        try:
            email_detail = self.poller.poll_for_email(
                email_address, 
                match_criteria
            )
            
            if email_detail:
                link = self.extract_link(email_detail)
                self.email_manager.confirm_email_used(email_address, "Main")
                return {"success": True, "link": link}
            else:
                raise Exception("未找到匹配邮件")
                
        except Exception as e:
            self.email_manager.release_email(email_address, "Main")
            return {"success": False, "error": str(e)}
```

### 添加新的链接提取模式

#### 1. 可配置的提取器

```python
class ConfigurableLinkExtractor:
    """可配置的链接提取器"""
    
    def __init__(self, patterns_config):
        self.patterns = patterns_config
    
    def extract_link(self, email_content, pattern_type):
        """根据模式类型提取链接"""
        patterns = self.patterns.get(pattern_type, [])
        
        for content in [email_content.get('html', ''), email_content.get('text', '')]:
            if not content:
                continue
                
            cleaned_content = self.clean_content(content)
            
            for pattern in patterns:
                matches = re.findall(pattern, cleaned_content, re.IGNORECASE)
                if matches:
                    return self.post_process_link(matches[0])
        
        return None
    
    def clean_content(self, content):
        """清理邮件内容"""
        from urllib.parse import unquote
        import html
        
        cleaned = unquote(content)
        cleaned = cleaned.replace('\\n', '').replace('\\r', '')
        cleaned = html.unescape(cleaned)
        return cleaned
    
    def post_process_link(self, link):
        """后处理链接"""
        # 移除可能的尾随字符
        link = link.rstrip('.",;')
        return link
    
    def add_pattern(self, pattern_type, pattern):
        """动态添加新模式"""
        if pattern_type not in self.patterns:
            self.patterns[pattern_type] = []
        self.patterns[pattern_type].append(pattern)
```

#### 2. 使用配置文件管理模式

```python
# patterns.json
{
    "firebase_auth": [
        "href=\"(https://[^.]*\\.firebaseapp\\.com[^\"]*)\""
    ],
    "custom_service": [
        "href=\"(https://your-service\\.com/verify[^\"]*)\""
    ],
    "generic_activation": [
        "(https://[^\\s'\"<>]*activate[^\\s'\"<>]*)",
        "(https://[^\\s'\"<>]*verify[^\\s'\"<>]*)"
    ]
}
```

```python
import json

class PatternManager:
    """模式管理器"""
    
    def __init__(self, patterns_file="patterns.json"):
        self.patterns_file = patterns_file
        self.patterns = self.load_patterns()
    
    def load_patterns(self):
        """加载模式配置"""
        try:
            with open(self.patterns_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def save_patterns(self):
        """保存模式配置"""
        with open(self.patterns_file, 'w', encoding='utf-8') as f:
            json.dump(self.patterns, f, indent=2, ensure_ascii=False)
    
    def add_service_patterns(self, service_name, patterns):
        """添加新服务的模式"""
        self.patterns[service_name] = patterns
        self.save_patterns()
    
    def get_patterns(self, service_name):
        """获取服务的模式"""
        return self.patterns.get(service_name, [])
```

### 监控和日志系统

#### 1. 结构化日志

```python
import logging
import json
from datetime import datetime

class EmailSystemLogger:
    """邮件系统专用日志器"""
    
    def __init__(self, name="EmailSystem"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # 创建处理器
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_email_generated(self, email, thread_id):
        """记录邮箱生成"""
        self.logger.info(f"邮箱生成 - 线程:{thread_id} 邮箱:{email}")
    
    def log_polling_start(self, email, thread_id):
        """记录轮询开始"""
        self.logger.info(f"开始轮询 - 线程:{thread_id} 邮箱:{email}")
    
    def log_email_found(self, email, mail_id, thread_id):
        """记录找到邮件"""
        self.logger.info(f"找到邮件 - 线程:{thread_id} 邮箱:{email} ID:{mail_id}")
    
    def log_link_extracted(self, email, link, thread_id):
        """记录链接提取"""
        self.logger.info(f"提取链接 - 线程:{thread_id} 邮箱:{email} 链接:{link}")
    
    def log_error(self, error, context, thread_id):
        """记录错误"""
        self.logger.error(f"系统错误 - 线程:{thread_id} 上下文:{context} 错误:{error}")
```

#### 2. 性能监控

```python
import time
from functools import wraps

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.stats = {
            'email_generation_time': [],
            'polling_time': [],
            'link_extraction_time': [],
            'total_verification_time': []
        }
    
    def time_it(self, operation_name):
        """装饰器：测量操作耗时"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                
                duration = end_time - start_time
                self.stats[operation_name].append(duration)
                
                print(f"{operation_name} 耗时: {duration:.2f}秒")
                return result
            return wrapper
        return decorator
    
    def get_statistics(self):
        """获取统计信息"""
        stats_summary = {}
        
        for operation, times in self.stats.items():
            if times:
                stats_summary[operation] = {
                    'count': len(times),
                    'total': sum(times),
                    'average': sum(times) / len(times),
                    'min': min(times),
                    'max': max(times)
                }
            else:
                stats_summary[operation] = {'count': 0}
        
        return stats_summary

# 使用监控器
monitor = PerformanceMonitor()

class MonitoredEmailSystem(EmailVerificationSystem):
    """带监控的邮件系统"""
    
    @monitor.time_it('email_generation_time')
    def generate_email(self, thread_id):
        return self.email_manager.get_email(thread_id)
    
    @monitor.time_it('polling_time')
    def poll_email(self, email_address, config, thread_id):
        return perform_email_activation_tempmail(
            email_address, config['sender'], config['subject_keywords'],
            config['poll_interval'], config['poll_timeout'], thread_id,
            config['list_url'], config['detail_url_template']
        )
```

---

## 📄 总结

这个邮箱邮件取件系统是一个功能完整、高度可配置的自动化邮件验证解决方案。通过模块化设计，它可以轻松集成到任何需要邮件验证的项目中。

### 主要优势

- **🔧 高度模块化** - 各组件独立，易于定制和扩展
- **🛡️ 线程安全** - 支持多线程并发操作
- **⚙️ 配置驱动** - 通过配置文件灵活调整行为
- **🎯 智能匹配** - 多维度邮件匹配和链接提取
- **📊 可监控** - 内置日志和性能监控支持

### 适用场景

- 自动化注册系统
- 邮件验证服务
- 账号激活流程
- 登录链接获取
- 批量邮件处理

### 技术特色

- 基于 TempMail.plus + Cloudflare Email Routing
- 支持正则表达式模式匹配
- 指数退避轮询策略
- HTTP连接池优化
- 结构化日志系统

通过本文档，你应该能够成功将这个邮件取件系统集成到你的项目中，并根据具体需求进行定制和扩展。如果在使用过程中遇到问题，请参考故障排除章节或联系开发者获取支持。 

--- 