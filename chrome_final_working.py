#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome有头模式最终工作版本
修复了JavaScript语法错误
"""

import os
import random
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options

def get_chrome_path():
    """自动检测Chrome路径"""
    username = os.getenv('USERNAME', 'Administrator')
    possible_paths = [
        rf"C:\Users\<USER>\AppData\Local\Google\Chrome\Bin\chrome.exe",
        rf"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None

def run_chrome_headed():
    """运行Chrome有头模式"""
    try:
        # 1. 配置代理环境
        os.environ['NO_PROXY'] = 'localhost,127.0.0.1,::1'
        print("✅ 代理环境已配置")
        
        # 2. 检测Chrome路径
        chrome_path = get_chrome_path()
        if not chrome_path:
            print("❌ 未找到Chrome浏览器")
            return False
        print(f"✅ 找到Chrome: {chrome_path}")
        
        # 3. 配置Chrome选项
        chrome_options = Options()
        chrome_options.binary_location = chrome_path
        chrome_options.add_argument('--proxy-bypass-list=localhost,127.0.0.1,::1')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--window-size=1200,800')
        chrome_options.add_argument('--remote-debugging-port=0')
        
        # 禁用自动化检测
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        print("✅ Chrome选项已配置")
        
        # 4. 启动Chrome
        chromedriver_path = os.path.join(os.getcwd(), "chromedriver.exe")
        if not os.path.exists(chromedriver_path):
            print("❌ 当前目录没有chromedriver.exe")
            print("💡 请将chromedriver.exe放到当前目录")
            return False
        
        port = random.randint(9000, 9999)
        service = Service(chromedriver_path, port=port)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("✅ Chrome有头模式启动成功！")
        print(f"🔧 使用端口: {port}")
        
        # 5. 执行反检测脚本（修复语法错误）
        try:
            # 修复：不要用双重花括号
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✅ 反检测脚本执行成功")
        except Exception as e:
            print(f"⚠️ 反检测脚本失败: {e}")
        
        # 6. 设置超时
        driver.implicitly_wait(10)
        driver.set_page_load_timeout(30)
        
        # 7. 访问测试页面
        print("🌐 访问测试页面...")
        driver.get("https://www.baidu.com")
        print(f"✅ 页面标题: {driver.title}")
        
        print("\n🎉 成功！Chrome有头模式运行正常！")
        print("⏱️ 按回车键关闭浏览器...")
        input()
        
        driver.quit()
        print("✅ 浏览器已关闭")
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    print("🚀 Chrome有头模式最终工作版本")
    print("=" * 50)
    print("🎯 修复了JavaScript语法错误")
    print("🔑 基于WarpSeleniumDriver成功逻辑")
    print("=" * 50)
    
    if run_chrome_headed():
        print("\n🎉 恭喜！Chrome有头模式配置成功！")
        print("💡 你现在可以在项目中使用这个配置了")
    else:
        print("\n❌ 配置失败，请检查Chrome和ChromeDriver")

if __name__ == "__main__":
    main() 