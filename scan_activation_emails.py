#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSU OKC 激活邮件扫描主程序
简单易用的激活邮件扫描器
"""

import sys
import time
from activation_email_scanner import ActivationEmailScanner
from activation_data_manager import ActivationDataManager

def main():
    """主函数"""
    print("🚀 OSU OKC 激活邮件扫描器")
    print("=" * 50)
    
    # 创建扫描器和数据管理器
    scanner = ActivationEmailScanner()
    data_manager = ActivationDataManager()
    
    try:
        # 让用户选择扫描数量
        print("\n请选择扫描模式:")
        print("1. 输入数字 - 多线程扫描指定数量的邮件（推荐）")
        print("2. 输入 0 - 持续扫描直到没有新邮件")
        print("3. 输入 -1 - 查看历史数据")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == "-1":
            # 查看历史数据
            data_manager.print_summary()
            return 0
        
        try:
            email_count = int(choice)
        except ValueError:
            print("❌ 请输入有效数字")
            return 1
        
        if email_count < 0:
            print("❌ 请输入0或正数")
            return 1
        
        if email_count == 0:
            # 持续扫描模式
            print(f"\n🔄 启动持续扫描模式（扫描到没有新邮件为止）")
            timeout = input("请输入超时时间（秒，直接回车默认300秒）: ").strip()
            timeout = int(timeout) if timeout else 300
            activation_emails = scanner.scan_continuous_emails(poll_timeout=timeout)
        else:
            # 多线程扫描指定数量邮件
            print(f"\n🚀 启动多线程扫描模式（10个线程并发）")
            activation_emails = scanner.scan_limited_emails_concurrent(email_count)
        
        # 保存结果
        if activation_emails:
            saved_file = data_manager.save_activation_emails(activation_emails)
            if saved_file:
                print(f"\n📄 扫描结果已保存到: {saved_file}")
            
            # 显示结果摘要
            print(f"\n📊 扫描完成！找到 {len(activation_emails)} 封激活邮件:")
            for i, email in enumerate(activation_emails, 1):
                metadata = email.get('email_metadata', {})
                recipient = email.get('recipient_name', 'N/A')
                code = email.get('temporary_code', 'N/A')
                print(f"  {i}. {recipient} - 验证码: {code}")
        else:
            print("\n📭 没有找到激活邮件")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 