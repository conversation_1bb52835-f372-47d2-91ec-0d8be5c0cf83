{"report": {"merge_summary": {"total_registrations": 131, "total_activation_emails": 53, "matched_records": 27, "unmatched_registrations": 104, "unmatched_activation_emails": 26, "duplicate_emails_removed": 0}, "merge_timestamp": "2025-08-14T10:15:41.862447", "data_sources": {"registrations_dir": "registrations", "activation_emails_dir": "activation_emails\\daily"}, "quality_metrics": {"match_rate": 20.610687022900763, "email_coverage": 50.943396226415096, "duplicate_rate": 0.0}}, "merged_records": [{"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Layman", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "005587214", "gender": "female", "address": "4200 Bloomfield Way", "city": "EADS", "state": "CO", "zipcode": "81036", "high_school_name": "p", "hs_grad_year": 2025, "timestamp": "2025-08-11 14:09:59", "registration_file": "20250811_140959_gaillayman207_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.698359", "status": "registration_only", "unique_id": "reg_1_gaillayman207_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "walter<PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "604352724", "gender": "male", "address": "1769 Brown Street", "city": "Oakland", "state": "CA", "zipcode": "94612", "high_school_name": "California City High School", "hs_grad_year": 2025, "timestamp": "2025-08-11 14:25:57", "registration_file": "20250811_142557_walter<PERSON><PERSON>son403_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.699892", "status": "registration_only", "unique_id": "reg_2_walter<PERSON><PERSON><PERSON>403_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "587186010", "gender": "female", "address": "4224 Mcwhorter Road", "city": "West Point", "state": "MS", "zipcode": "39773", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2026, "timestamp": "2025-08-11 14:26:22", "registration_file": "20250811_142622_tracybarrett979_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.701291", "status": "registration_only", "unique_id": "reg_3_tracybarrett979_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "harol<PERSON><PERSON><PERSON><PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "242378118", "gender": "male", "address": "1682 Twin Willow Lane", "city": "Fayetteville", "state": "NC", "zipcode": "28301", "high_school_name": "Alva Senior High School", "hs_grad_year": 2025, "timestamp": "2025-08-11 14:26:45", "registration_file": "20250811_142645_harol<PERSON><PERSON><PERSON>haw695_at_caosi.fun.json"}, "activation_email": {"activation_link": null, "temporary_code": null, "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:38:00.613622", "email_metadata": {"subject": "Your OSU Orange Key Account Is Ready to Activate", "from_mail": "<EMAIL>", "to_mail": "<harold<PERSON><PERSON><PERSON><EMAIL>>", "mail_id": **********, "date": ""}, "raw_email_file": "raw_email_20250813_123807_51.json", "activation_file": "activation_emails_20250813.json", "match_score": 0.****************, "match_details": {"email_match": true, "name_similarity": 0.****************, "compared_emails": "harold<PERSON><EMAIL> <-> harold<PERSON><EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.703274", "status": "matched", "unique_id": "reg_4_ha<PERSON><PERSON><PERSON><PERSON><PERSON>695_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON>er", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "2076 Saint Marys Avenue", "city": "Syracuse", "state": "NY", "zipcode": "13202", "high_school_name": "alv", "hs_grad_year": 2026, "timestamp": "2025-08-11 14:28:38", "registration_file": "20250811_142838_lonniedowner162_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.705533", "status": "registration_only", "unique_id": "reg_5_lonniedowner162_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "421552290", "gender": "female", "address": "3841 Willow Greene Drive", "city": "<PERSON>", "state": "AL", "zipcode": "36352", "high_school_name": "Cesar <PERSON> Public Charter School for Public Policy: Parkside Campus", "hs_grad_year": 2026, "timestamp": "2025-08-11 14:30:25", "registration_file": "20250811_143025_mabeljones621_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.706782", "status": "registration_only", "unique_id": "reg_6_mabel<PERSON><PERSON>621_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "652264188", "gender": "female", "address": "1221 Timberbrook Lane", "city": "ROSSVILLE", "state": "GA", "zipcode": "30741", "high_school_name": "Albany College of Pharmacy and Health Sciences", "hs_grad_year": 2025, "timestamp": "2025-08-11 14:36:14", "registration_file": "20250811_143614_karajackson758_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.708057", "status": "registration_only", "unique_id": "reg_7_ka<PERSON><PERSON>son758_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "Della", "last_name": "Self", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "289425026", "gender": "female", "address": "4904 Sunny Glen Lane", "city": "Cleveland", "state": "OH", "zipcode": "44114", "high_school_name": "alv", "hs_grad_year": 2026, "timestamp": "2025-08-11 14:36:51", "registration_file": "20250811_143651_dellaself992_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.709468", "status": "registration_only", "unique_id": "reg_8_dellaself992_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "602575654", "gender": "male", "address": "1603 Gordon Street", "city": "Ontario", "state": "CA", "zipcode": "91762", "high_school_name": "alb", "hs_grad_year": 2026, "timestamp": "2025-08-11 14:37:41", "registration_file": "20250811_143741_leorutledge215_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.711550", "status": "registration_only", "unique_id": "reg_9_leorutledge215_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "022649032", "gender": "male", "address": "3382 Levy Court", "city": "Boston", "state": "MA", "zipcode": "02110", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2025, "timestamp": "2025-08-11 14:40:34", "registration_file": "20250811_144034_noahcoronel166_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.713123", "status": "registration_only", "unique_id": "reg_10_noahcoronel166_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "086462569", "gender": "male", "address": "1635 Cherry Ridge Drive", "city": "Buffalo", "state": "NY", "zipcode": "14214", "high_school_name": "Albany High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 14:40:54", "registration_file": "20250811_144054_kareemedwards569_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.714854", "status": "registration_only", "unique_id": "reg_11_kareemedwards569_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "081461345", "gender": "male", "address": "3896 Godfrey Road", "city": "New York", "state": "NY", "zipcode": "10038", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2025, "timestamp": "2025-08-11 14:41:01", "registration_file": "20250811_144101_johngalyean966_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.717064", "status": "registration_only", "unique_id": "reg_12_johnga<PERSON>ean966_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "199225551", "gender": "male", "address": "4865 Stone Lane", "city": "Philadelphia", "state": "PA", "zipcode": "19108", "high_school_name": "Alba High School", "hs_grad_year": 2025, "timestamp": "2025-08-11 14:42:43", "registration_file": "20250811_144243_williamfreund561_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.719079", "status": "registration_only", "unique_id": "reg_13_williamfreund561_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Bible", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "467073027", "gender": "male", "address": "741 Werninger Street", "city": "Houston", "state": "TX", "zipcode": "77030", "high_school_name": "alb", "hs_grad_year": 2025, "timestamp": "2025-08-11 14:42:56", "registration_file": "20250811_144256_danielbible730_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.721211", "status": "registration_only", "unique_id": "reg_14_danielbible730_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "christopher<PERSON><PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "306387744", "gender": "male", "address": "2970 Charack Road", "city": "GLENDALE", "state": "CA", "zipcode": "91208", "high_school_name": "California Pacific Charter School of Central California", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:26:17", "registration_file": "20250811_192617_christopherkrause242_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.723038", "status": "registration_only", "unique_id": "reg_15_christopher<PERSON><PERSON>242_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "591806831", "gender": "female", "address": "763 Drainer Avenue", "city": "<PERSON>", "state": "FL", "zipcode": "32347", "high_school_name": "Palisades Park Junior Senior High School", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:26:27", "registration_file": "20250811_192627_eileenmayes492_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.724715", "status": "registration_only", "unique_id": "reg_16_e<PERSON>enmayes492_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "274387097", "gender": "female", "address": "1644 Sunny Glen Lane", "city": "Cleveland", "state": "OH", "zipcode": "44109", "high_school_name": "Alverno College", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:27:03", "registration_file": "20250811_192703_cynthialinch237_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.725922", "status": "registration_only", "unique_id": "reg_17_cynthialinch237_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Bonk", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "501842748", "gender": "female", "address": "4929 Hidden Meadow Drive", "city": "Grand Forks Air Base", "state": "ND", "zipcode": "58228", "high_school_name": "Palisades Park Junior Senior High School", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:28:18", "registration_file": "20250811_192818_peggybonk868_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.726891", "status": "registration_only", "unique_id": "reg_18_peggybonk868_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "397905597", "gender": "male", "address": "382 Benson Street", "city": "<PERSON><PERSON>", "state": "WI", "zipcode": "54914", "high_school_name": "Calvary Bible Christian Academy", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:29:29", "registration_file": "20250811_192929_brianmachin742_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.728042", "status": "registration_only", "unique_id": "reg_19_brianmachin742_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "485254534", "gender": "male", "address": "3249 Centennial Farm Road", "city": "Walnut", "state": "IA", "zipcode": "51577", "high_school_name": "Corning-Painted Post West High School", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:30:46", "registration_file": "20250811_193046_eddieeaton310_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.729838", "status": "registration_only", "unique_id": "reg_20_eddieeaton310_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "Pitman", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "074488192", "gender": "female", "address": "424 Long Street", "city": "New York", "state": "NY", "zipcode": "10004", "high_school_name": "Calvary Bible Christian School", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:31:07", "registration_file": "20250811_193107_kirstenpitman151_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.731153", "status": "registration_only", "unique_id": "reg_21_kirstenpitman151_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "654127389", "gender": "female", "address": "1454 Pooh Bear Lane", "city": "Charlotte", "state": "SC", "zipcode": "28202", "high_school_name": "Argyll Centre Calgary Campus", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:32:36", "registration_file": "20250811_193236_elizabeth<PERSON>bcock409_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.732757", "status": "registration_only", "unique_id": "reg_22_el<PERSON><PERSON><PERSON><PERSON>cock409_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "3559 Richards Avenue", "city": "Riverbank", "state": "CA", "zipcode": "95367", "high_school_name": "p", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:32:56", "registration_file": "20250811_193256_mariobenner446_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.733973", "status": "registration_only", "unique_id": "reg_23_mariobenner446_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "808 Church Street", "city": "PHILADELPHIA", "state": "PA", "zipcode": "19101", "high_school_name": "Alvin Community College", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:33:15", "registration_file": "20250811_193315_eleanoremyers560_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.735682", "status": "registration_only", "unique_id": "reg_24_el<PERSON><PERSON><PERSON><PERSON>560_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "652367515", "gender": "male", "address": "4642 Pick Street", "city": "PORT HOPE", "state": "MI", "zipcode": "48468", "high_school_name": "Center Academy at Pinellas Park", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:34:11", "registration_file": "20250811_193411_justingreene754_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.736921", "status": "registration_only", "unique_id": "reg_25_justing<PERSON>e754_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Love", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "562847628", "gender": "female", "address": "3153 Locust Court", "city": "Long Beach", "state": "CA", "zipcode": "90805", "high_school_name": "alb", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:35:54", "registration_file": "20250811_193554_rachellove996_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.738116", "status": "registration_only", "unique_id": "reg_26_rachellove996_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "Don", "last_name": "<PERSON><PERSON><PERSON>", "email": "don<PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "500081563", "gender": "male", "address": "1028 Chandler Drive", "city": "Springfield", "state": "MO", "zipcode": "65865", "high_school_name": "California Pacific Charter School of Central California", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:36:23", "registration_file": "20250811_193623_don<PERSON>rell168_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.739267", "status": "registration_only", "unique_id": "reg_27_don<PERSON><PERSON>168_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "594979749", "gender": "female", "address": "1937 Willis Avenue", "city": "Palm Coast", "state": "FL", "zipcode": "32037", "high_school_name": "Alverno High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:36:40", "registration_file": "20250811_193640_deannanorton504_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.740815", "status": "registration_only", "unique_id": "reg_28_dean<PERSON>orton504_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "405504580", "gender": "male", "address": "4953 Straford Park", "city": "Winchester", "state": "KY", "zipcode": "40391", "high_school_name": "International School Phnom Penh", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:37:30", "registration_file": "20250811_193730_williamtorres860_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.742361", "status": "registration_only", "unique_id": "reg_29_williamtorres860_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "455330902", "gender": "female", "address": "2943 Romines Mill Road", "city": "Dallas", "state": "TX", "zipcode": "75234", "high_school_name": "Calvary Bible Christian Academy", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:38:00", "registration_file": "20250811_193800_sarahmartin290_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.743637", "status": "registration_only", "unique_id": "reg_30_sarahmartin290_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "258443916", "gender": "male", "address": "1055 Layman Court", "city": "Gainesville", "state": "GA", "zipcode": "30501", "high_school_name": "Albany High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:38:56", "registration_file": "20250811_193856_j<PERSON><PERSON><PERSON><PERSON>on411_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.745133", "status": "registration_only", "unique_id": "reg_31_j<PERSON><PERSON><PERSON><PERSON><PERSON>411_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "561170734", "gender": "female", "address": "3247 Lowndes Hill Park Road", "city": "Bakersfield", "state": "CA", "zipcode": "93301", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:39:06", "registration_file": "20250811_193906_barbarastewart549_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.746440", "status": "registration_only", "unique_id": "reg_32_barbarastewart549_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Glasgow", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "147098399", "gender": "female", "address": "3549 Pooz Street", "city": "Piscataway", "state": "NJ", "zipcode": "08854", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:40:45", "registration_file": "20250811_194045_leslieglasgow233_at_caosi.fun.json"}, "activation_email": {"activation_link": null, "temporary_code": null, "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:38:01.121954", "email_metadata": {"subject": "Your OSU Orange Key Account Is Ready to Activate", "from_mail": "<EMAIL>", "to_mail": "<<EMAIL>>", "mail_id": **********, "date": ""}, "raw_email_file": "raw_email_20250813_123807_53.json", "activation_file": "activation_emails_20250813.json", "match_score": 0.****************, "match_details": {"email_match": true, "name_similarity": 0.6, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.747775", "status": "matched", "unique_id": "reg_33_leslieglasgow233_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Ashton", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "4814 Garrett Street", "city": "Kalamazoo", "state": "MI", "zipcode": "49007", "high_school_name": "Alva Senior High School", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:40:47", "registration_file": "20250811_194047_ryanashton999_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.748945", "status": "registration_only", "unique_id": "reg_34_ryanashton999_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "449469432", "gender": "female", "address": "3637 Whispering Pines Circle", "city": "Plano", "state": "TX", "zipcode": "75074", "high_school_name": "Corning-Painted Post West High School", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:42:15", "registration_file": "20250811_194215_ginamontgomery478_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.750250", "status": "registration_only", "unique_id": "reg_35_ginamontgomery478_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "548155977", "gender": "male", "address": "696 Thunder Road", "city": "PORT ROYAL", "state": "KY", "zipcode": "40058", "high_school_name": "Alva Senior High School", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:51:22", "registration_file": "20250811_195122_cecilmiller948_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.751711", "status": "registration_only", "unique_id": "reg_36_c<PERSON><PERSON><PERSON>er948_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "J<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "217037487", "gender": "female", "address": "5 Hamilton Drive", "city": "Ellicott City", "state": "MD", "zipcode": "21042", "high_school_name": "Calvary Bible Christian Academy", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:51:25", "registration_file": "20250811_195125_reneejara657_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.753351", "status": "registration_only", "unique_id": "reg_37_reneejara657_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "641221726", "gender": "male", "address": "44 Sussex Court", "city": "WASHINGTON", "state": "DC", "zipcode": "20050", "high_school_name": "Alba High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:51:38", "registration_file": "20250811_195138_michaelayala557_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.754931", "status": "registration_only", "unique_id": "reg_38_mi<PERSON><PERSON>yala557_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "520357611", "gender": "female", "address": "3412 Archwood Avenue", "city": "FRUITLAND", "state": "NM", "zipcode": "87416", "high_school_name": "Alvernia University", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:53:00", "registration_file": "20250811_195300_hildawoods989_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.756082", "status": "registration_only", "unique_id": "reg_39_hildawoods989_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "023422613", "gender": "female", "address": "3214 <PERSON>", "city": "ANDES", "state": "NY", "zipcode": "13731", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:53:13", "registration_file": "20250811_195313_lindatruong944_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.757261", "status": "registration_only", "unique_id": "reg_40_lindatruong944_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Ashby", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "402259063", "gender": "female", "address": "2496 North Bend River Road", "city": "CHASE MILLS", "state": "NY", "zipcode": "13621", "high_school_name": "International School Phnom Penh", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:53:30", "registration_file": "20250811_195330_jacquelineashby114_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.758961", "status": "registration_only", "unique_id": "reg_41_j<PERSON><PERSON><PERSON><PERSON>by114_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "540016592", "gender": "male", "address": "3848 Buena Vista Avenue", "city": "<PERSON><PERSON><PERSON>", "state": "OR", "zipcode": "97330", "high_school_name": "California Pacific Charter School of Central California", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:55:01", "registration_file": "20250811_195501_sterlingharris722_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.760487", "status": "registration_only", "unique_id": "reg_42_sterlingharris722_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "552900226", "gender": "female", "address": "2704 Canis Heights Drive", "city": "Los Angeles", "state": "CA", "zipcode": "90026", "high_school_name": "California City High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:55:21", "registration_file": "20250811_195521_teresanguyen887_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.761760", "status": "registration_only", "unique_id": "reg_43_teresanguyen887_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "202807420", "gender": "male", "address": "1901 Stone Lane", "city": "Reading", "state": "PA", "zipcode": "19601", "high_school_name": "Alvernia University", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:56:42", "registration_file": "20250811_195642_dennisrodriguez374_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.763189", "status": "registration_only", "unique_id": "reg_44_dennis<PERSON><PERSON>uez374_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "348501872", "gender": "male", "address": "3118 Johnstown Road", "city": "Schaumburg", "state": "IL", "zipcode": "60173", "high_school_name": "Alba High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:57:13", "registration_file": "20250811_195713_johnjones847_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.764344", "status": "registration_only", "unique_id": "reg_45_john<PERSON><PERSON>847_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "238518556", "gender": "male", "address": "2084 McVaney Road", "city": "ROUNDUP", "state": "MT", "zipcode": "59073", "high_school_name": "California City High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:57:13", "registration_file": "20250811_195713_williammalloy567_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.766293", "status": "registration_only", "unique_id": "reg_46_will<PERSON><PERSON>loy567_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "007568642", "gender": "male", "address": "218 Fantages Way", "city": "New Harbor", "state": "ME", "zipcode": "04554", "high_school_name": "Albany Area Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 19:58:50", "registration_file": "20250811_195850_walterbittner206_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.768421", "status": "registration_only", "unique_id": "reg_47_walter<PERSON><PERSON><PERSON>206_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "Don", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "750058139", "gender": "male", "address": "4826 Indiana Avenue", "city": "Honolulu", "state": "HI", "zipcode": "96814", "high_school_name": "Calvary Bible Christian School", "hs_grad_year": 2025, "timestamp": "2025-08-11 19:59:05", "registration_file": "20250811_195905_doncoon220_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.769508", "status": "registration_only", "unique_id": "reg_48_doncoon220_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON><PERSON>", "first_name": "Eldora", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "564758818", "gender": "female", "address": "2555 Diane Street", "city": "BURGIN", "state": "KY", "zipcode": "40310", "high_school_name": "California Pacific Charter School of Central California", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:00:28", "registration_file": "20250811_200028_eldorastevens886_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.770816", "status": "registration_only", "unique_id": "reg_49_eldorastevens886_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "509033100", "gender": "male", "address": "2448 Roosevelt Road", "city": "MONTICELLO", "state": "MN", "zipcode": "55362", "high_school_name": "Alba High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:00:46", "registration_file": "20250811_200046_franciscurran134_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.772574", "status": "registration_only", "unique_id": "reg_50_franciscurran134_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "529428236", "gender": "female", "address": "4062 Walton Street", "city": "Salt Lake City", "state": "UT", "zipcode": "84116", "high_school_name": "Palisades Park Junior Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:02:26", "registration_file": "20250811_200226_tinanewman159_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.774096", "status": "registration_only", "unique_id": "reg_51_tinanewman159_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "242480236", "gender": "female", "address": "3203 Kooter Lane", "city": "Harrisburg", "state": "NC", "zipcode": "28075", "high_school_name": "Corning-Painted Post West High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:02:50", "registration_file": "20250811_200250_tracynorton736_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.775492", "status": "registration_only", "unique_id": "reg_52_tracy<PERSON>ton736_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "Crystal", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "538173951", "gender": "female", "address": "1506 Pinnickinick Street", "city": "Tigard", "state": "WA", "zipcode": "97223", "high_school_name": "California Pacific Charter School of Central California", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:05:10", "registration_file": "20250811_200510_crystalwatson580_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.776792", "status": "registration_only", "unique_id": "reg_53_crystal<PERSON>son580_at_caosi_fun"}, {"registration_info": {"full_name": "Brittany Lafleur", "first_name": "Brittany", "last_name": "Lafleur", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "489126318", "gender": "female", "address": "178 Briarwood Road", "city": "Springfield", "state": "MO", "zipcode": "65804", "high_school_name": "Alvin Community College", "hs_grad_year": 2025, "timestamp": "2025-08-11 20:05:23", "registration_file": "20250811_200523_brittanylafleur331_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.778360", "status": "registration_only", "unique_id": "reg_54_brittanylafleur331_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "765106134", "gender": "male", "address": "2810 Griffin Street", "city": "Phoenix", "state": "AZ", "zipcode": "85034", "high_school_name": "Alvarado High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:07:01", "registration_file": "20250811_200701_josephwoods790_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.779879", "status": "registration_only", "unique_id": "reg_55_josephwoods790_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "347846300", "gender": "male", "address": "2030 Holden Street", "city": "MATHER", "state": "WI", "zipcode": "54641", "high_school_name": "Albany Area Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:07:05", "registration_file": "20250811_200705_johnmcmahon252_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.781752", "status": "registration_only", "unique_id": "reg_56_johnm<PERSON><PERSON>on252_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "568927820", "gender": "female", "address": "3043 Green Avenue", "city": "San Francisco", "state": "CA", "zipcode": "94107", "high_school_name": "Alvernia University", "hs_grad_year": 2025, "timestamp": "2025-08-11 20:08:39", "registration_file": "20250811_200839_karencooper435_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.783500", "status": "registration_only", "unique_id": "reg_57_karencooper435_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "Louis", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "215130266", "gender": "male", "address": "2096 Marie Street", "city": "Cockeysville", "state": "MD", "zipcode": "21030", "high_school_name": "Albany College of Pharmacy and Health Sciences", "hs_grad_year": 2025, "timestamp": "2025-08-11 20:08:39", "registration_file": "20250811_200839_louisferrara850_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.784832", "status": "registration_only", "unique_id": "reg_58_lo<PERSON><PERSON><PERSON>rara850_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "607068740", "gender": "male", "address": "2381 Brannon Street", "city": "Los Angeles", "state": "CA", "zipcode": "90017", "high_school_name": "Albany Academies", "hs_grad_year": 2025, "timestamp": "2025-08-11 20:10:30", "registration_file": "20250811_201030_trevorsmith694_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.786556", "status": "registration_only", "unique_id": "reg_59_trevorsmith694_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "019700194", "gender": "female", "address": "2412 Cedar Lane", "city": "BOONVILLE", "state": "MO", "zipcode": "65233", "high_school_name": "alv", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:10:33", "registration_file": "20250811_201033_kellirichardson580_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.788698", "status": "registration_only", "unique_id": "reg_60_k<PERSON><PERSON><PERSON>on580_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "4319 American Drive", "city": "Woodbury", "state": "NJ", "zipcode": "08096", "high_school_name": "Alba High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:11:16", "registration_file": "20250811_201116_mariettaberry634_at_caosi.fun.json"}, "activation_email": {"activation_link": null, "temporary_code": null, "recipient_name": "<PERSON><PERSON>", "extraction_timestamp": "2025-08-13T12:38:00.862949", "email_metadata": {"subject": "Your OSU Orange Key Account Is Ready to Activate", "from_mail": "<EMAIL>", "to_mail": "<<EMAIL>>", "mail_id": **********, "date": ""}, "raw_email_file": "raw_email_20250813_123807_52.json", "activation_file": "activation_emails_20250813.json", "match_score": 0.****************, "match_details": {"email_match": true, "name_similarity": 0.****************, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON><PERSON> <-> <PERSON><PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.790215", "status": "matched", "unique_id": "reg_61_mariettaberry634_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "3421 Werninger Street", "city": "NORWALK", "state": "CT", "zipcode": "06860", "high_school_name": "Albany College of Pharmacy and Health Sciences", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:12:17", "registration_file": "20250811_201217_jameswagner996_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.791402", "status": "registration_only", "unique_id": "reg_62_james<PERSON>gner996_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "376882970", "gender": "female", "address": "2947 Railroad Street", "city": "Sault Sainte Marie", "state": "MI", "zipcode": "49783", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:12:17", "registration_file": "20250811_201217_tonyamckinney808_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.792594", "status": "registration_only", "unique_id": "reg_63_ton<PERSON><PERSON><PERSON>808_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "255027635", "gender": "male", "address": "1336 Smith Road", "city": "SALEM", "state": "WV", "zipcode": "26426", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:13:09", "registration_file": "20250811_201309_robertelliston481_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.794335", "status": "registration_only", "unique_id": "reg_64_<PERSON><PERSON><PERSON><PERSON>481_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "660123934", "gender": "male", "address": "2614 Willow Oaks Lane", "city": "Lake Charles", "state": "LA", "zipcode": "70601", "high_school_name": "Argyll Centre Calgary Campus", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:13:54", "registration_file": "20250811_201354_edgarwright223_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.795805", "status": "registration_only", "unique_id": "reg_65_edgar<PERSON>223_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "richard<PERSON><PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "438300137", "gender": "male", "address": "2627 Woodland Avenue", "city": "JONESBORO", "state": "IN", "zipcode": "46938", "high_school_name": "alb", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:14:09", "registration_file": "20250811_201409_richardhartman496_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.797134", "status": "registration_only", "unique_id": "reg_66_<PERSON><PERSON><PERSON><PERSON>496_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Lyon", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "031182756", "gender": "male", "address": "4958 Hampton Meadows", "city": "Marlboro", "state": "MA", "zipcode": "01752", "high_school_name": "c", "hs_grad_year": 2025, "timestamp": "2025-08-11 20:15:44", "registration_file": "20250811_201544_willielyon941_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.798232", "status": "registration_only", "unique_id": "reg_67_willielyon941_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "661071004", "gender": "male", "address": "1372 Wood Street", "city": "<PERSON><PERSON><PERSON>", "state": "LA", "zipcode": "70001", "high_school_name": "alb", "hs_grad_year": 2025, "timestamp": "2025-08-11 20:15:49", "registration_file": "20250811_201549_richardmcdonough561_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.799521", "status": "registration_only", "unique_id": "reg_68_richardmcdonough561_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "305080023", "gender": "female", "address": "2851 Parrill Court", "city": "Bridgeview", "state": "IN", "zipcode": "60455", "high_school_name": "Alverno College", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:16:21", "registration_file": "20250811_201621_eleanorcoulter350_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.801084", "status": "registration_only", "unique_id": "reg_69_eleanor<PERSON>ulter350_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON>rice", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "767263017", "gender": "female", "address": "2849 Chestnut Street", "city": "Saint Petersburg", "state": "FL", "zipcode": "33711", "high_school_name": "Albany High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:17:22", "registration_file": "20250811_201722_patriceswanson923_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.802522", "status": "registration_only", "unique_id": "reg_70_patriceswanson923_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "423073958", "gender": "female", "address": "4601 Franklin Street", "city": "West Point", "state": "AL", "zipcode": "31833", "high_school_name": "Alba High School", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:17:39", "registration_file": "20250811_201739_sharonmcglothlin957_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.803920", "status": "registration_only", "unique_id": "reg_71_sharonmcglothlin957_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "677033633", "gender": "female", "address": "2888 Rosebud Avenue", "city": "<PERSON>", "state": "AR", "zipcode": "72601", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:18:14", "registration_file": "20250811_201814_cherylphillips198_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.805440", "status": "registration_only", "unique_id": "reg_72_chery<PERSON><PERSON><PERSON><PERSON>198_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "255451949", "gender": "female", "address": "1213 Mount Olive Road", "city": "Atlanta", "state": "GA", "zipcode": "30303", "high_school_name": "Cesar <PERSON> Public Charter School for Public Policy: Parkside Campus", "hs_grad_year": 2026, "timestamp": "2025-08-11 20:19:30", "registration_file": "20250811_201930_leonabennett476_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.807137", "status": "registration_only", "unique_id": "reg_73_le<PERSON><PERSON><PERSON>476_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "405281930", "gender": "female", "address": "3291 Counts Lane", "city": "Lexington", "state": "KY", "zipcode": "40507", "high_school_name": "Albany Area Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:05:06", "registration_file": "20250813_100506_nickoleatkins849_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz3G_9d5q2YXUpWv3rS6dO-5bLgXsVcJBOzLar3DKgxFlUMWc0tKgvJzGn0mPFCWcnhgOrQfhkiHK_ynewEAg-nYi-m0TDDUSLIWLnW4SdKfp2U_LUbQUtzudq2LzIEyH9nbEk1kNEMHEOoflGI6MG0Q/4j0/iTWASTKcQMeU4vGY_p8ivw/h0/h001.CzCFFkfAtaUT2vlPALdPuWcEr8Atow36pFmMJvngnUQ", "temporary_code": "*********", "recipient_name": "<PERSON><PERSON>", "extraction_timestamp": "2025-08-13T12:37:59.637081", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:03:04 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_49.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON><PERSON> <-> <PERSON><PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.808449", "status": "matched", "unique_id": "reg_74_nick<PERSON><PERSON>kins849_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "Winona", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "3394 Round Table Drive", "city": "IOWA", "state": "LA", "zipcode": "70647", "high_school_name": "Albany Academies", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:07:16", "registration_file": "20250813_100716_winonaoshea246_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz4-NW6jSEVPZScR0rU8ZbLvalbe5gt3K36yNksB5eKxqGRFq4DbvaApm2U2TOVfcfdWrgVcZ7hjmbYP27aA2SDHGsa4fN-_STZEhu85qma_t5ludLOoEM3xGRBOIb2XsPTebQWikn_nYxiLUkMSE9Xg/4j0/kAoPymGQRJ217M0_m_BlsA/h0/h001.Pe6PumwQPNJOUCfVOIGupVeaO2pQI7ok9fqZG62_27U", "temporary_code": "*********", "recipient_name": "<PERSON><PERSON>", "extraction_timestamp": "2025-08-13T12:37:58.939703", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:05:02 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_43.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON><PERSON> <-> <PERSON><PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.810370", "status": "matched", "unique_id": "reg_75_winona<PERSON>ea246_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "1538 Gordon Street", "city": "San Bernardino", "state": "CA", "zipcode": "92405", "high_school_name": "International School Phnom Penh", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:09:28", "registration_file": "20250813_100928_marthabess293_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszo4H8_yNg1_zP8VhHRWBeC1h3AQ8nQI1c0WygcWMtsoZ7U3-dbWiaomF-rwrUjUmhhjU5s8hCXKwsOM3fYnE__TQGkJAMlNPHqdUj2jf1Czu228wCjt5FjGsjU7-heZRdk2zrrQU6EnmEoVV5i-RAkw/4j0/yOaXUvLiSpWy5NX8DndG-w/h0/h001.Yfclk2wD_xjJlZIt8Oq4293B-zW6S1AIiPH-VPSuvS8", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:57.295413", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:07:06 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_39.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.811505", "status": "matched", "unique_id": "reg_76_marthabess293_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "4916 Scott Street", "city": "Beacon", "state": "NY", "zipcode": "12508", "high_school_name": "c", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:11:33", "registration_file": "20250813_101133_justindillion593_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszyJ0Vj19cUXbt9TV81RGuioprta05ZYEsna2GBRRWqgVLd0wSzxW0JrD-g6kN2d6IvolcbRS9AJkgauSCAblt7iCWcKCLM5cA2rMeiRDNlrvf2B8U4vHpoXWgOmhCu0A3rUaAuxshb4OJEJjGuV7iRQ/4j0/Aw4g3IspQZGYNPw9IKBiIg/h0/h001.SnfjET1A6sqbFpr9FVnio8GbLoh4TqwhzRXr_xsmdQg", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:57.029619", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:09:06 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_37.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.812689", "status": "matched", "unique_id": "reg_77_justindillion593_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "3955 Pike Street", "city": "San Diego", "state": "CA", "zipcode": "92121", "high_school_name": "Alba High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:12:05", "registration_file": "20250813_101205_jamescoleman489_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszkO9WtaBIzmaCUGGR0kL_K2AyO9kqBZb5brWnYA359FjFbhEufwVZiG2FkrCqoUnicj2RCjceFw7CSVaAd-i4c3GXLoxMJaPbb_Fb9s_8FLaNh54yqDhIWS4bIi-xmgR81PemRmnuxaEiXr7ue6VybQ/4j0/3AwmvNb4R6uqFX_q72bQgQ/h0/h001.C0Hq0hTi1LO-k0mZIFKMkoZ4ZLP0Gpt8K7VPVZ5q_X4", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:56.100026", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:09:52 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_35.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.813823", "status": "matched", "unique_id": "reg_78_james<PERSON><PERSON>an489_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Livingston", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "2085 Hilltop Drive", "city": "Memphis", "state": "TX", "zipcode": "79245", "high_school_name": "Alva Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:14:16", "registration_file": "20250813_101416_careylivingston704_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszrP9Q6IEjllL6fQMlSlt5H1805d6Ucaj_xGhNfKWUGm84BUAdi_VpLCUzI_FZv3_gf3Xl5akPs-nie-yp_B6XbxVzRNyI0JkiCiUIyXEicc9xX3VIF9RwBzBf1ZOAM0h2PQeyS43Vk2-r8dT_V1F23w/4j0/Fydo56U_RnW9Oy-ePSrn7A/h0/h001.NE-uu5E11NBcS66hXZqoavmEsoopainC4-uZNsxJg-g", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:54.968878", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:12:18 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_30.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.815760", "status": "matched", "unique_id": "reg_79_careylivingston704_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "2125 Norman Street", "city": "Los Angeles", "state": "CA", "zipcode": "90042", "high_school_name": "c", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:16:22", "registration_file": "20250813_101622_larryvasquez613_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszYLmwOm5upqubQVyAj0mSaoLfJ38piMwkZTZ2Mhj0Ldub9yllGdAZfupFZcz3KH2SabFxPW_FWIp8t8dhhUoqa9gIWtZ07YauUnYEOzpqhXo_ZfyI7ehRHVrceOgjRNCWhwJTUqJbYV0tYi4rREtJrg/4j0/kCUCEhImQy2LSWyZonK7HA/h0/h001.asGY_VGsBf5hYNQgEbl1b4Ar0R23xisaoAXUuFcq-Hs", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:54.944007", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:14:43 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_29.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.817121", "status": "matched", "unique_id": "reg_80_larry<PERSON><PERSON>613_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "2245 Sussex Court", "city": "Mexia", "state": "TX", "zipcode": "76667", "high_school_name": "Alvarado High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:19:57", "registration_file": "20250813_101957_patriciabosse436_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz995-XAsEdStbLn65rJiMxoS6n1EHIi6JlAY-oj72xj9zEKKFqqqpun3mIu-eBv8Mrk950BcHB5fTPd3EY2BDwxEg5Cj3hCKCgUwCcJo_cc9MtIL7-Ipt3vDzoRLQ8I5d6lz7ZyDY-pWht2I5sG-tFQ/4j0/a5zF75FATaitpmEv7g7ryA/h0/h001.RbBVrdwNxAcW-dsJP95VmwWuyH05mtCknQaVfrYyjEA", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:52.907844", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:17:54 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_20.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.818377", "status": "matched", "unique_id": "reg_81_patric<PERSON><PERSON>se436_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Calderon", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "1602 Thrash Trail", "city": "Longview", "state": "TX", "zipcode": "75601", "high_school_name": "Calvary Bible Christian Academy", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:20:00", "registration_file": "20250813_102000_carlcalderon593_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszfAfDtsL2fBbwVXQ2INxHa6eDg97IoHoNcOmEulwzb28qto93kkjHaX9R_wLgWfQCGmUjdL2E63s35N_ZQoZslnGPra5yAciIJB35oxA62a3oWUo4IDN1vpQN61LhaYHb89EPYrrGZrXicdYfhuAYdA/4j0/vvLDsE-aRj6c3xdciwO1KQ/h0/h001.X4l8U4z9aG9hF1ih7DwJQY55wfwycv7Q_D-zu5biyoQ", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:52.925492", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:17:49 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_21.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.819558", "status": "matched", "unique_id": "reg_82_carlcalderon593_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "henry<PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "3860 Jehovah Drive", "city": "Winchester", "state": "VA", "zipcode": "22601", "high_school_name": "Corning-Painted Post West High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:20:49", "registration_file": "20250813_102049_henrymobley893_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsztAqkazKipRAPuCbgU9F0unJ41S95LmsNG6EgXGwkD0aNG6tL14qg_Stnknm7v1YFJi2fOWj9k5UdlfDW_K-hs-C51ljg0nyOg-vbU9uHeqm925BigoEQ9oS4outPAQLLdfS6-MZV1U3brNXylhfoHQ/4j0/RB8OpgxgQfeirfdU-pfGaw/h0/h001.djFKQzlRvoM-Z2oHMypMoWoQMRdDa9rY5Xuc2nIby3Q", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:52.023692", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "henry<PERSON><EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:18:54 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_19.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.820628", "status": "matched", "unique_id": "reg_83_hen<PERSON><PERSON><PERSON>893_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "4209 Lady Bug Drive", "city": "Queens", "state": "NY", "zipcode": "11693", "high_school_name": "c", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:22:54", "registration_file": "20250813_102254_beckiphillips826_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszcluD_2xWQJo6Rm1EPmfSgeqOmaTkWoG4i0UlORdaK3B6cz0SJWvRV-6etm8NHU9jtcXZRtmx8ow7TBjM_hhCh2fPBa6iCu43abo6NyF3A57a7xBkWzxij-ItrdivYTHSPHKTt84B6n0Y1l4nIFkUYg/4j0/p8RhOntDSim0nDttXW3Tlw/h0/h001.ajnCn73vs3KIHVZ5H2MfoaF2kFXcOe_KVI3PBFCOQjs", "temporary_code": "*********", "recipient_name": "<PERSON><PERSON>", "extraction_timestamp": "2025-08-13T12:37:51.230309", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:21:06 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_18.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON><PERSON> <-> <PERSON><PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.822158", "status": "matched", "unique_id": "reg_84_becki<PERSON><PERSON>ps826_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "mary<PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "3011 Burnside Court", "city": "Phoenix", "state": "AZ", "zipcode": "85008", "high_school_name": "c", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:22:58", "registration_file": "20250813_102258_mary<PERSON>linger994_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszbC_bAgP3kZfRJmPrde5TaPk-KiVGhGmf5WkhuRy5mOS1H0ypA0EJB7dH-ukdWtFA-bO0kEQbDrfzXtecZuG63Ip7KVUvjVL_WiJuLp_EK88HXwN1cCwGZjnWN1Wuhsz8Waik81ZrqXwpvvnE3xjrvg/4j0/g5enD_rXSWueqiQrZL8f2g/h0/h001.lxRmfuO1sz5b8sewpx4xPGMeZTWV5-aKJPrO7p2wBGE", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:50.957397", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "mary<PERSON><EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:20:25 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_15.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.823914", "status": "matched", "unique_id": "reg_85_mary<PERSON>linger994_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "timo<PERSON><PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "2423 Limer Street", "city": "Greensboro", "state": "GA", "zipcode": "30642", "high_school_name": "Palisades Park Junior Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:25:09", "registration_file": "20250813_102509_timothyhiguchi491_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszkXoc2zH5b6GxT_4zurdGmwSbfoUTCPiKpUyyGBZUTRgeG75iDv2MN5Q0Snye4EiOuKQgKwj1nF4_EO6ubXdM5mKjvT7YFLyQ3XzRKJiMken1jodCL713rsyu2YaUnTstPMTAukcEGixf_mL3ptjolg/4j0/C1ADEDqjT9q38DAfQ13fDg/h0/h001.qPV4h51jvGjv_3DjZMi38yl3nghF53PzmWCMow2rO1U", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:49.012893", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "timo<PERSON><PERSON><EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:23:11 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_12.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.824897", "status": "matched", "unique_id": "reg_86_timo<PERSON><PERSON><PERSON>491_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Card", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "761 Memory Lane", "city": "Rockcut", "state": "IL", "zipcode": "61103", "high_school_name": "Alva Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:25:13", "registration_file": "20250813_102513_davidcard497_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszBtLxgBylzNGMoTsdoIdWGZjkU0ZNTdvJm8BbQ7qh3JNyux0yyEmy0ON_U4iHXDzm7xzFhz_gGz_-W_DmMAiL1wT6eeyohQIWIvV9ByAPLsL-G0PTDfqCOJRZTqpLU3aLjY9Thm2WjF-C-uZAIkTMOA/4j0/2z7MS8AoRvKa9qDLCxPEJQ/h0/h001.G8r6OTkRn0izV4W-9kMAKOorcAhwoG8QlvAivlc3-Ms", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:49.760435", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:23:01 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_14.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.825681", "status": "matched", "unique_id": "reg_87_davidcard497_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Hadfield", "email": "jennifer<PERSON><PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "4870 Quiet Valley Lane", "city": "<PERSON><PERSON>", "state": "CA", "zipcode": "91214", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:26:17", "registration_file": "20250813_102617_jennifer<PERSON>field471_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszF6pw3qsY4rQWV0ZU-aqZGuLN5DY9sW94BdFhXW5-B5Vy_9Q_9Pl3ZPWdD8Gmdq3UozzD-hCCeh5DJEPnTZYolVrhkYhhwBDuhDTNqLFLV-u2jfKqlbfiLC_wPDYM3d_f2mI-QwCyzVW_gzsAheDCHg/4j0/0uP_JYtcTjKV6tXfltfiRg/h0/h001.0111oG9Rs219m1LvV6XGUeBmnv5QiZUZ3bvZI3yV0Gk", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:48.955224", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "jennifer<PERSON><PERSON><EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:24:32 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_11.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.826664", "status": "matched", "unique_id": "reg_88_jen<PERSON><PERSON><PERSON><PERSON>471_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "jimm<PERSON><PERSON><PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "1639 Clark Street", "city": "Bay Shore", "state": "NY", "zipcode": "11706", "high_school_name": "Albany High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:26:54", "registration_file": "20250813_102654_ji<PERSON><PERSON><PERSON><PERSON>378_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszPlucUDH1q95AdSv6Dwal4eHI5fZftwPYG0dbeQqHYbw71wQybBFuEuNFgVEDLeabWi3P3V78KwzzO5uoLEfygMa6PrIc7MsS5BOY2HcmcEW5PKYT35msnIQwahrJJaNmuixAJK6q6f3X-ZcmZ5YotA/4j0/X1wqX74nS_S0nIsvwiY6tg/h0/h001.oKkpCw4uIkduIUvQ4Up89-cQ8tg2WeH5TlG-IH2NjR8", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:49.236712", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "jimm<PERSON><PERSON><PERSON><EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:25:22 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_13.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.827562", "status": "matched", "unique_id": "reg_89_jimm<PERSON><PERSON><PERSON>378_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON>nie", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "1432 Hillcrest Lane", "city": "Elsinore", "state": "CA", "zipcode": "92330", "high_school_name": "Albany Academies", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:27:18", "registration_file": "20250813_102718_minniearnold279_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz3sCJtvjIOMqJbbJcDKibHt_xm8OCppjg_rK2pW_0BQLwqOlBw0-P9jUBOJwOSp_m4x9EtNNk66j4L35-U_uwrDidlHdo-A31Y2icrBya6_ftOefcsUyn2x141vimJb9QG3nZxKO8mUV4BXFcmH8Uyw/4j0/enQiyOlHT82zxyR3kCws5A/h0/h001.EW3N35j8ouTXsATEvZED74cf6Bh4g4gIrOh_oRJWsGk", "temporary_code": "*********", "recipient_name": "<PERSON><PERSON>", "extraction_timestamp": "2025-08-13T12:37:48.732941", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:25:31 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_9.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON><PERSON> <-> <PERSON><PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.828611", "status": "matched", "unique_id": "reg_90_minniearnold279_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "3405 Stiles Street", "city": "Pittsburgh", "state": "PA", "zipcode": "15201", "high_school_name": "Alba Golden High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:28:07", "registration_file": "20250813_102807_mandyangelo948_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszRpdU-MgJpw-YZAyxY28WA8CYJ-UDSIiaYlgOz_GVQYq537Ym1pRe0-Xeb88b7U4t_V0Miegoj1UWg0tJrmjg7m0SBXLjLCWy-nbF5-T6OZ9eMbAyOXI1QOvtF65eRDh0Ue_e5SH5CfYGh_DKuqbigA/4j0/em6aSHSMT1uPQm6wsBIonQ/h0/h001.fSkcoW4RoOZcCqdJVSL70Sf03NjNf-ZdoGlYNuiQycw", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:48.730353", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:26:33 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_8.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.829649", "status": "matched", "unique_id": "reg_91_mandyangelo948_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "robert<PERSON><EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "680 Hidden Pond Road", "city": "<PERSON>", "state": "TN", "zipcode": "37067", "high_school_name": "Calvary Bible Christian School", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:29:19", "registration_file": "20250813_102919_robertboatright881_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszFIgXoZiOgEF5H49Oo-676vWge4Srp9Cr9E5sxu9a6umxGDHUPFq29LxOxYcxrzO7fihHwP4a7mJHjZCv04oieXMIHiLUuFvsRUz1dPDykuLOZa8PcrJk64lq3rpCdmQbHTGuKMMbVAo0XxLEc0J5zQ/4j0/B2tDcV52QemwbPlBkd1v1A/h0/h001.MIENm19HR-XpwSmKeEXb0ZoQFeIWGYU6fQN2H_Y_XwM", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:46.421611", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "robert<PERSON><EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:27:06 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_4.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.830510", "status": "matched", "unique_id": "reg_92_robert<PERSON>right881_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "4270 Brighton Circle Road", "city": "MINOCQUA", "state": "WI", "zipcode": "54548", "high_school_name": "Alba Golden High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:29:47", "registration_file": "20250813_102947_nicholashumphreys439_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszZH9aiQCOxgbnYeSUG8tjL04WfjKtN7YVvtSDMYTkkVtr8XkmmiX_b908XfhKQr0UR02fHpUpF9HdD3G4bToWTngilSl_UIX8yY8j8VVfMXNl3Bk2JDU4Sr4UrD8XQ2VFmJh63b80Wdu0IT86Pa6btw/4j0/5oKOooQpTtigPijsvD0TGQ/h0/h001.5TGPPhcV_nXWeg7V4cFAZ9fYzHP7FgsyllYvxyzyRyU", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:46.439236", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:28:20 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_6.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.831410", "status": "matched", "unique_id": "reg_93_nicho<PERSON><PERSON><PERSON>eys439_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "Chester", "last_name": "Lovely", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "1192 Johnstown Road", "city": "Wheeling", "state": "IL", "zipcode": "60090", "high_school_name": "Alvernia University", "hs_grad_year": 2025, "timestamp": "2025-08-13 10:31:06", "registration_file": "20250813_103106_chesterlovely823_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszqZk1lfy2OO_YCua0zWsOnK2Dl-6v5deHmbEELMlbNlF1qf4Ugq_puMqRPijrZ5jL1JJBeVgwIuxOw1ynuk5fBMH5qyihYYYeV_bFJGlue5pO9Aci9zHfeLXXvwVFfX3gvLTJ6CNMw3Bt1y2PIINFhw/4j0/21U1KA0RQrSwarYNbhKOUw/h0/h001.80vDBHKMNNZD6533pS7n5uM5OuUdlHrA-itAl-hhaTI", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:46.146830", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:29:39 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_2.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.832598", "status": "matched", "unique_id": "reg_94_chester<PERSON>ly823_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "female", "address": "4110 Cambridge Place", "city": "COBB ISLAND", "state": "MD", "zipcode": "20625", "high_school_name": "Palisades Park Junior Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:31:20", "registration_file": "20250813_103120_elsiebrown932_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszpSs1W_CgPE2uqiFXx13YO3lUUAmtVxdFwTuB2v6D-M0udpxFBZ9J0jTCgtAj-czE2K9g-prYLZXv0vxs4TddcttGfY6MC1BZtcFmKAdX6V7HJP2ut6wmizxuM8m9h9nPJ4W715V_3S9I0jKw0be5Yw/4j0/oxtzwHbuQ9GNmaB1tvsQXg/h0/h001.6n_vs-sH3douRsAAt0zZyu0wOOIZCKPcFPOF3kWRw5o", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:46.149568", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:29:31 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_3.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.833359", "status": "matched", "unique_id": "reg_95_elsiebrown932_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Rhodes", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "2349 Diamond Street", "city": "Charlotte", "state": "NC", "zipcode": "28263", "high_school_name": "Albany High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:31:32", "registration_file": "20250813_103132_richardrhodes228_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz8jtfaHy67XazT8xREokW4XNM9dLMRcM5ODGGDqm_bDDlBIUWr8H72VRGFi-RMyLkKSQ0fxMuEVSgrJoy-F36UHXPZrUK26uwykbITpUiMSmFICRdEBE1hh0lCdIGUf7qXNcYKR2vSXo7s5R7URpOiQ/4j0/aS8baoDARKqd5pOY2oXFbg/h0/h001.Vrad56IcwoVooNfu7Af-mvbkAHkQkI40ALJnYZbMk6I", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:46.427632", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:30:06 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_5.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.834250", "status": "matched", "unique_id": "reg_96_richardrhodes228_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "962 <PERSON>", "city": "Elk City", "state": "OK", "zipcode": "73644", "high_school_name": "Alva Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 10:33:14", "registration_file": "20250813_103314_charlestatman607_at_caosi.fun.json"}, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszg5rsHLc7GusCOb-XgMOF_2MxqmiNfUds5Nvp4KQFVAI2Heg8CtJnEcp9wkg7DzT8HBNSRdP9P3TvB4zdLRLX-KAx0RKWpzzqnYLcIrqSGfKhyC4fuCJEo36JWFyZnivSpQJx3C9T4Sp_VGtKGCgsJw/4j0/KAcrQP-nQRCb9qXWEVBLQg/h0/h001.s9Kd9z6mdqlVDOlhaIZnsfq-nNaRev4o3JZ9XlBguHk", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:43.259282", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:31:20 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_1.json", "activation_file": "activation_emails_20250813.json", "match_score": 1.0, "match_details": {"email_match": true, "name_similarity": 1.0, "compared_emails": "<EMAIL> <-> <EMAIL>", "compared_names": "<PERSON> <-> <PERSON>"}}, "merge_timestamp": "2025-08-14T10:15:41.835004", "status": "matched", "unique_id": "reg_97_charles<PERSON>man607_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "*********", "gender": "male", "address": "1432 Romano Street", "city": "Cambridge", "state": "MA", "zipcode": "02138", "high_school_name": "Alverno College", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:35:35", "registration_file": "20250813_203535_shanerocha289_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.835686", "status": "registration_only", "unique_id": "reg_98_shanerocha289_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Vaillancourt", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "678014752", "gender": "male", "address": "4272 Dawson Drive", "city": "Little Rock", "state": "AR", "zipcode": "72212", "high_school_name": "p", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:35:36", "registration_file": "20250813_203536_mi<PERSON><PERSON><PERSON>illancourt448_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.836543", "status": "registration_only", "unique_id": "reg_99_mi<PERSON><PERSON><PERSON><PERSON><PERSON>urt448_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "510760658", "gender": "female", "address": "728 Sherman Street", "city": "<PERSON><PERSON>", "state": "KS", "zipcode": "66419", "high_school_name": "c", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:35:55", "registration_file": "20250813_203555_janicecummings744_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.837340", "status": "registration_only", "unique_id": "reg_100_janicecummings744_at_caosi_fun"}, {"registration_info": {"full_name": "Melba Rainville", "first_name": "Melba", "last_name": "Rainville", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "418508703", "gender": "female", "address": "822 Fleming Street", "city": "ALBANY", "state": "LA", "zipcode": "70711", "high_school_name": "Alvarado High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:37:12", "registration_file": "20250813_203712_melbarainville310_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.838340", "status": "registration_only", "unique_id": "reg_101_mel<PERSON><PERSON>ville310_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "219420103", "gender": "male", "address": "105 Pine Tree Lane", "city": "GLENDO", "state": "WY", "zipcode": "82213", "high_school_name": "Calvary Bible Christian Academy", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:37:34", "registration_file": "20250813_203734_earlmatthews267_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.839123", "status": "registration_only", "unique_id": "reg_102_earlmatthews267_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "127625651", "gender": "male", "address": "3389 Gandy Street", "city": "Syracuse", "state": "NY", "zipcode": "13202", "high_school_name": "Alverno College", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:38:54", "registration_file": "20250813_203854_carlosmyers982_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.839896", "status": "registration_only", "unique_id": "reg_103_carlos<PERSON>ers982_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "378461570", "gender": "female", "address": "3184 Shingleton Road", "city": "Kalamazoo", "state": "MI", "zipcode": "49007", "high_school_name": "Palisades Park Junior Senior High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:39:23", "registration_file": "20250813_203923_car<PERSON><PERSON>on170_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.840796", "status": "registration_only", "unique_id": "reg_104_<PERSON><PERSON><PERSON><PERSON>170_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "Angel<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "180782470", "gender": "female", "address": "4489 Horseshoe Lane", "city": "Fort Washington", "state": "PA", "zipcode": "19034", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:39:34", "registration_file": "20250813_203934_angeliapang503_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.841588", "status": "registration_only", "unique_id": "reg_105_angeliapang503_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "Louis", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "510102412", "gender": "male", "address": "4663 Maloy Court", "city": "<PERSON><PERSON><PERSON>", "state": "KS", "zipcode": "67741", "high_school_name": "Alba High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:40:33", "registration_file": "20250813_204033_louisclarke848_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.842323", "status": "registration_only", "unique_id": "reg_106_louisclarke848_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "426421491", "gender": "male", "address": "173 Washington Avenue", "city": "<PERSON>", "state": "MS", "zipcode": "39208", "high_school_name": "Center Academy at Pinellas Park", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:41:21", "registration_file": "20250813_204121_eddiehain234_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.843016", "status": "registration_only", "unique_id": "reg_107_eddiehain234_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "335203598", "gender": "female", "address": "856 Oakmound Road", "city": "Chicago", "state": "IL", "zipcode": "60609", "high_school_name": "c", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:41:57", "registration_file": "20250813_204157_brendathompson221_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.843805", "status": "registration_only", "unique_id": "reg_108_brendathompson221_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "646647596", "gender": "female", "address": "1385 Austin Secret Lane", "city": "AVON", "state": "IL", "zipcode": "61415", "high_school_name": "c", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:43:32", "registration_file": "20250813_204332_maryanngaines402_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.844557", "status": "registration_only", "unique_id": "reg_109_maryanngaines402_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "564723351", "gender": "male", "address": "1223 Locust Court", "city": "RUSHVILLE", "state": "NY", "zipcode": "14544", "high_school_name": "Alvernia University", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:43:48", "registration_file": "20250813_204348_percydreher216_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.845300", "status": "registration_only", "unique_id": "reg_110_percydreher216_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "257978919", "gender": "male", "address": "949 White Lane", "city": "Macon", "state": "GA", "zipcode": "31201", "high_school_name": "Cesar <PERSON> Public Charter School for Public Policy: Parkside Campus", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:44:30", "registration_file": "20250813_204430_floydbuckley251_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.846057", "status": "registration_only", "unique_id": "reg_111_floydbuckley251_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "292785290", "gender": "male", "address": "4086 Cedarstone Drive", "city": "AMHERST", "state": "NH", "zipcode": "03031", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:45:31", "registration_file": "20250813_204531_marksmith979_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.846653", "status": "registration_only", "unique_id": "reg_112_marksmith979_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "Bill", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "548150828", "gender": "male", "address": "1864 Brown Bear Drive", "city": "EATONTOWN", "state": "NJ", "zipcode": "07799", "high_school_name": "c", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:46:32", "registration_file": "20250813_204632_billmartin845_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.847364", "status": "registration_only", "unique_id": "reg_113_billmartin845_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Law", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "445626789", "gender": "female", "address": "3634 Ruckman Road", "city": "Oklahoma City", "state": "OK", "zipcode": "73102", "high_school_name": "Cesar <PERSON> Public Charter School for Public Policy: Parkside Campus", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:48:14", "registration_file": "20250813_204814_claralaw698_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.848166", "status": "registration_only", "unique_id": "reg_114_claralaw698_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "195648926", "gender": "male", "address": "4911 Stone Lane", "city": "Bensalem", "state": "PA", "zipcode": "19020", "high_school_name": "California City High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:49:46", "registration_file": "20250813_204946_leonardgordon985_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.849068", "status": "registration_only", "unique_id": "reg_115_leonardgordon985_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "654036862", "gender": "female", "address": "4455 Kessla Way", "city": "Florence", "state": "SC", "zipcode": "29501", "high_school_name": "c", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:49:57", "registration_file": "20250813_204957_teresitaschutte312_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.850012", "status": "registration_only", "unique_id": "reg_116_teresitaschutte312_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "532942523", "gender": "male", "address": "4122 Honeysuckle Lane", "city": "Centralia", "state": "WA", "zipcode": "98531", "high_school_name": "Cesar <PERSON> Public Charter School for Public Policy: Parkside Campus", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:50:28", "registration_file": "20250813_205028_anthonybuchanan380_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.850779", "status": "registration_only", "unique_id": "reg_117_anthonybuchanan380_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "Ju<PERSON>z", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "347030225", "gender": "male", "address": "142 Vine Street", "city": "Chicago", "state": "IL", "zipcode": "60631", "high_school_name": "Alvernia University", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:52:13", "registration_file": "20250813_205213_randyjuarez269_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.851481", "status": "registration_only", "unique_id": "reg_118_randy<PERSON><PERSON>z269_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "624299330", "gender": "female", "address": "2778 Euclid Avenue", "city": "Thousand Oaks", "state": "CA", "zipcode": "91362", "high_school_name": "Alverno High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:52:44", "registration_file": "20250813_205244_eileenmccarthy944_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.852244", "status": "registration_only", "unique_id": "reg_119_eileenmccarthy944_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "591994506", "gender": "female", "address": "3490 Trails End Road", "city": "Miami", "state": "FL", "zipcode": "33176", "high_school_name": "Alba Golden High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:54:19", "registration_file": "20250813_205419_nickolewright680_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.852944", "status": "registration_only", "unique_id": "reg_120_nickolewright680_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "608248593", "gender": "male", "address": "1145 Beech Street", "city": "Danville", "state": "CA", "zipcode": "94526", "high_school_name": "International School Phnom Penh", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:54:29", "registration_file": "20250813_205429_kennethswanger835_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.853627", "status": "registration_only", "unique_id": "reg_121_kennethswanger835_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "470154734", "gender": "female", "address": "2007 Rocket Drive", "city": "Minneapolis", "state": "MN", "zipcode": "55406", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:56:17", "registration_file": "20250813_205617_anitahoman655_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.854297", "status": "registration_only", "unique_id": "reg_122_anitahoman655_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "518984507", "gender": "male", "address": "3625 Young Road", "city": "PASADENA", "state": "CA", "zipcode": "91117", "high_school_name": "c", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:56:52", "registration_file": "20250813_205652_kevinandrews430_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.855031", "status": "registration_only", "unique_id": "reg_123_kevinandrews430_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "Livingston", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "444583307", "gender": "female", "address": "2292 Hornor Avenue", "city": "Claremore", "state": "OK", "zipcode": "74017", "high_school_name": "International School Phnom Penh", "hs_grad_year": 2025, "timestamp": "2025-08-13 20:58:05", "registration_file": "20250813_205805_mindylivingston729_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.855846", "status": "registration_only", "unique_id": "reg_124_mindylivingston729_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "585756718", "gender": "male", "address": "3421 Cooks Mine Road", "city": "<PERSON><PERSON>", "state": "NM", "zipcode": "87571", "high_school_name": "Center Academy at Pinellas Park", "hs_grad_year": 2026, "timestamp": "2025-08-13 20:58:43", "registration_file": "20250813_205843_anthonymannino437_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.856709", "status": "registration_only", "unique_id": "reg_125_anthonymannino437_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "King", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "427312547", "gender": "male", "address": "1160 Oxford Court", "city": "Coffeeville", "state": "MS", "zipcode": "38922", "high_school_name": "Albany College of Pharmacy and Health Sciences", "hs_grad_year": 2025, "timestamp": "2025-08-13 21:00:15", "registration_file": "20250813_210015_leoking831_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.857415", "status": "registration_only", "unique_id": "reg_126_leoking831_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "162320726", "gender": "male", "address": "4561 Burning Memory Lane", "city": "Philadelphia", "state": "PA", "zipcode": "19124", "high_school_name": "Alva Senior High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 21:00:25", "registration_file": "20250813_210025_julianwallace232_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.858159", "status": "registration_only", "unique_id": "reg_127_julian<PERSON>ace232_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "566930952", "gender": "male", "address": "232 Pretty View Lane", "city": "SEDONA", "state": "AZ", "zipcode": "86341", "high_school_name": "Alba High School", "hs_grad_year": 2026, "timestamp": "2025-08-13 21:02:20", "registration_file": "20250813_210220_tedwilson108_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.858811", "status": "registration_only", "unique_id": "reg_128_tedwilson108_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>s", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "074327632", "gender": "female", "address": "3923 Benedum Drive", "city": "Middletown", "state": "NY", "zipcode": "10940", "high_school_name": "p", "hs_grad_year": 2025, "timestamp": "2025-08-13 21:02:59", "registration_file": "20250813_210259_ellenlittles577_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.859432", "status": "registration_only", "unique_id": "reg_129_ellenlittles577_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "531215306", "gender": "male", "address": "1443 Fort Street", "city": "Kent", "state": "WA", "zipcode": "98032", "high_school_name": "Alva Senior High School", "hs_grad_year": 2025, "timestamp": "2025-08-13 21:02:59", "registration_file": "20250813_210259_nicholasbooker667_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.860152", "status": "registration_only", "unique_id": "reg_130_nicholasbooker667_at_caosi_fun"}, {"registration_info": {"full_name": "<PERSON>", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone": "**********", "birthdate": "01/01/2005", "ssn": "579286993", "gender": "male", "address": "2998 Rhode Island Avenue", "city": "Washington", "state": "DC", "zipcode": "20005", "high_school_name": "Goucher College Post-Baccalaureate Pre-Medical Program", "hs_grad_year": 2025, "timestamp": "2025-08-13 21:06:33", "registration_file": "20250813_210633_quentinlee163_at_caosi.fun.json"}, "activation_email": null, "merge_timestamp": "2025-08-14T10:15:41.861141", "status": "registration_only", "unique_id": "reg_131_quentinlee163_at_caosi_fun"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszrKRRb2W8qnuMsl3pr1uvA86uLZg_du0JKHyLrm6I9GglfQc4UoUeHntX7x3oOqxU5Thp-VouQQAqKo7QSTlU4BMHdadZp9d5xpmvhY8bFCkOG6lPUI3Q3M9v4Fbz3NVFxRznvFY6G2IPkQQ9G5J9bg/4j0/MlJCJQtCQx2ks9VfryTLNg/h0/h001.W5EpFW3GpYZWr9689E2CXS_AfD-Xz3oRnG-g3G697tA", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:46.747269", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:27:33 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_7.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861276", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszIasBXkVJWF6Oky8a0Wnr-D5AWEZIZJiWSY0c9X0lc8dEOGg1nCUvc69T2kUAg5DZLVkEn9-1jmUe2e_aq4h5t43vg2hqSGTr4uwaxTKinfXNVi5HLfYpwjZH6YKmHGkwUBWWzWW8GY5WiaxHeD223g/4j0/u2xHR6sOTWWuksO9aH9zBA/h0/h001.2NZkDQa2IF3QuAya2hIHaV5DHLMteagXTwEEyxfHIfI", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:48.759297", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:23:09 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_10.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861287", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszY8slF5ZvRT3oswa0qRpS5KcvHuyoe_WPipjKBvLmV50aAmiigr2VXMRqBIBIsSOZG94JDKnegW-JtKRFcfnrOxGC7PHCl75iMx8ig_0C-M5QviyegFgKO4diO5M15icAh1l6HEFONf7uuC3ZKefdzQ/4j0/ejkDME7gTHaJH0cMkFp_JA/h0/h001.puG6NBGGMAw6nETIS0nyQCnEEJ-csFQ7RktrKuk_Asc", "temporary_code": "*********", "recipient_name": "<PERSON><PERSON>", "extraction_timestamp": "2025-08-13T12:37:51.094771", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:20:26 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_16.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861291", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszsTaGg59hAHguusZqKB9dajPbCTIbvmEQMhhKSUG9B02PhW_U6qB30uTmwStfbxtNHJYvNhhpwPOYYm_7pXF1-7pcsRPE5qhOG7jjQb9YeAWt7GLkD1bju6k-BB_zP5mU6F_g-zqb8sFvjgD-NRQelQ/4j0/7DqlK8GsQa-49rWqochykw/h0/h001.Gn-bMQfkDl5j53dWzDORKXU7Ohu--aZC6bdAl7a9bWo", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:51.162348", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:21:41 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_17.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861294", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszj3128R4VpYHvGtFXHPX6U7jMTZTt4sbCaMMhh2rlTBJSrJGoCqF6XT2vk8PeUU4GP1FKE8uPSvu3YSeYz90yPmHtghv4RSl9Xwf_vvaGiqy9eq71uAxE7znTsHXjRFhSvs-RijOm0Ik0s-vaJlQs8w/4j0/t7zWjuzZSm2kzEJG5tnNnA/h0/h001.nRmRlLH7o9ecVSrLcm1K6rA9wWuzLgOK2oNJ31rk3NE", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:52.926207", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:16:58 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_22.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861297", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszW1Ut0osdpNDk-G1wTuY9cjvj2tBRKACxXAMt1l4-h74j3Kyq3Wjk_z4sgHRz9MGqSqGPagKajnLMQnKx4Aq7Zm9Tl0OgAV8CCOUzJ77F3vPUm8Iz5bF0LZA2NM9WEwdNGx3WayKToXPd4fVpnItoyQ/4j0/Zs_kFmazTfOwaqz_8J7pIQ/h0/h001.mi8cSWy0ex83K3OKPj2dD0J5BuwRzC5EAoUbh_vTzq8", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:53.089664", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "lance<PERSON><EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:17:07 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_23.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861300", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz2aeiIclk-rfu7idz9SJdgQYjG0RecCS2bqu5rws8Ic8Y7PkhKzXQzsVdm6m1Wi_WC-FlmGDsuFc7e3CxwtLyHDfEhyU2U-dl9GJ1XyrxoliuOiBXEKB7piSI8nuS8TPetyWCsNQ57WRvrLqpPVnoQQ/4j0/xL2t3ALoRlm35xHYRF9INw/h0/h001.EhLhbYQ86nQei43l5DRQxbTfRtQtO2oFQqJrhwNamSM", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:53.169018", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:16:47 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_24.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861303", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszqwkHwXi1JmBCa-PT22lnihng9OHlNc6_E4s2TnsPE99ssr9OVq2fYqi-OEKgmyfmD50-NS4dutaxOgHR8VkytF8pjz6ChUI_BibGAPOnUOMJIX4bZoM5VIrUat4PV8OHo8sKHC3sgqZVRdmMCqECew/4j0/9XESLBbdQMq-Wp_4x1jl9g/h0/h001.Er8mYP4hFzBzVGmMtnLVmq91I2k5S_IHSu30YD707eg", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:53.169418", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:15:56 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_25.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861306", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszEFP0F8MCiE9qZ8EcJC4u2CufupwebYuCzfjC93KCWtyxv6YcD3OzTydkDKft9nu_LzY-5nRGlXN9PMqEjP82C2W4uU4sD0KZJqx9IQ9opQje8xu8qySbsm1bdWqMyE9TywTo9JBRXxXUecCh_qcYXA/4j0/0r1w4ladRxKrYttTr6ym7g/h0/h001.2o88_RF2oVP9IPsUGeDdlr4xjGnPsxgd0xVBo7x_7Ig", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:53.605740", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:14:54 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_26.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861308", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszqLJpt2RRZn5Tk1pOjIKSpNQIO0gO6QCsYFcUlypnT-B9kh1hbRah7-m92jkrSJLCp4_Cz1aIypRBOPi82JozJh-4Wvs0mS-jNFCindwC4WTDmXJT9RsjRQJfWvY5SqQZg7RnC9q4-aM4XzAYnUu4GA/4j0/IO7UkOozQdaEw3ZaWf_aLw/h0/h001.xRgs3TMVSTTay9NsU4ykV4n1xYlc55UO7eR4EmRI7LA", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:53.737650", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "heleng<PERSON><EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:15:57 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_27.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861311", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszr6iaszUinawH2c5sG5Rmf19_Eiq-QS1pw2r6tO2gWr9oM5FpXSfgc_Wg8f0K3bANK5nOHntOa9OCcMeOyy4a_pVw2_yVhU79Obmu1hNs8n1pUJ6HayRv3LqdtLzQnoKeuahuDlu1xBrbMXZ5_E-khA/4j0/jSyuEln_TuW8-q6WgcaA0w/h0/h001.FSix9_7CfAoSV9GVHx1gETJgdSQ_RIgiWdGgwblneig", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:53.748208", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:15:15 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_28.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861314", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszNtpZd8q451JKEA7DTzikxrbPZ3dDEDn1nv7j-QiXTrkI6EFBr60dFHQjFOxaRoj5Easkm9K0Nz_AzNS9Il2g7bfKCjBKqSZoi7MLH7J544QyOn-Yzdnq1ty1VTEfhuwtlbUw4KVLmb7DPJZZfKMTGA/4j0/0v4Z6-C0Sr2R8erNizakUg/h0/h001.KyqF8EvCIWbV3U3Mpry6wdKuT4s35fj_hDac_7z_TyY", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:55.184406", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:11:51 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_31.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861316", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszGixL93vhf3Gbg8FaIbUH4k3wMCGV17ugBylTkMp4uWee3bAXOAUKDCwB6Z6MgDZIuuLYkd7qQSMvvZORj7g79h1udCms97cgXsnMe4I9cFeAI7ZR_rKfUE6xEcPzdrsunuU6geIe9ySRBzUdqhVPug/4j0/GePLF-YiTz-WOOQeT8j1Uw/h0/h001.a4t1A3VGgIIOSN4PDmtflEoYPoIasqp3JuWZNkM2m0M", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:55.264060", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:13:49 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_32.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861322", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszq1p9KVqqRa6arBqX8XYdxfOlCBMeF_n-WftsjK2q6Kv1FRESDcfikaT9SB83WyiTH0N4E46iygAnIL26V7c4Wm_6nILpdiiQnDYvgcXapRGRQk9KsfaNUeHq5cigdwe7ImXD3ltaHqngACAUSmnPmw/4j0/WXk9GhBgQxaTbT8WxCFq4w/h0/h001.fHJayhqsIfkNavmqkOHz12WkI7K8c80u4W302s9lY1A", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:55.630444", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:13:48 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_33.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861325", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszWxeUmVz-weuUkxwAzG4BDzzg-6I98WmotlG0020YcIFaTZ2kXd45e60vD5Dc3YYHCZa6aJhqPlnCrtI-LfJkP-nyrukvl18PNGbk5gALJXr9Grn0G8KEk39g7KxYhezc8QtM0pWJc1a0ssVCdbGwGg/4j0/iFjIu3M_T_egwFeZwThD4w/h0/h001.HCAzIdfHpOMEz0MBrFUkecodsQIni_SO9huf9UFfXWw", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:56.099401", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:10:54 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_34.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861327", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszZruZddMzKWijux6lcUZJOYt1HXupfwEfY9keuamBWpMz7QisSlk4BPf4knPOyMAxqFyETmnNpQjRft4AYfXFxexoVYnYgdVkGRLrJe8jDLENfeOfSj3XVXZG6Rqb6099GH9H0YF4cVALt0zJaitWig/4j0/ZPtmBC6CSLWmb89-u2BUmw/h0/h001.9W2PovihicxsrIoRvdGQJ3q6Ms4wWWsy1juRq21U4CE", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:56.169567", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:09:49 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_36.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861330", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz-7AVX3-aZ0uSf-badWJ2yagx4TA0hZkKMjdqF9xJHXXXM8N6LbpvqblL3WmsZTRlXX1LVTcq0nhT6uKTvmoGbilVKkFZFRWweJg8PFcHgwMRAQf-bVUGc4kuByfdvy2ZBRAoW9KUfpzJ_aglt_ldPw/4j0/HQ8WbDBfQUSW7_WSbWv8_A/h0/h001.-Mf1lf6PsZsHMSO1pDbhzmCBJrzIHyS648SifWuNuLY", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:57.164132", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:05:42 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_38.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861333", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszybn34h-M8vWVbC9Dz09vRq_kifALftQ2EKof-xC8WONz5cZrI3uazp_Jw7dwAbku0sUCvizk0PCoww8zRT-RKzBaL7H0iJGtvJLF8Df7Z39GKrALBOvB9GgPa4vH6K4Kod6aoJ_tpSyvtffGsvARzA/4j0/uy_63LXfRm6MpGLKYLVeLA/h0/h001.AsPpY8IZln5dwWGz4RWeaTCkFB3k-jGITL1L4LES680", "temporary_code": "*********", "recipient_name": "<PERSON><PERSON>", "extraction_timestamp": "2025-08-13T12:37:57.560791", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:06:09 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_40.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861335", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszw4V2yetQl-IUOM19DyxmtC5stsWGubyYVjHD1tVyF-8ZM2W6dNq9vHHDzc9mG4s2BsH-OD0RD1cl2Rx8evVHw8Fxcd-4GCtIQ1Wy3x4cKR14rGSlY_n_8Xf-SusYilVh2HUiG5INdITMMjJYpB3OHg/4j0/QVwWjCgBQQqLOzrwlHmABA/h0/h001.pPKGLgImwc50bgMDUKJKVRnugdy5smchi-lDUqv5umo", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:57.576843", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:07:35 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_41.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861339", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszGanUokQLxvab_SBbSW8CFhteFjwtr_PA8N4l_QgVX5R29R6ylXe2eilWSpGb-e2zZluRT-KJwIN_9j-dfTh8wdn0CjG0Y4dWHUp7j6aS--StI2AdCMjhoKpL3QGGtOu0hmj4JX0Jxm3Xa2veOazfiw/4j0/wPDDtWO8Q3ymMhcxDImyrw/h0/h001.c8tv42pFrhwFwJz1x1gBFBBuISDxzDY2bMn_4Niw9q0", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:58.062059", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:03:58 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_42.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861342", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz85i-4WxVls4EeVg6POYjpwrkxuyzAK1wuG0XI2vw8LBBXzjgw-2Av8MFlQCGB629U1mUhzQk-o8yvr3B-kRbEbQybCW_ijDxtlICxGOpLurWuBgtyA_NUVlLGKAPXSPX08k2EF3R3-k6uj5NL_DmEQ/4j0/pho18fKBSAGeFU03CYyiNA/h0/h001.mu1pV5ik5ed1GgrWpS5ppqqsR76z7ohwuEpLF0ZCZ_c", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:59.046308", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:03:02 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_44.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861344", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszpvSzQ3gj0WEMjqbFUaVaznCfGqsaCrONxGf1Nkre9GQhdkeiRlpyvjJC4aV7DXSkE_aCfosWGYp_-q3P1QNXFsnjHeoDoNgUiiUpQMgdLEo83NKjKCJJnlgU2jl4WN40hP1XgUcwWVS_wle_yRyQhg/4j0/YdKZySCsRG2yFd_zmORxVQ/h0/h001.wK_TLi_wyHjM2V8C8Dlv3lQUO8Zq8JajrghDYNh8siw", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:59.176713", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:04:01 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_45.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861347", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszP9bhbivax0lWby1LudykE2VTIjdicGgAMb6sFpIapXutaFY1aQlWXPqenQYvhgN0GozE2ZHmFhM3yryB04aEMFoYp6diJE3NVbaTUsZ6GkkGp5cxhH65Bes6SoTClIRH9XjkYFLVHTZp8aR4TFhZ9Q/4j0/BxU4f_nJRAamQcFrVjsMCg/h0/h001.I4I5OE7mPX4Ld0o56dtLjOuFKlp3G7IlHylJeBLl1Sk", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:59.197263", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:03:01 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_46.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861350", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszR0zMYvjOC_UZTHrN4ceNRCmSPrsmbRzZrFY8jn7GHy_-H4iNAHFmD7dBY-f15i5dZGdZwfgTQqlPAsztIaTrZJQgl69ERbNzWhmm33wfaCDeiuz9MQUtyNwP19cxIl-pTkneKqlo8kv8kWmZR_BF5g/4j0/VKYq5D2MShqPZpenSrI7Kw/h0/h001.6kDu1QdAvaEDrIU0xAD8hUlB3xaMwYyQYIIuklqSvDk", "temporary_code": "*********", "recipient_name": "<PERSON><PERSON>", "extraction_timestamp": "2025-08-13T12:37:59.412246", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:02:02 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_47.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861352", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszIzxa_ddv7QRQNpbnFVv7JSOlJRvgiBRYSeT7LiUFqXpTAlvirvbdmYibz8LmAVQDVJL5-QQ4Pz3ccNRM7sWM0sRj54qD4raevicmN4AwEfIX-fpsQVbfk4P8UZ6P4tNb-fY9YDzr85VGh0RmADyzjQ/4j0/RaelG61FRp6fTn3IDbG9Dg/h0/h001.pbUGzxFjniGiLFHAj_Xh9-2mKpxqFkAH6POJ1CjJO5k", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:37:59.544328", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "<EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:01:53 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_48.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861355", "status": "activation_only", "unique_id": "email_**********"}, {"registration_info": null, "activation_email": {"activation_link": "https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tszyJKjUPkwUsMnKREEuPUfFkZJsZYf9qBaZHym-CABMwdy9MXDrBc_nK4dXxuVIDpDgJsQEnfbMAhrtGTPXjfJBMt_XFP2khg7cGmVgdqLRa_mgpHFabq0abg0yvTnoNArAuHWbFJsbaKTKbUNGiO6WA/4j0/kkEuKjKwQvOFcJFT_2uQag/h0/h001.DCY166KYqvcW_jctwdgl_mH75KMyb6r6u-zk_spPwP4", "temporary_code": "*********", "recipient_name": "<PERSON>", "extraction_timestamp": "2025-08-13T12:38:00.348775", "email_metadata": {"subject": "Activate your OSU application account", "from_mail": "<EMAIL>", "to_mail": "kath<PERSON><PERSON><EMAIL>", "mail_id": **********, "date": "Wed, 13 Aug 2025 02:01:53 +0000 (UTC)"}, "raw_email_file": "raw_email_20250813_123807_50.json", "activation_file": "activation_emails_20250813.json"}, "merge_timestamp": "2025-08-14T10:15:41.861358", "status": "activation_only", "unique_id": "email_**********"}]}