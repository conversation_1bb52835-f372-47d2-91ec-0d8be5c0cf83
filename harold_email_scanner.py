#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON>邮箱扫描器
专门扫描 <EMAIL> 邮箱的邮件内容
支持50线程并发，支持多页扫描，保存邮件到本地
"""

import requests
import json
import threading
import time
from datetime import datetime
from pathlib import Path
import queue
from typing import Dict, List, Optional, Tuple
import html

class HaroldEmailScanner:
    """Harold邮箱专用扫描器"""
    
    def __init__(self, target_email: str = "<EMAIL>", max_threads: int = 50):
        # TempMail API配置 (基于项目现有配置)
        self.tempmail_target = "<EMAIL>"
        self.tempmail_epin = ""
        
        # 扫描配置
        self.target_email = target_email
        self.max_threads = max_threads
        
        # 线程控制
        self.mail_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.threads = []
        self.scanning = False
        
        # 统计信息
        self.total_mails = 0
        self.processed_mails = 0
        self.target_mails = 0
        self.saved_count = 0
        self.page_count = 0
        
        # 输出目录
        self.output_dir = Path("harold_emails")
        self.output_dir.mkdir(exist_ok=True)
        
        # 线程锁
        self.print_lock = threading.Lock()
        self.stats_lock = threading.Lock()
        
        print(f"🎯 Harold邮箱扫描器初始化完成")
        print(f"📧 目标邮箱: {self.target_email}")
        print(f"🚀 并发线程数: {self.max_threads}")
        print(f"📁 输出目录: {self.output_dir}")
        
    def thread_safe_print(self, message: str):
        """线程安全的打印函数"""
        with self.print_lock:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def update_stats(self, increment_processed: bool = False, increment_target: bool = False, increment_saved: bool = False):
        """线程安全的统计更新"""
        with self.stats_lock:
            if increment_processed:
                self.processed_mails += 1
            if increment_target:
                self.target_mails += 1
            if increment_saved:
                self.saved_count += 1

    def get_mail_list(self, last_id: Optional[str] = None, limit: int = 50) -> Tuple[List[Dict], bool]:
        """
        获取邮箱中的邮件列表（支持分页）
        
        Args:
            last_id: 最后一个邮件ID，用于分页
            limit: 每页邮件数量
        
        Returns:
            Tuple[List[Dict], bool]: (邮件列表, 是否还有更多邮件)
        """
        try:
            if last_id:
                # 分页获取：从指定ID开始向下获取
                url = f"https://tempmail.plus/api/mails?email={self.tempmail_target}&last_id={last_id}&limit={limit}&epin={self.tempmail_epin}"
            else:
                # 默认获取：获取最新邮件
                url = f"https://tempmail.plus/api/mails?email={self.tempmail_target}&first_id=0&epin={self.tempmail_epin}"
            
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if data.get("result") and data.get("mail_list"):
                mail_list = data["mail_list"]
                has_more = data.get("more", False)
                return mail_list, has_more
            else:
                return [], False
                
        except Exception as e:
            self.thread_safe_print(f"❌ 获取邮件列表失败: {e}")
            return [], False
    
    def get_mail_detail(self, mail_id: str) -> Optional[Dict]:
        """获取单封邮件详情"""
        try:
            detail_url = f"https://tempmail.plus/api/mails/{mail_id}?email={self.tempmail_target}&epin={self.tempmail_epin}"
            response = requests.get(detail_url, timeout=15)
            response.raise_for_status()
            
            detail = response.json()
            if detail.get("result"):
                return detail
            else:
                return None
                
        except Exception as e:
            self.thread_safe_print(f"⚠️ 获取邮件 {mail_id} 详情失败: {e}")
            return None
    
    def is_target_email(self, email_detail: Dict) -> bool:
        """检查是否为目标邮箱的邮件"""
        to_field = email_detail.get("to", "").lower()
        return self.target_email.lower() in to_field
    
    def save_email_to_file(self, email_detail: Dict, mail_id: str) -> str:
        """保存邮件到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"harold_email_{timestamp}_{mail_id}.json"
            filepath = self.output_dir / filename
            
            # 准备保存的数据
            save_data = {
                "scan_timestamp": datetime.now().isoformat(),
                "target_email": self.target_email,
                "mail_id": mail_id,
                "email_detail": email_detail,
                "extracted_info": {
                    "subject": email_detail.get("subject", ""),
                    "from": email_detail.get("from", ""),
                    "from_mail": email_detail.get("from_mail", ""),
                    "to": email_detail.get("to", ""),
                    "date": email_detail.get("date", ""),
                    "text_content": email_detail.get("text", ""),
                    "html_content": email_detail.get("html", ""),
                    "attachments": email_detail.get("attachments", [])
                }
            }
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            self.update_stats(increment_saved=True)
            return str(filepath)
            
        except Exception as e:
            self.thread_safe_print(f"💾 保存邮件 {mail_id} 失败: {e}")
            return ""
    
    def worker_thread(self, thread_id: int):
        """工作线程函数"""
        self.thread_safe_print(f"🔧 线程 {thread_id} 启动")
        
        while self.scanning or not self.mail_queue.empty():
            try:
                # 获取邮件任务
                mail_item = self.mail_queue.get(timeout=1)
                mail_id = mail_item.get("mail_id")
                
                if not mail_id:
                    continue
                
                self.thread_safe_print(f"🔍 [线程{thread_id}] 处理邮件 {mail_id}")
                
                # 获取邮件详情
                email_detail = self.get_mail_detail(mail_id)
                self.update_stats(increment_processed=True)
                
                if email_detail:
                    # 显示邮件基本信息
                    subject = email_detail.get('subject', 'N/A')
                    to_field = email_detail.get('to', 'N/A')
                    self.thread_safe_print(f"📧 [线程{thread_id}] 邮件详情: {subject} -> {to_field}")
                    
                    # 检查是否为目标邮件
                    if self.is_target_email(email_detail):
                        self.update_stats(increment_target=True)
                        self.thread_safe_print(f"🎯 [线程{thread_id}] 发现目标邮件: {subject}")
                        
                        # 保存邮件
                        saved_file = self.save_email_to_file(email_detail, mail_id)
                        if saved_file:
                            self.thread_safe_print(f"💾 [线程{thread_id}] 邮件已保存: {saved_file}")
                        
                        # 将结果放入结果队列
                        self.result_queue.put({
                            "mail_id": mail_id,
                            "email_detail": email_detail,
                            "saved_file": saved_file,
                            "thread_id": thread_id
                        })
                
                # 显示进度
                progress = (self.processed_mails / max(self.total_mails, 1)) * 100
                self.thread_safe_print(f"📊 [线程{thread_id}] 进度: {self.processed_mails}/{self.total_mails} ({progress:.1f}%) | 目标邮件: {self.target_mails}")
                
                self.mail_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.thread_safe_print(f"❌ [线程{thread_id}] 处理异常: {e}")
        
        self.thread_safe_print(f"🏁 线程 {thread_id} 结束")
    
    def start_scanning(self, max_emails: int = 1000):
        """开始扫描（支持多页）"""
        self.thread_safe_print("🚀 开始扫描Harold邮箱...")
        
        # 收集所有要处理的邮件
        all_mail_items = []
        processed_count = 0
        last_id = None
        
        while processed_count < max_emails:
            self.page_count += 1
            self.thread_safe_print(f"📄 获取第 {self.page_count} 页邮件...")
            
            # 获取邮件列表（支持分页）
            mail_list, has_more = self.get_mail_list(last_id, limit=50)
            
            if not mail_list:
                self.thread_safe_print("📭 没有更多邮件了")
                break
            
            self.thread_safe_print(f"📧 获取到 {len(mail_list)} 封邮件，还有更多: {has_more}")
            
            # 添加邮件到处理队列
            for mail_item in mail_list:
                if processed_count >= max_emails:
                    break
                
                mail_id = mail_item.get("mail_id")
                if mail_id:
                    all_mail_items.append(mail_item)
                    processed_count += 1
                    last_id = mail_id  # 更新last_id用于下次分页
            
            # 如果没有更多邮件了，停止翻页
            if not has_more:
                self.thread_safe_print("📄 已到达邮件列表末尾")
                break
        
        if not all_mail_items:
            self.thread_safe_print("❌ 无邮件可扫描")
            return
        
        self.total_mails = len(all_mail_items)
        self.thread_safe_print(f"🔄 开始多线程处理 {self.total_mails} 封邮件...")
        
        # 将邮件任务加入队列
        for mail_item in all_mail_items:
            self.mail_queue.put(mail_item)
        
        self.thread_safe_print(f"📋 已添加 {len(all_mail_items)} 个邮件任务到队列")
        
        # 启动扫描标志
        self.scanning = True
        
        # 启动工作线程
        self.thread_safe_print(f"🔧 启动 {self.max_threads} 个工作线程...")
        for i in range(self.max_threads):
            thread = threading.Thread(target=self.worker_thread, args=(i+1,))
            thread.daemon = True
            thread.start()
            self.threads.append(thread)
        
        # 等待队列处理完成
        self.thread_safe_print("⏳ 等待所有邮件处理完成...")
        self.mail_queue.join()
        
        # 停止扫描
        self.scanning = False
        
        # 等待所有线程结束
        for thread in self.threads:
            thread.join(timeout=5)
        
        self.thread_safe_print("✅ 扫描完成！")
        self.print_final_statistics()
    
    def print_final_statistics(self):
        """打印最终统计信息"""
        print("\n" + "="*60)
        print("📊 Harold邮箱扫描完成统计")
        print("="*60)
        print(f"🎯 目标邮箱: {self.target_email}")
        print(f"📄 翻页次数: {self.page_count}")
        print(f"📧 总邮件数: {self.total_mails}")
        print(f"🔍 已处理邮件: {self.processed_mails}")
        print(f"✅ 目标邮件数: {self.target_mails}")
        print(f"💾 已保存文件: {self.saved_count}")
        print(f"📁 保存目录: {self.output_dir}")
        print(f"📈 匹配率: {(self.target_mails/max(self.total_mails,1)*100):.2f}%")
        print(f"🚀 使用线程数: {self.max_threads}")
        print("="*60)
        
        # 如果有保存的邮件，列出文件
        if self.saved_count > 0:
            print("\n📁 已保存的邮件文件:")
            for file in sorted(self.output_dir.glob("harold_email_*.json")):
                print(f"  📄 {file.name}")

def main():
    """主程序入口"""
    print("🔍 Harold邮箱扫描器 (支持多页扫描)")
    print("="*50)
    
    # 创建扫描器实例
    scanner = HaroldEmailScanner(
        target_email="<EMAIL>",
        max_threads=50
    )
    
    try:
        # 开始扫描（扫描最多1000封邮件）
        scanner.start_scanning(max_emails=1000)
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断扫描")
    except Exception as e:
        print(f"\n❌ 扫描过程中发生错误: {e}")
    
    print("\n🎉 程序结束")

if __name__ == "__main__":
    main() 