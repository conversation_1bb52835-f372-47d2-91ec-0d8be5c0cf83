#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的注册信息与激活邮件匹配器
优先邮箱地址完全匹配
"""

import json
import os
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

class SimpleEmailMatcher:
    """简化的邮箱匹配器"""
    
    def __init__(self):
        self.matched_count = 0
        self.total_registrations = 0
        self.total_activation_emails = 0
    
    def normalize_email(self, email: str) -> str:
        """标准化邮箱地址"""
        # 去除角括号、空格，转小写
        email = re.sub(r'[<>\s]', '', email.lower().strip())
        return email
    
    def load_data(self):
        """加载所有数据"""
        print("📂 加载注册信息...")
        registrations = []
        for json_file in Path("registrations").glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    reg = json.load(f)
                    registrations.append(reg)
            except Exception as e:
                print(f"⚠️ 加载失败 {json_file}: {e}")
        
        print("📧 加载激活邮件...")
        activation_emails = []
        for json_file in Path("activation_emails/daily").glob("activation_emails_*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    email_data = json.load(f)
                    activation_emails.extend(email_data.get("emails", []))
            except Exception as e:
                print(f"⚠️ 加载失败 {json_file}: {e}")
        
        self.total_registrations = len(registrations)
        self.total_activation_emails = len(activation_emails)
        
        print(f"✅ 加载完成: {len(registrations)} 注册信息, {len(activation_emails)} 激活邮件")
        return registrations, activation_emails
    
    def simple_match(self):
        """执行简单匹配"""
        registrations, activation_emails = self.load_data()
        
        # 创建激活邮件的邮箱地址索引
        email_to_activation = {}
        for email in activation_emails:
            to_mail = self.normalize_email(email.get("email_metadata", {}).get("to_mail", ""))
            if to_mail:
                email_to_activation[to_mail] = email
        
        print(f"\n🔍 开始匹配...")
        print(f"激活邮件邮箱索引: {len(email_to_activation)} 个")
        
        matched_records = []
        unmatched_registrations = []
        
        # 遍历注册信息，查找匹配的激活邮件
        for i, reg in enumerate(registrations, 1):
            reg_email = self.normalize_email(reg.get("email", ""))
            reg_name = reg.get("full_name", "")
            
            # 查找邮箱匹配
            if reg_email in email_to_activation:
                activation_email = email_to_activation[reg_email]
                self.matched_count += 1
                
                matched_record = {
                    "registration_info": reg,
                    "activation_email": activation_email,
                    "match_type": "email_exact_match",
                    "status": "matched"
                }
                matched_records.append(matched_record)
                
                act_name = activation_email.get("recipient_name", "")
                print(f"✅ [{i:3d}] {reg_name} ({reg_email}) -> {act_name}")
                
                # 从索引中移除已匹配的邮件
                del email_to_activation[reg_email]
            else:
                unmatched_registrations.append(reg)
                print(f"❌ [{i:3d}] 未匹配: {reg_name} ({reg_email})")
        
        # 处理未匹配的激活邮件
        unmatched_activations = []
        for email_addr, activation_email in email_to_activation.items():
            unmatched_record = {
                "registration_info": None,
                "activation_email": activation_email,
                "match_type": "activation_only",
                "status": "activation_only"
            }
            matched_records.append(unmatched_record)
            unmatched_activations.append(activation_email)
            
            act_name = activation_email.get("recipient_name", "")
            print(f"📧 未匹配激活邮件: {act_name} ({email_addr})")
        
        # 处理未匹配的注册信息
        for reg in unmatched_registrations:
            unmatched_record = {
                "registration_info": reg,
                "activation_email": None,
                "match_type": "registration_only", 
                "status": "registration_only"
            }
            matched_records.append(unmatched_record)
        
        return matched_records, unmatched_registrations, unmatched_activations
    
    def print_statistics(self, matched_records, unmatched_registrations, unmatched_activations):
        """打印统计信息"""
        print(f"\n📊 匹配统计报告")
        print(f"=" * 50)
        print(f"📝 总注册信息数: {self.total_registrations}")
        print(f"📧 总激活邮件数: {self.total_activation_emails}")
        print(f"✅ 成功匹配数: {self.matched_count}")
        print(f"⚠️ 未匹配注册: {len(unmatched_registrations)}")
        print(f"⚠️ 未匹配激活邮件: {len(unmatched_activations)}")
        
        if self.total_registrations > 0:
            match_rate = self.matched_count / self.total_registrations * 100
            print(f"📈 匹配成功率: {match_rate:.1f}%")
        
        if self.total_activation_emails > 0:
            coverage_rate = self.matched_count / self.total_activation_emails * 100
            print(f"📈 邮件覆盖率: {coverage_rate:.1f}%")
        
        print(f"=" * 50)
    
    def save_results(self, matched_records):
        """保存匹配结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"simple_matched_data_{timestamp}.json"
        
        output_data = {
            "match_timestamp": datetime.now().isoformat(),
            "statistics": {
                "total_registrations": self.total_registrations,
                "total_activation_emails": self.total_activation_emails,
                "matched_count": self.matched_count,
                "match_rate": self.matched_count / max(self.total_registrations, 1) * 100,
                "coverage_rate": self.matched_count / max(self.total_activation_emails, 1) * 100
            },
            "matched_records": matched_records
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 结果已保存: {output_file}")
        print(f"📁 文件大小: {os.path.getsize(output_file) / 1024:.1f} KB")
        return output_file

def main():
    print("🔗 简化邮箱地址匹配器")
    print("=" * 40)
    
    matcher = SimpleEmailMatcher()
    matched_records, unmatched_regs, unmatched_acts = matcher.simple_match()
    
    matcher.print_statistics(matched_records, unmatched_regs, unmatched_acts)
    output_file = matcher.save_results(matched_records)
    
    print(f"\n🎉 匹配完成！")
    print(f"📄 输出文件: {output_file}")
    print(f"📝 总记录数: {len(matched_records)}")

if __name__ == "__main__":
    main() 