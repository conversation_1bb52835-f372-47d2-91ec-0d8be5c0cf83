任务详情：
1进入：https://apply.osuokc.edu/apply/
快速定位到：<table class="fixed" role="none" style="width: 700px;"><colgroup><col style="width: 50%;"><col style="width: 50%;"></colgroup><tbody><tr><td><h2>Returning users:</h2>
    If you started an application but did not finish, load your account using your personal email address and password.
  <p><b><a href="/account/login?r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f">Log in</a></b></p></td><td><h2>First-time users:</h2>
    Create an account and get started!
  <p><b><a href="/account/register?r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f"><span>Create an account</span></a></b></p></td></tr></tbody></table>

点击<p><b><a href="/account/register?r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f"><span>Create an account</span></a></b></p>

会跳转到一个新的页面
等待元素加载后
<table class="plain" role="none"><colgroup><col style="width: 150px;"><col></colgroup><tbody><tr><td><label for="email" data-required="1">Email Address</label></td><td><input id="email" maxlength="64" name="email" size="48" type="email" value="" data-validate="{ required: true, format: 'email', help: 'Email address is required.' }" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></td></tr><tr><td><label for="first" data-required="1">First Name</label></td><td><input id="first" maxlength="64" name="first" size="32" type="text" value="" data-validate="{ required: true, help: 'First name is required.' }" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></td></tr><tr><td><label for="last" data-required="1">Last Name</label></td><td><input id="last" maxlength="64" name="last" size="32" type="text" value="" data-validate="{ required: true, help: 'Last name is required.' }" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></td></tr><tr><td><label for="birthdate">Birthdate</label></td><td><fieldset id="birthdate"><select aria-label="Month" id="birthdate_m" name="birthdate_m" size="1" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option value=""></option><option value="01">January</option><option value="02">February</option><option value="03">March</option><option value="04">April</option><option value="05">May</option><option value="06">June</option><option value="07">July</option><option value="08">August</option><option value="09">September</option><option value="10">October</option><option value="11">November</option><option value="12">December</option></select><select aria-label="Day" id="birthdate_d" name="birthdate_d" size="1" style="margin-left: 5px;" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option value=""></option><option value="01">1</option><option value="02">2</option><option value="03">3</option><option value="04">4</option><option value="05">5</option><option value="06">6</option><option value="07">7</option><option value="08">8</option><option value="09">9</option><option value="10">10</option><option value="11">11</option><option value="12">12</option><option value="13">13</option><option value="14">14</option><option value="15">15</option><option value="16">16</option><option value="17">17</option><option value="18">18</option><option value="19">19</option><option value="20">20</option><option value="21">21</option><option value="22">22</option><option value="23">23</option><option value="24">24</option><option value="25">25</option><option value="26">26</option><option value="27">27</option><option value="28">28</option><option value="29">29</option><option value="30">30</option><option value="31">31</option></select><select aria-label="Year" id="birthdate_y" name="birthdate_y" size="1" style="margin-left: 5px;" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option></option><option value="2015">2015</option><option value="2014">2014</option><option value="2013">2013</option><option value="2012">2012</option><option value="2011">2011</option><option value="2010">2010</option><option value="2009">2009</option><option value="2008">2008</option><option value="2007">2007</option><option value="2006">2006</option><option value="2005">2005</option><option value="2004">2004</option><option value="2003">2003</option><option value="2002">2002</option><option value="2001">2001</option><option value="2000">2000</option><option value="1999">1999</option><option value="1998">1998</option><option value="1997">1997</option><option value="1996">1996</option><option value="1995">1995</option><option value="1994">1994</option><option value="1993">1993</option><option value="1992">1992</option><option value="1991">1991</option><option value="1990">1990</option><option value="1989">1989</option><option value="1988">1988</option><option value="1987">1987</option><option value="1986">1986</option><option value="1985">1985</option><option value="1984">1984</option><option value="1983">1983</option><option value="1982">1982</option><option value="1981">1981</option><option value="1980">1980</option><option value="1979">1979</option><option value="1978">1978</option><option value="1977">1977</option><option value="1976">1976</option><option value="1975">1975</option><option value="1974">1974</option><option value="1973">1973</option><option value="1972">1972</option><option value="1971">1971</option><option value="1970">1970</option><option value="1969">1969</option><option value="1968">1968</option><option value="1967">1967</option><option value="1966">1966</option><option value="1965">1965</option><option value="1964">1964</option><option value="1963">1963</option><option value="1962">1962</option><option value="1961">1961</option><option value="1960">1960</option><option value="1959">1959</option><option value="1958">1958</option><option value="1957">1957</option><option value="1956">1956</option><option value="1955">1955</option><option value="1954">1954</option><option value="1953">1953</option><option value="1952">1952</option><option value="1951">1951</option><option value="1950">1950</option><option value="1949">1949</option><option value="1948">1948</option><option value="1947">1947</option><option value="1946">1946</option><option value="1945">1945</option><option value="1944">1944</option><option value="1943">1943</option><option value="1942">1942</option><option value="1941">1941</option><option value="1940">1940</option><option value="1939">1939</option><option value="1938">1938</option><option value="1937">1937</option><option value="1936">1936</option><option value="1935">1935</option><option value="1934">1934</option><option value="1933">1933</option><option value="1932">1932</option><option value="1931">1931</option><option value="1930">1930</option><option value="1929">1929</option><option value="1928">1928</option><option value="1927">1927</option><option value="1926">1926</option><option value="1925">1925</option><option value="1924">1924</option><option value="1923">1923</option><option value="1922">1922</option><option value="1921">1921</option><option value="1920">1920</option><option value="1919">1919</option><option value="1918">1918</option><option value="1917">1917</option><option value="1916">1916</option><option value="1915">1915</option></select></fieldset></td></tr></tbody></table>


发现了该表单
上面填入随机生成的邮箱地址，我们的邮箱地址
person_info.csv库中对应的姓名
出生日期统一为01/01/2005

填写完成后点击
<div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Postback(this, { cmd: 'submit' }); return false;" type="submit">Continue</button></div> 中的<button class="default" onclick="if (FW.Validate(this)) FW.Postback(this, { cmd: 'submit' }); return false;" type="submit">Continue</button> 按钮

然后监听
<p class="success">A temporary PIN has been sent to your email address. If you do not receive this message in the next few minutes, please check your junk mail folder.</p>
出现后，去邮件中寻找pin码
邮件格式：
<div id=":mv" class="a3s aiL msg4571657460116420926"><u></u>






<div>
<table border="0" cellpadding="0" cellspacing="0" width="100%"><tbody><tr><td align="center" bgcolor="#D0D0CE" style="background-color:#d0d0ce"><table align="center" border="0" cellpadding="0" cellspacing="0" width="500"><tbody><tr><td align="center" bgcolor="#D0D0CE" style="font-size:0;padding-left:0px;padding-bottom:0px;padding-right:0px;border-bottom:0px" valign="top"><table align="center" border="0" cellpadding="0" cellspacing="0" width="250"><tbody><tr><td align="left"><img alt="OSU OKC Office of Admissions" style="display:block;width:600px" width="600" src="https://ci3.googleusercontent.com/meips/ADKq_NYF35f5CBXs4gU5TFHya_r9rHW1HNJlA8ZtSXKd_TTMPIahhU1NnOODcJQYH9pPGyD55pgUtQoQksVS2glledRgoB8EFsMSloUwAqY6S-uSxocRITFmicUV698r1HkEPOQETdqKefmA=s0-d-e1-ft#https://apply.osuokc.edu/www/images/Admissions/Office%20of%20Admissions_Header.png" class="CToWUd a6T" data-bit="iit" tabindex="0"><div class="a6S" dir="ltr" style="opacity: 0.01; left: 718.5px; top: 148px;"><span data-is-tooltip-wrapper="true" class="a5q" jsaction="JIbuQc:.CLIENT"><button class="VYBDae-JX-I VYBDae-JX-I-ql-ay5-ays CgzRE" jscontroller="PIVayb" jsaction="click:h5M12e; clickmod:h5M12e;pointerdown:FEiYhc;pointerup:mF5Elf;pointerenter:EX0mI;pointerleave:vpvbp;pointercancel:xyn4sd;contextmenu:xexox;focus:h06R8; blur:zjh6rb;mlnRJb:fLiPzd;" data-idom-class="CgzRE" data-use-native-focus-logic="true" jsname="hRZeKc" aria-label="Download attachment " data-tooltip-enabled="true" data-tooltip-id="tt-c39" data-tooltip-classes="AZPksf" id="" jslog="91252; u014N:cOuCgd,Kr2w4b,xr6bB; 4:WyIjbXNnLWY6MTg0MDExNjQyODQwOTQ3NzQxMCJd; 43:WyJpbWFnZS9qcGVnIl0."><span class="OiePBf-zPjgPe VYBDae-JX-UHGRz"></span><span class="bHC-Q" jscontroller="LBaJxb" jsname="m9ZlFb" soy-skip="" ssk="6:RWVI5c"></span><span class="VYBDae-JX-ank-Rtc0Jf" jsname="S5tZuc" aria-hidden="true"><span class="notranslate bzc-ank" aria-hidden="true"><svg viewBox="0 -960 960 960" height="20" width="20" focusable="false" class=" aoH"><path d="M480-336L288-528l51-51L444-474V-816h72v342L621-579l51,51L480-336ZM263.72-192Q234-192 213-213.15T192-264v-72h72v72H696v-72h72v72q0,29.7-21.16,50.85T695.96-192H263.72Z"></path></svg></span></span><div class="VYBDae-JX-ano"></div></button><div class="ne2Ple-oshW8e-J9" id="tt-c39" role="tooltip" aria-hidden="true">Download</div></span></div></td></tr></tbody></table></td></tr><tr><td align="center" bgcolor="#FFFFFF" style="padding:20px;background-color:#ffffff"><table align="center" border="0" cellpadding="0" cellspacing="0" width="100%"><tbody><tr><td style="font-family:Open Sans,Helvetica,Arial,sans-serif;font-size:16px;font-weight:400;line-height:24px;padding-top:5px;padding-bottom:0px"><p>
                        Dear Danial Sawayn,<span class="im"><br><br>
                        Thank you for registering your application account with Oklahoma State University - OKC.<br><br>
                        Before you can apply, you need to <strong><a href="https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz6EMp1X11oSY-PJedVf8sXVFULJzTvbCv5ULLpWVEqMt6Dv2o0QYCavp6ytMvGlIXZ_YYmEWVJcTGAVgR8St102_ZArxNaVtaUlc5d4pi2TAAg-E1KXu46j-hPpm_ZQC2t5f7KEBth1BroJoVdSFKow/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h0/h001.0QbvijZ5xjFRmRoHqF9QIGdeE1oaK4T9kdoHh2MiyYU" title="https://apply.osuokc.edu/account/login?eid=Ozn7zH5UF2Ofw7OYYAIHamtTo6DpFhV4R5q1WbiugPaRGQwtJ3LyFA&amp;s=n&amp;r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz6EMp1X11oSY-PJedVf8sXVFULJzTvbCv5ULLpWVEqMt6Dv2o0QYCavp6ytMvGlIXZ_YYmEWVJcTGAVgR8St102_ZArxNaVtaUlc5d4pi2TAAg-E1KXu46j-hPpm_ZQC2t5f7KEBth1BroJoVdSFKow/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h0/h001.0QbvijZ5xjFRmRoHqF9QIGdeE1oaK4T9kdoHh2MiyYU&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw0xSdvwpPZ1G4RhYhlNhu4t">activate your account</a></strong>.<br><br></span>
                        When activating, please use this temporary code:&nbsp;<strong>*********</strong></p></td></tr></tbody></table></td></tr><tr><td align="center" bgcolor="#D0D0CE" style="padding-left:0px;padding-right:0px;background-color:#d0d0ce"><table align="center" border="0" cellpadding="0" cellspacing="0" width="100%"><tbody><tr><td align="center" bgcolor="#D0D0CE" style="font-size:0;padding-left:0px;padding-bottom:0px;padding-right:0px" valign="top"></td></tr><tr><td align="center" bgcolor="#D0D0CE" style="padding:0px;background-color:#d0d0ce"><table align="center" border="0" cellpadding="0" cellspacing="0" width="100%"><tbody><tr><td align="left" style="font-family:Open Sans,Helvetica,Arial,sans-serif;font-size:16px;font-weight:400;line-height:10px;padding-top:5px;padding-bottom:0px"><table align="center" cellpadding="5" cellspacing="0" style="width:500px" width="100%"><tbody><tr><td><div align="left" id="m_4571657460116420926container2"><div style="display:inline-block"><span style="font-family:Verdana,Geneva,sans-serif"><span style="font-size:9px"><strong>Office of Admissions</strong></span></span><br><span style="font-family:Verdana,Geneva,sans-serif"><span style="font-size:9px">Oklahoma State University - OKC</span></span><br><span style="font-family:Verdana,Geneva,sans-serif"><span style="font-size:9px">900 N. Portland Ave. | Oklahoma City, OK 73107</span></span><br><span style="font-family:Verdana,Geneva,sans-serif"><span style="font-size:9px"><a href="tel:************" title="tel:************" target="_blank">************</a> | <strong><a href="https://mx.technolutions.net/ss/c/u001.XEfBm45kvnMxKIc7tKAAI6IevuM8fwjSGTNxZf8i_C4/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h1/h001._6XO0PhQ5JMbqVsV2ESl4aPcXf8hCJfa9CbImRmotc4" title="https://osuokc.edu/" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.XEfBm45kvnMxKIc7tKAAI6IevuM8fwjSGTNxZf8i_C4/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h1/h001._6XO0PhQ5JMbqVsV2ESl4aPcXf8hCJfa9CbImRmotc4&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw3AkNFtmDW3HwPMX51YD0BP">osuokc.edu</a></strong></span></span></div></div></td><td style="width:246px;white-space:nowrap;text-align:right" valign="center"><a href="https://mx.technolutions.net/ss/c/u001.jxaZRujEt1lhE0WEp4QewFt-Xjd8vEyIQf0VhGguqDLnN8dIg9edM4Cd36X5qLef/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h2/h001.-u3esek8uiCnMoPnXYm7YxA6F6tGwF5WO3-ma5adr5I" title="https://www.facebook.com/osuokc" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.jxaZRujEt1lhE0WEp4QewFt-Xjd8vEyIQf0VhGguqDLnN8dIg9edM4Cd36X5qLef/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h2/h001.-u3esek8uiCnMoPnXYm7YxA6F6tGwF5WO3-ma5adr5I&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw1QeL_dTq5xEcRwIb7-kHap"><img alt="Facebook" style="width:45px;height:45px" height="45" width="45" src="https://ci3.googleusercontent.com/meips/ADKq_NYiWEAZw_u2uENv-4_EgfjVKgH-B5nIZi03Wf23UPoNkgi1S0vgNQG9WF0wsxL8lXu83moqpWQfSFbEdStrlIMlt6AFrauv5w7NSQ=s0-d-e1-ft#https://orange.okstate.edu/www/images/new-icon-fb.png" class="CToWUd" data-bit="iit"></a>&nbsp;<a href="https://mx.technolutions.net/ss/c/u001.gdErue62yx9LFCvq4pzlnmPXkrX7YbUsoJRFtrt6Wjs/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h3/h001.5Kk3R8U2RTM7am9edImCQAtRmLA-T4kTgnMaqyuEEgo" title="https://x.com/OSUOKC" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.gdErue62yx9LFCvq4pzlnmPXkrX7YbUsoJRFtrt6Wjs/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h3/h001.5Kk3R8U2RTM7am9edImCQAtRmLA-T4kTgnMaqyuEEgo&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw3dWyQwCSXx3QoOfaQBCwo9"><img alt="Twitter" style="width:45px;height:45px" height="45" width="45" src="https://ci3.googleusercontent.com/meips/ADKq_Nb9icjoWvDj1AKG5-8rxzdiU6sMoayICok9WZsDTV--7CiXHLF1g08w9FrF32FDkd6VKFkNwkSngPKz69KPg2F8qkHKHGLqZdk9=s0-d-e1-ft#https://orange.okstate.edu/www/images/new-icon-x.png" class="CToWUd" data-bit="iit"></a>&nbsp;<a href="https://mx.technolutions.net/ss/c/u001.jxaZRujEt1lhE0WEp4QewNyUWzt-M6pZ_FXvPJGcmVpHKNG5NudWcTeL0BSdlywa/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h4/h001.0qX3M_eQhmI8TrZbVfFzsxTqyDAbu0FtY0_1XaQrjPc" title="https://www.instagram.com/osu.okc" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.jxaZRujEt1lhE0WEp4QewNyUWzt-M6pZ_FXvPJGcmVpHKNG5NudWcTeL0BSdlywa/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h4/h001.0qX3M_eQhmI8TrZbVfFzsxTqyDAbu0FtY0_1XaQrjPc&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw2v8QzQ5H8X6UEhJ7EZXnUY"><img alt="Instagram" style="width:45px;height:45px" height="45" width="45" src="https://ci3.googleusercontent.com/meips/ADKq_Nanvy5JZLejcubh8E_LHK-nupJrl47AbQKriRmdnsb6VCXbTW922ZwbgJa0OZ465uqityoSeGChspM8aDId53fFxdGdZowBOPA103EzKg=s0-d-e1-ft#https://orange.okstate.edu/www/images/new-icon-insta.png" class="CToWUd" data-bit="iit"></a>&nbsp;<a href="mailto:<EMAIL>" title="mailto:<EMAIL>" target="_blank"><img alt="Email" style="width:45px;height:45px" height="45" width="45" src="https://ci3.googleusercontent.com/meips/ADKq_NYNDbCDd1mxpViFtx6ujGcRVQRGmL-63dwBaff0sIbNGlcoPT40ISsRpznfe4ZwgcqJUL5wHSRTuby1DFKIk7b_bLLfYz74mGLLzo7sww=s0-d-e1-ft#https://orange.okstate.edu/www/images/new-icon-email.png" class="CToWUd" data-bit="iit"></a></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><br>
          &nbsp;</td></tr></tbody></table>
<img src="https://ci3.googleusercontent.com/meips/ADKq_NYQFheFOG-M19LR864exP2QzixuoIMiV8NydYjNldjQBQkr5qFXn4KxiE3uCHlWuMNvuilnI1gujPdw6vYiohnQV-aO3yghwQ-g4nxCFnoBBG8bAcchu_QxbfMgQJ0cBvMFVB0zhG3ddiCFwtCcZ1eFmigrAA=s0-d-e1-ft#https://mx.technolutions.net/ss/o/u001.l7ggKG0fqQ6Gg4_ean_HcQ/4iy/qNjjPtp0TjSMqMnbQS8ULQ/ho.gif" alt="" width="1" height="1" border="0" style="height:1px!important;width:1px!important;border-width:0!important;margin-top:0!important;margin-bottom:0!important;margin-right:0!important;margin-left:0!important;padding-top:0!important;padding-bottom:0!important;padding-right:0!important;padding-left:0!important" class="CToWUd" data-bit="iit"></div><div class="yj6qo"></div><div class="adL">
</div></div>
中的 <td style="font-family:Open Sans,Helvetica,Arial,sans-serif;font-size:16px;font-weight:400;line-height:24px;padding-top:5px;padding-bottom:0px"><p>
                        Dear Danial Sawayn,<span class="im"><br><br>
                        Thank you for registering your application account with Oklahoma State University - OKC.<br><br>
                        Before you can apply, you need to <strong><a href="https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz6EMp1X11oSY-PJedVf8sXVFULJzTvbCv5ULLpWVEqMt6Dv2o0QYCavp6ytMvGlIXZ_YYmEWVJcTGAVgR8St102_ZArxNaVtaUlc5d4pi2TAAg-E1KXu46j-hPpm_ZQC2t5f7KEBth1BroJoVdSFKow/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h0/h001.0QbvijZ5xjFRmRoHqF9QIGdeE1oaK4T9kdoHh2MiyYU" title="https://apply.osuokc.edu/account/login?eid=Ozn7zH5UF2Ofw7OYYAIHamtTo6DpFhV4R5q1WbiugPaRGQwtJ3LyFA&amp;s=n&amp;r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz6EMp1X11oSY-PJedVf8sXVFULJzTvbCv5ULLpWVEqMt6Dv2o0QYCavp6ytMvGlIXZ_YYmEWVJcTGAVgR8St102_ZArxNaVtaUlc5d4pi2TAAg-E1KXu46j-hPpm_ZQC2t5f7KEBth1BroJoVdSFKow/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h0/h001.0QbvijZ5xjFRmRoHqF9QIGdeE1oaK4T9kdoHh2MiyYU&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw0xSdvwpPZ1G4RhYhlNhu4t">activate your account</a></strong>.<br><br></span>
                        When activating, please use this temporary code:&nbsp;<strong>*********</strong></p></td> 中的 <strong>*********</strong> 元素里面会有pin码

获取到pin之后
在页面中输入
<table class="plain" role="none"><colgroup><col style="width: 150px;"><col></colgroup><tbody><tr><th>Email</th><td><EMAIL><a href="/account/login?id=&amp;r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f" style="margin-left: 1em;">switch</a></td></tr><tr><th>Account</th><td><input name="eid" type="hidden" value="Ozn7zH5UF2Ofw7OYYAIHamtTo6DpFhV4R5q1WbiugPaRGQwtJ3LyFA">Sawayn, Danial</td></tr><tr><th><label for="password" data-required="1">Temporary PIN</label></th><td><input id="password" maxlength="64" name="password" size="48" style="width: 200px;" type="password" value="" data-validate="{ required: true, help: 'Password is required.' }" autocomplete="off" spellcheck="false" required="required"></td></tr><tr><th><label for="birthdate">Birthdate</label></th><td><fieldset id="birthdate"><select aria-label="Month" id="birthdate_m" name="birthdate_m" size="1" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option value=""></option><option value="01">January</option><option value="02">February</option><option value="03">March</option><option value="04">April</option><option value="05">May</option><option value="06">June</option><option value="07">July</option><option value="08">August</option><option value="09">September</option><option value="10">October</option><option value="11">November</option><option value="12">December</option></select><select aria-label="Day" id="birthdate_d" name="birthdate_d" size="1" style="margin-left: 5px;" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option value=""></option><option value="01">1</option><option value="02">2</option><option value="03">3</option><option value="04">4</option><option value="05">5</option><option value="06">6</option><option value="07">7</option><option value="08">8</option><option value="09">9</option><option value="10">10</option><option value="11">11</option><option value="12">12</option><option value="13">13</option><option value="14">14</option><option value="15">15</option><option value="16">16</option><option value="17">17</option><option value="18">18</option><option value="19">19</option><option value="20">20</option><option value="21">21</option><option value="22">22</option><option value="23">23</option><option value="24">24</option><option value="25">25</option><option value="26">26</option><option value="27">27</option><option value="28">28</option><option value="29">29</option><option value="30">30</option><option value="31">31</option></select><select aria-label="Year" id="birthdate_y" name="birthdate_y" size="1" style="margin-left: 5px;" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option></option><option value="2025">2025</option><option value="2024">2024</option><option value="2023">2023</option><option value="2022">2022</option><option value="2021">2021</option><option value="2020">2020</option><option value="2019">2019</option><option value="2018">2018</option><option value="2017">2017</option><option value="2016">2016</option><option value="2015">2015</option><option value="2014">2014</option><option value="2013">2013</option><option value="2012">2012</option><option value="2011">2011</option><option value="2010">2010</option><option value="2009">2009</option><option value="2008">2008</option><option value="2007">2007</option><option value="2006">2006</option><option value="2005">2005</option><option value="2004">2004</option><option value="2003">2003</option><option value="2002">2002</option><option value="2001">2001</option><option value="2000">2000</option><option value="1999">1999</option><option value="1998">1998</option><option value="1997">1997</option><option value="1996">1996</option><option value="1995">1995</option><option value="1994">1994</option><option value="1993">1993</option><option value="1992">1992</option><option value="1991">1991</option><option value="1990">1990</option><option value="1989">1989</option><option value="1988">1988</option><option value="1987">1987</option><option value="1986">1986</option><option value="1985">1985</option><option value="1984">1984</option><option value="1983">1983</option><option value="1982">1982</option><option value="1981">1981</option><option value="1980">1980</option><option value="1979">1979</option><option value="1978">1978</option><option value="1977">1977</option><option value="1976">1976</option><option value="1975">1975</option><option value="1974">1974</option><option value="1973">1973</option><option value="1972">1972</option><option value="1971">1971</option><option value="1970">1970</option><option value="1969">1969</option><option value="1968">1968</option><option value="1967">1967</option><option value="1966">1966</option><option value="1965">1965</option><option value="1964">1964</option><option value="1963">1963</option><option value="1962">1962</option><option value="1961">1961</option><option value="1960">1960</option><option value="1959">1959</option><option value="1958">1958</option><option value="1957">1957</option><option value="1956">1956</option><option value="1955">1955</option><option value="1954">1954</option><option value="1953">1953</option><option value="1952">1952</option><option value="1951">1951</option><option value="1950">1950</option><option value="1949">1949</option><option value="1948">1948</option><option value="1947">1947</option><option value="1946">1946</option><option value="1945">1945</option><option value="1944">1944</option><option value="1943">1943</option><option value="1942">1942</option><option value="1941">1941</option><option value="1940">1940</option><option value="1939">1939</option><option value="1938">1938</option><option value="1937">1937</option><option value="1936">1936</option><option value="1935">1935</option><option value="1934">1934</option><option value="1933">1933</option><option value="1932">1932</option><option value="1931">1931</option><option value="1930">1930</option><option value="1929">1929</option><option value="1928">1928</option><option value="1927">1927</option><option value="1926">1926</option><option value="1925">1925</option><option value="1924">1924</option><option value="1923">1923</option><option value="1922">1922</option><option value="1921">1921</option><option value="1920">1920</option><option value="1919">1919</option><option value="1918">1918</option><option value="1917">1917</option><option value="1916">1916</option><option value="1915">1915</option></select></fieldset></td></tr></tbody></table>
pin码以及我们先前的生日
最后点击
<div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Postback(this); return false;" type="submit">Login</button></div>
登录按钮，
等待监听元素：进入密码设置页面
<form method="post" onsubmit="return false;" novalidate="novalidate" data-fw-form="1" autocomplete="off"><h1>Set Password</h1><p>To protect the security of your account, please specify a new password. The password must meet complexity requirements.</p><div class="password_change" style="margin: 25px 0;"><div class="password_requirements"><div id="password_letter">At least one letter</div><div id="password_capital">At least one capital letter</div><div id="password_number">At least one number</div><div id="password_length">Be at least 12 characters</div><div id="password_mismatch">New passwords must match</div></div><input class="hidden" value="<EMAIL>" autocomplete="off"><table class="plain password_table"><colgroup><col style="width: 150px;"><col></colgroup><tbody><tr><th><label for="p1" data-required="1">New Password</label></th><td><input class="expanded" id="p1" maxlength="32" name="p1" oninput="password_check();" size="32" type="password" value="" data-validate="{ required: true, help: 'New password is required.' }" autofocus="autofocus" autocomplete="off" spellcheck="false" required="required"></td></tr><tr><th><label for="p2" data-required="1">New Password (again)</label></th><td><input class="expanded" id="p2" maxlength="32" name="p2" oninput="password_check();" size="32" type="password" value="" data-validate="{ required: true, help: 'New password is required again.' }" autocomplete="off" spellcheck="false" required="required"></td></tr></tbody></table></div><div class="action"><button class="default" onclick="if (FW.Validate(this)) if (password_verify()) FW.Postback(this); return false;" type="submit">Set Password</button></div></form>

中<tbody><tr><th><label for="p1" data-required="1">New Password</label></th><td><input class="expanded" id="p1" maxlength="32" name="p1" oninput="password_check();" size="32" type="password" value="" data-validate="{ required: true, help: 'New password is required.' }" autofocus="autofocus" autocomplete="off" spellcheck="false" required="required"></td></tr><tr><th><label for="p2" data-required="1">New Password (again)</label></th><td><input class="expanded" id="p2" maxlength="32" name="p2" oninput="password_check();" size="32" type="password" value="" data-validate="{ required: true, help: 'New password is required again.' }" autocomplete="off" spellcheck="false" required="required"></td></tr></tbody>
密码统一设置为 Huangkun729.（注意有一个点）

然后点击<div class="action"><button class="default" onclick="if (FW.Validate(this)) if (password_verify()) FW.Postback(this); return false;" type="submit">Set Password</button></div>
设置按钮
再次监听页面元素，往下滑，找到<p style="text-align: center;"><a href="//" id="start_application_link" onclick="return FW.Lazy.Popup(this);" data-href="?cmd=detail">Start New Application</a></p>
点击Start New Application后
会出现
<div style="width: 500px;"><form action="https://apply.osuokc.edu/apply/?cmd=detail" method="post" onsubmit="return false;" novalidate="novalidate" data-fw-form="1" autocomplete="off"><input name="id" type="hidden" value="80a13d66-a073-42a1-95ee-f785cdf20170"><div class="header ui-draggable-handle dialog_closeable">Start New Application<div class="dialog_close" aria-label="Close" role="button" aria-pressed="false" title="Close" data-clickable="1" tabindex="0"></div></div><div class="content" style="height: 200px;"><div style="padding: 15px 0 0 15px;"><strong></strong><p>Select an application type:</p><div style="margin: 15px 0 15px 0;"><select aria-label="Application Type" id="period" size="1" data-validate="{ required: true, help: 'Select an application type.' }" onchange="var val = $(this).val(); $('.period').showAndEnable(false); $('.period_' + val).showAndEnable(true);" autocomplete="off" required="required"><option></option><option value="5a01843c-8961-4f31-9792-58fc2b9e3e5b">2026 Applications</option><option value="1f8afb2d-cc3b-466f-90b9-85368ef45f1f">2025 Applications</option></select></div><div class="period_5a01843c-8961-4f31-9792-58fc2b9e3e5b period hidden" style="margin-left: 25px;" aria-hidden="true"><select aria-label="Application Subtype" id="round" name="round" size="1" data-validate="{ required: true, help: 'Select an application type.' }" disabled="" autocomplete="off" required="required"><option></option><option value="8ebb4bf2-88da-439f-8941-d0ddb617116c">Fall 2026 Application</option><option value="f227e667-d68e-45b2-af03-22b71e9e24b6">High School Concurrent Fall 2026</option><option value="fc50e2a2-d4de-4390-a45d-2634c3836896">High School Concurrent Spring 2026</option><option value="4162b0cf-299c-434a-ada3-a84c12040eee">Spring 2026 Application</option></select></div><div class="period_1f8afb2d-cc3b-466f-90b9-85368ef45f1f period hidden" style="margin-left: 25px;" aria-hidden="true"><select aria-label="Application Subtype" id="round" name="round" size="1" data-validate="{ required: true, help: 'Select an application type.' }" disabled="" autocomplete="off" required="required"><option></option><option value="000ca4be-4342-46c7-ab68-2d2f93bb1b35">High School Concurrent Fall 2025</option><option value="aa4e1454-eb6b-418f-8676-11d47a3021f7">Fall 2025 Application</option></select></div></div></div><div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Create Application</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div></form></div> 弹窗
我们选择2026 Applications 
选择后会出现另一个复选框（同页面），<div class="period_5a01843c-8961-4f31-9792-58fc2b9e3e5b period" style="margin-left: 25px;" aria-hidden="false"><select aria-label="Application Subtype" id="round" name="round" size="1" data-validate="{ required: true, help: 'Select an application type.' }" autocomplete="off" required="required"><option></option><option value="8ebb4bf2-88da-439f-8941-d0ddb617116c">Fall 2026 Application</option><option value="f227e667-d68e-45b2-af03-22b71e9e24b6">High School Concurrent Fall 2026</option><option value="fc50e2a2-d4de-4390-a45d-2634c3836896">High School Concurrent Spring 2026</option><option value="4162b0cf-299c-434a-ada3-a84c12040eee">Spring 2026 Application</option></select></div> 
我们选择Fall 2026 Application 
然后点击<div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Create Application</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div>
中的Create Application
再次监听页面元素
<div style="width: 500px;"><form method="post" onsubmit="return false;" action="https://apply.osuokc.edu/apply/?id=80a13d66-a073-42a1-95ee-f785cdf20170&amp;cmd=detail" novalidate="novalidate" data-fw-form="1" autocomplete="off"><div class="header ui-draggable-handle dialog_closeable">Application Details<div class="dialog_close" aria-label="Close" role="button" aria-pressed="false" title="Close" data-clickable="1" tabindex="0"></div></div><div class="content" style="height: 200px;"><table class="plain" role="none"><colgroup><col style="width: 100px;"><col></colgroup><tbody><tr><th>Started</th><td>08/10/2025</td></tr><tr><th>Status</th><td>In Progress</td></tr><tr><th></th><td>2026 Applications</td></tr><tr style="vertical-align: top;"><th></th><td><input name="round_existing" type="hidden" value="8ebb4bf2-88da-439f-8941-d0ddb617116c"><input id="round_1" name="round" type="radio" value="8ebb4bf2-88da-439f-8941-d0ddb617116c" checked="checked" autocomplete="off"><label for="round_1">Fall 2026 Application</label><br><input id="round_2" name="round" type="radio" value="f227e667-d68e-45b2-af03-22b71e9e24b6" autocomplete="off"><label for="round_2">High School Concurrent Fall 2026</label><br><input id="round_3" name="round" type="radio" value="4162b0cf-299c-434a-ada3-a84c12040eee" autocomplete="off"><label for="round_3">Spring 2026 Application</label><br><input id="round_4" name="round" type="radio" value="fc50e2a2-d4de-4390-a45d-2634c3836896" autocomplete="off"><label for="round_4">High School Concurrent Spring 2026</label><br></td></tr></tbody></table></div><div class="action"><button class="default" type="button" onclick="var el = $(this).parents('form').find('input[name = &quot;round&quot;]:enabled'); if (el.length &gt; 0) if (el.filter(':checked').length == 0) { alert('Please select an application round.'); el.eq(0).focus(); return; } FW.Lazy.Commit(this, { cmd: 'save' });">Open Application</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div></form></div> 
点击<div class="action"><button class="default" type="button" onclick="var el = $(this).parents('form').find('input[name = &quot;round&quot;]:enabled'); if (el.length &gt; 0) if (el.filter(':checked').length == 0) { alert('Please select an application round.'); el.eq(0).focus(); return; } FW.Lazy.Commit(this, { cmd: 'save' });">Open Application</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div> 中的Open Application

先做这么多，后续再继续，不要关闭浏览器




在这之后我们会跳转到一个新的页面（在此代码的基础上继续），
找到<div class="form_question form_plugin:widget form_question_05674088-7978-41f7-ae2f-764704458ecf form_layout_stacked" id="form_question_05674088-7978-41f7-ae2f-764704458ecf" style="clear: left;" data-id="05674088-7978-41f7-ae2f-764704458ecf" data-type="plugin:widget"><label>Please provide your permanent and mailing addresses below.<span style="color:red">*</span></label><div class="form_responses"><table data-id="05674088-7978-41f7-ae2f-764704458ecf" class="table sortable sortable_completed"><colgroup><col><col><col><col></colgroup><thead><tr class="column"><th class="sortable_header">Type</th><th class="sortable_header">Street Address</th><th class="sortable_header">Location</th><th class="sortable_header">Country</th></tr><tr class="row_hover"><td colspan="4"><a class="widget_add" data-href="/apply/frm?cmd=widget&amp;form=99fbaab0-296b-458c-906c-07e5326ab2de&amp;field=05674088-7978-41f7-ae2f-764704458ecf&amp;scope=app_hosted_page" href="//" onclick="return FW.Lazy.Popup(this);">Add Address</a></td></tr></thead><tbody></tbody></table></div></div>
然后点击<thead><tr class="column"><th class="sortable_header">Type</th><th class="sortable_header">Street Address</th><th class="sortable_header">Location</th><th class="sortable_header">Country</th></tr><tr class="row_hover"><td colspan="4"><a class="widget_add" data-href="/apply/frm?cmd=widget&amp;form=99fbaab0-296b-458c-906c-07e5326ab2de&amp;field=05674088-7978-41f7-ae2f-764704458ecf&amp;scope=app_hosted_page" href="//" onclick="return FW.Lazy.Popup(this);">Add Address</a></td></tr></thead>中的
<tr class="row_hover"><td colspan="4"><a class="widget_add" data-href="/apply/frm?cmd=widget&amp;form=99fbaab0-296b-458c-906c-07e5326ab2de&amp;field=05674088-7978-41f7-ae2f-764704458ecf&amp;scope=app_hosted_page" href="//" onclick="return FW.Lazy.Popup(this);">Add Address</a></td></tr>
Add Address按钮

然后会出现一个弹窗<div class="dialog dialog_closeable" data-href="/apply/frm?cmd=widget&amp;form=99fbaab0-296b-458c-906c-07e5326ab2de&amp;field=05674088-7978-41f7-ae2f-764704458ecf&amp;scope=app_hosted_page" role="dialog" aria-hidden="false" aria-label="Addresses"><style></style><form action="https://apply.osuokc.edu/apply/frm?cmd=widget&amp;form=99fbaab0-296b-458c-906c-07e5326ab2de&amp;field=05674088-7978-41f7-ae2f-764704458ecf&amp;scope=app_hosted_page" enctype="multipart/form-data" method="post" data-fw-changed-attribute="dirty" style="width: 650px;" novalidate="novalidate" data-fw-form="1" autocomplete="off" data-ready="1"><div class="header ui-draggable-handle dialog_closeable">Addresses<div class="dialog_close" aria-label="Close" role="button" aria-pressed="false" title="Close" data-clickable="1" tabindex="0"></div></div><div class="content" style="height: 500px;"><div id="form_contents"><script>/*<![CDATA[*/new function($) { var renderForm = function() { var form = window['Form'] = window['form_99fbaab0-296b-458c-906c-07e5326ab2de'] = new FormFiller($('#form_page_99fbaab0-296b-458c-906c-07e5326ab2de')); try {

} catch (ex) { if (console.log) console.log(ex); } form.ready(); }; $(renderForm); }(FW.$);/*]]>*/</script><div class="form_container"><div id="form_page_99fbaab0-296b-458c-906c-07e5326ab2de"><div class="form_pages"><div id="form_page_1" data-id="99fbaab0-296b-458c-906c-07e5326ab2de" data-page="1" class="form_page form_page_1" style="clear: left;"><div class="form_question form_radio form_question_1ab06946-e773-42e2-8ea9-156c0aa3e7c2 form_layout_table" id="form_question_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" style="clear: left;" data-id="1ab06946-e773-42e2-8ea9-156c0aa3e7c2" data-type="radio" data-export="sys:address:type" data-required="1"><fieldset id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2"><legend>Type</legend><div class="form_label">Type</div><div class="form_responses"><div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_1" type="radio" data-text="Mailing Address" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="mailing" autocomplete="off" required="required"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_1">Mailing Address</label></div><div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_2" type="radio" data-text="Permanent Address" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="permanent" autocomplete="off" required="required"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_2">Permanent Address</label></div><div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_3" type="radio" data-text="Secondary Address for Residency" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="residency" autocomplete="off" required="required"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_3">Secondary Address for Residency</label></div></div></fieldset></div><div class="form_question form_address form_question_3569c020-19d2-41e9-870b-2d81b1812820 form_layout_table" id="form_question_3569c020-19d2-41e9-870b-2d81b1812820" style="clear: left;" data-id="3569c020-19d2-41e9-870b-2d81b1812820" data-type="address" data-export="sys:address_block" data-required="1"><fieldset><legend>Street Address</legend><div class="form_label">Street Address</div><div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_country">Country</label><div class="form_responses"><select id="form_3569c020-19d2-41e9-870b-2d81b1812820_country" name="form_3569c020-19d2-41e9-870b-2d81b1812820_country" size="1" autocomplete="off" required="required"><option value="AF">Afghanistan</option><option value="AX">Aland Islands</option><option value="AL">Albania</option><option value="AG">Algeria</option><option value="AN">Andorra</option><option value="AO">Angola</option><option value="AV">Anguilla</option><option value="AC">Antigua and Barbuda</option><option value="AR">Argentina</option><option value="AM">Armenia</option><option value="AA">Aruba</option><option value="AT">Ashmore and Cartier Islands</option><option value="AS">Australia</option><option value="AU">Austria</option><option value="AJ">Azerbaijan</option><option value="BF">Bahamas, The</option><option value="BA">Bahrain</option><option value="BG">Bangladesh</option><option value="BB">Barbados</option><option value="BS">Bassas Da India</option><option value="BO">Belarus</option><option value="BE">Belgium</option><option value="BH">Belize</option><option value="BN">Benin</option><option value="BD">Bermuda</option><option value="BT">Bhutan</option><option value="BL">Bolivia</option><option value="BQ">Bonaire, Sint Eustatius, and ...</option><option value="BK">Bosnia and Herzegovina</option><option value="BC">Botswana</option><option value="BV">Bouvet Island</option><option value="BR">Brazil</option><option value="IO">British Indian Ocean Territory</option><option value="VI">British Virgin Islands</option><option value="BX">Brunei</option><option value="BU">Bulgaria</option><option value="UV">Burkina Faso</option><option value="BY">Burundi</option><option value="CB">Cambodia</option><option value="CM">Cameroon</option><option value="CA">Canada</option><option value="CV">Cape Verde</option><option value="CJ">Cayman Islands</option><option value="CT">Central African Republic</option><option value="CD">Chad</option><option value="CI">Chile</option><option value="CH">China</option><option value="KT">Christmas Island</option><option value="IP">Clipperton Island</option><option value="CK">Cocos Islands (Keeling Islands)</option><option value="CO">Colombia</option><option value="CN">Comoros</option><option value="CF">Congo (Brazzaville)</option><option value="CG">Congo (Kinshasa)</option><option value="CW">Cook Islands</option><option value="CR">Coral Sea Islands</option><option value="CS">Costa Rica</option><option value="IV">Cote D'Ivoire</option><option value="HR">Croatia</option><option value="CU">Cuba</option><option value="UC">Curacao</option><option value="CY">Cyprus</option><option value="EZ">Czech Republic</option><option value="DA">Denmark</option><option value="DJ">Djibouti</option><option value="DO">Dominica</option><option value="DR">Dominican Republic</option><option value="EC">Ecuador</option><option value="EG">Egypt</option><option value="ES">El Salvador</option><option value="EK">Equatorial Guinea</option><option value="ER">Eritrea</option><option value="EN">Estonia</option><option value="WZ">Eswatini</option><option value="ET">Ethiopia</option><option value="EU">Europa Island</option><option value="FK">Falkland Islands (Islas Malvi...</option><option value="FO">Faroe Islands</option><option value="FM">Federated States of Micronesia</option><option value="FJ">Fiji</option><option value="FI">Finland</option><option value="FR">France</option><option value="FG">French Guiana</option><option value="FP">French Polynesia</option><option value="FS">French Southern and Antarctic...</option><option value="GB">Gabon</option><option value="GA">Gambia, The</option><option value="GG">Georgia</option><option value="GM">Germany</option><option value="GH">Ghana</option><option value="GI">Gibraltar</option><option value="GO">Glorioso Islands</option><option value="GR">Greece</option><option value="GL">Greenland</option><option value="GJ">Grenada</option><option value="GP">Guadeloupe</option><option value="GT">Guatemala</option><option value="GK">Guernsey</option><option value="GV">Guinea</option><option value="PU">Guinea-Bissau</option><option value="GY">Guyana</option><option value="HA">Haiti</option><option value="HM">Heard Island and McDonald Isl...</option><option value="HO">Honduras</option><option value="HK">Hong Kong S.A.R.</option><option value="HU">Hungary</option><option value="IC">Iceland</option><option value="IN">India</option><option value="ID">Indonesia</option><option value="IR">Iran</option><option value="IZ">Iraq</option><option value="EI">Ireland</option><option value="IS">Israel</option><option value="IT">Italy</option><option value="JM">Jamaica</option><option value="JN">Jan Mayen</option><option value="JA">Japan</option><option value="JE">Jersey</option><option value="JO">Jordan</option><option value="JU">Juan De Nova Island</option><option value="KZ">Kazakhstan</option><option value="KE">Kenya</option><option value="KR">Kiribati</option><option value="KN">Korea, North</option><option value="KS">Korea, South</option><option value="KV">Kosovo</option><option value="KU">Kuwait</option><option value="KG">Kyrgyzstan</option><option value="LA">Laos</option><option value="LG">Latvia</option><option value="LE">Lebanon</option><option value="LT">Lesotho</option><option value="LI">Liberia</option><option value="LY">Libya</option><option value="LS">Liechtenstein</option><option value="LH">Lithuania</option><option value="LU">Luxembourg</option><option value="MC">Macau S.A.R.</option><option value="MK">Macedonia, Republic of North</option><option value="MA">Madagascar</option><option value="MI">Malawi</option><option value="MY">Malaysia</option><option value="MV">Maldives</option><option value="ML">Mali</option><option value="MT">Malta</option><option value="IM">Man, Isle of</option><option value="RM">Marshall Islands</option><option value="MB">Martinique</option><option value="MR">Mauritania</option><option value="MP">Mauritius</option><option value="MF">Mayotte</option><option value="MX">Mexico</option><option value="MD">Moldova</option><option value="MN">Monaco</option><option value="MG">Mongolia</option><option value="MJ">Montenegro</option><option value="MH">Montserrat</option><option value="MO">Morocco</option><option value="MZ">Mozambique</option><option value="BM">Myanmar</option><option value="WA">Namibia</option><option value="NR">Nauru</option><option value="NP">Nepal</option><option value="NL">Netherlands</option><option value="NC">New Caledonia</option><option value="NZ">New Zealand</option><option value="NU">Nicaragua</option><option value="NG">Niger</option><option value="NI">Nigeria</option><option value="NE">Niue</option><option value="NF">Norfolk Island</option><option value="NO">Norway</option><option value="MU">Oman</option><option value="PK">Pakistan</option><option value="PS">Palau, the Pacific Islands of</option><option value="WE">Palestine</option><option value="PM">Panama</option><option value="PP">Papua New Guinea</option><option value="PA">Paraguay</option><option value="PE">Peru</option><option value="RP">Philippines</option><option value="PC">Pitcairn Islands</option><option value="PL">Poland</option><option value="PO">Portugal</option><option value="QA">Qatar</option><option value="RE">Reunion</option><option value="RO">Romania</option><option value="RS">Russia</option><option value="RW">Rwanda</option><option value="TB">Saint Barthelemy</option><option value="SH">Saint Helena</option><option value="SC">Saint Kitts and Nevis</option><option value="ST">Saint Lucia</option><option value="RN">Saint Martin</option><option value="SB">Saint Pierre and Miquelon</option><option value="VC">Saint Vincent and the Grenadines</option><option value="WS">Samoa</option><option value="SM">San Marino</option><option value="TP">Sao Tome and Principe</option><option value="SA">Saudi Arabia</option><option value="SG">Senegal</option><option value="RB">Serbia</option><option value="SE">Seychelles</option><option value="SL">Sierra Leone</option><option value="SN">Singapore</option><option value="NN">Sint Maarten</option><option value="LO">Slovakia</option><option value="SI">Slovenia</option><option value="BP">Solomon Islands</option><option value="SO">Somalia</option><option value="SF">South Africa</option><option value="SX">South Georgia and the South S...</option><option value="OD">South Sudan</option><option value="SP">Spain</option><option value="PG">Spratly Islands</option><option value="CE">Sri Lanka</option><option value="SU">Sudan</option><option value="NS">Suriname</option><option value="SV">Svalbard</option><option value="SW">Sweden</option><option value="SZ">Switzerland</option><option value="SY">Syria</option><option value="TW">Taiwan</option><option value="TI">Tajikistan</option><option value="TZ">Tanzania</option><option value="TH">Thailand</option><option value="TT">Timor-Leste</option><option value="TO">Togo</option><option value="TL">Tokelau</option><option value="TN">Tonga</option><option value="TD">Trinidad and Tobago</option><option value="TE">Tromelin Island</option><option value="TS">Tunisia</option><option value="TU">Turkey</option><option value="TX">Turkmenistan</option><option value="TK">Turks and Caicos Islands</option><option value="TV">Tuvalu</option><option value="UG">Uganda</option><option value="UP">Ukraine</option><option value="AE">United Arab Emirates</option><option value="UK">United Kingdom</option><option value="US">United States</option><option value="UY">Uruguay</option><option value="UZ">Uzbekistan</option><option value="NH">Vanuatu</option><option value="VT">Vatican City</option><option value="VE">Venezuela</option><option value="VM">Vietnam</option><option value="WF">Wallis and Futuna Islands</option><option value="WI">Western Sahara</option><option value="YM">Yemen</option><option value="ZA">Zambia</option><option value="ZI">Zimbabwe</option></select></div></div><div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_street">Street</label><div class="form_responses"><textarea cols="64" id="form_3569c020-19d2-41e9-870b-2d81b1812820_street" maxlength="256" name="form_3569c020-19d2-41e9-870b-2d81b1812820_street" rows="2" wrap="soft" spellcheck="false" autocomplete="off" class="maxlength_enabled" required="required" aria-autocomplete="list" aria-expanded="false" role="combobox" aria-owns="suggest_b5c6864d-6d1f-42a3-8241-7aebd6ef47e3" aria-controls="suggest_b5c6864d-6d1f-42a3-8241-7aebd6ef47e3"></textarea></div></div><div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_city">City</label><div class="form_responses"><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_city" maxlength="64" name="form_3569c020-19d2-41e9-870b-2d81b1812820_city" size="48" type="text" value="" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></div></div><div class="address-row"><label class="form_label" id="form_3569c020-19d2-41e9-870b-2d81b1812820_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_region" data-default="">State</label><div class="form_responses"><select id="form_3569c020-19d2-41e9-870b-2d81b1812820_region" name="form_3569c020-19d2-41e9-870b-2d81b1812820_region" size="1" style="margin-right: 10px;" autocomplete="off" required="required"><option value="">Select State</option><option value="AL">Alabama</option><option value="AK">Alaska</option><option value="AS">American Samoa</option><option value="AA">APO/FPO (AA)</option><option value="AE">APO/FPO (AE)</option><option value="AP">APO/FPO (AP)</option><option value="AZ">Arizona</option><option value="AR">Arkansas</option><option value="CA">California</option><option value="CO">Colorado</option><option value="CT">Connecticut</option><option value="DE">Delaware</option><option value="DC">District of Columbia</option><option value="FM">Federated States of Micronesia</option><option value="FL">Florida</option><option value="GA">Georgia</option><option value="GU">Guam</option><option value="HI">Hawaii</option><option value="ID">Idaho</option><option value="IL">Illinois</option><option value="IN">Indiana</option><option value="IA">Iowa</option><option value="KS">Kansas</option><option value="KY">Kentucky</option><option value="LA">Louisiana</option><option value="ME">Maine</option><option value="MP">Mariana Islands</option><option value="MH">Marshall Islands</option><option value="MD">Maryland</option><option value="MA">Massachusetts</option><option value="MI">Michigan</option><option value="MN">Minnesota</option><option value="MS">Mississippi</option><option value="MO">Missouri</option><option value="MT">Montana</option><option value="NE">Nebraska</option><option value="NV">Nevada</option><option value="NH">New Hampshire</option><option value="NJ">New Jersey</option><option value="NM">New Mexico</option><option value="NY">New York</option><option value="NC">North Carolina</option><option value="ND">North Dakota</option><option value="OH">Ohio</option><option value="OK">Oklahoma</option><option value="OR">Oregon</option><option value="PW">Palau</option><option value="PA">Pennsylvania</option><option value="PR">Puerto Rico</option><option value="RI">Rhode Island</option><option value="SC">South Carolina</option><option value="SD">South Dakota</option><option value="TN">Tennessee</option><option value="TX">Texas</option><option value="UT">Utah</option><option value="VT">Vermont</option><option value="VI">Virgin Islands, U.S.</option><option value="VA">Virginia</option><option value="WA">Washington</option><option value="WV">West Virginia</option><option value="WI">Wisconsin</option><option value="WY">Wyoming</option></select><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_region_static" type="hidden" value=""></div></div><div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_postal">Postal Code</label><div class="form_responses"><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_postal" maxlength="16" name="form_3569c020-19d2-41e9-870b-2d81b1812820_postal" type="text" value="" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></div></div><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_quality" name="form_3569c020-19d2-41e9-870b-2d81b1812820_quality" type="hidden" value=""><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_dirty" name="form_3569c020-19d2-41e9-870b-2d81b1812820_dirty" type="hidden" value="0"><script>/*<![CDATA[*/new function($) { FW.Require('address.js?v=' + FW.Version, function() { new FW.Address.Client($('#form_3569c020-19d2-41e9-870b-2d81b1812820_street'),$('#form_3569c020-19d2-41e9-870b-2d81b1812820_city'), $('#form_3569c020-19d2-41e9-870b-2d81b1812820_region'), $('#form_3569c020-19d2-41e9-870b-2d81b1812820_postal'),$('#form_3569c020-19d2-41e9-870b-2d81b1812820_country'), $('#form_3569c020-19d2-41e9-870b-2d81b1812820_label'), null, null, 'validate', null, null, $('#form_3569c020-19d2-41e9-870b-2d81b1812820_quality'), $('#form_3569c020-19d2-41e9-870b-2d81b1812820_dirty')); }); }(FW.$);/*]]>*/</script></fieldset></div></div></div></div></div><div style="clear: left;"></div></div></div><div class="action"><button class="default" onclick="if (Form.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Save</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div></form></div>
首先选中<fieldset id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2"><legend>Type</legend><div class="form_label">Type</div><div class="form_responses"><div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_1" type="radio" data-text="Mailing Address" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="mailing" autocomplete="off" required="required"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_1">Mailing Address</label></div><div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_2" type="radio" data-text="Permanent Address" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="permanent" autocomplete="off" required="required"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_2">Permanent Address</label></div><div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_3" type="radio" data-text="Secondary Address for Residency" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="residency" autocomplete="off" required="required"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_3">Secondary Address for Residency</label></div></div></fieldset>
中的<div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_1" type="radio" data-text="Mailing Address" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="mailing" autocomplete="off" required="required" data-gtm-form-interact-field-id="0"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_1">Mailing Address</label></div>
然后在下方表单中填入我们库中所需的数据，<div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_street">Street</label><div class="form_responses"><textarea cols="64" id="form_3569c020-19d2-41e9-870b-2d81b1812820_street" maxlength="256" name="form_3569c020-19d2-41e9-870b-2d81b1812820_street" rows="2" wrap="soft" spellcheck="false" autocomplete="off" class="maxlength_enabled" required="required" aria-autocomplete="list" aria-expanded="false" role="combobox" aria-owns="suggest_b5c6864d-6d1f-42a3-8241-7aebd6ef47e3" aria-controls="suggest_b5c6864d-6d1f-42a3-8241-7aebd6ef47e3"></textarea></div></div>，<div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_city">City</label><div class="form_responses"><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_city" maxlength="64" name="form_3569c020-19d2-41e9-870b-2d81b1812820_city" size="48" type="text" value="" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></div></div>，<div class="address-row"><label class="form_label" id="form_3569c020-19d2-41e9-870b-2d81b1812820_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_region" data-default="">State</label><div class="form_responses"><select id="form_3569c020-19d2-41e9-870b-2d81b1812820_region" name="form_3569c020-19d2-41e9-870b-2d81b1812820_region" size="1" style="margin-right: 10px;" autocomplete="off" required="required"><option value="">Select State</option><option value="AL">Alabama</option><option value="AK">Alaska</option><option value="AS">American Samoa</option><option value="AA">APO/FPO (AA)</option><option value="AE">APO/FPO (AE)</option><option value="AP">APO/FPO (AP)</option><option value="AZ">Arizona</option><option value="AR">Arkansas</option><option value="CA">California</option><option value="CO">Colorado</option><option value="CT">Connecticut</option><option value="DE">Delaware</option><option value="DC">District of Columbia</option><option value="FM">Federated States of Micronesia</option><option value="FL">Florida</option><option value="GA">Georgia</option><option value="GU">Guam</option><option value="HI">Hawaii</option><option value="ID">Idaho</option><option value="IL">Illinois</option><option value="IN">Indiana</option><option value="IA">Iowa</option><option value="KS">Kansas</option><option value="KY">Kentucky</option><option value="LA">Louisiana</option><option value="ME">Maine</option><option value="MP">Mariana Islands</option><option value="MH">Marshall Islands</option><option value="MD">Maryland</option><option value="MA">Massachusetts</option><option value="MI">Michigan</option><option value="MN">Minnesota</option><option value="MS">Mississippi</option><option value="MO">Missouri</option><option value="MT">Montana</option><option value="NE">Nebraska</option><option value="NV">Nevada</option><option value="NH">New Hampshire</option><option value="NJ">New Jersey</option><option value="NM">New Mexico</option><option value="NY">New York</option><option value="NC">North Carolina</option><option value="ND">North Dakota</option><option value="OH">Ohio</option><option value="OK">Oklahoma</option><option value="OR">Oregon</option><option value="PW">Palau</option><option value="PA">Pennsylvania</option><option value="PR">Puerto Rico</option><option value="RI">Rhode Island</option><option value="SC">South Carolina</option><option value="SD">South Dakota</option><option value="TN">Tennessee</option><option value="TX">Texas</option><option value="UT">Utah</option><option value="VT">Vermont</option><option value="VI">Virgin Islands, U.S.</option><option value="VA">Virginia</option><option value="WA">Washington</option><option value="WV">West Virginia</option><option value="WI">Wisconsin</option><option value="WY">Wyoming</option></select><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_region_static" type="hidden" value=""></div></div>以及<div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_postal">Postal Code</label><div class="form_responses"><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_postal" maxlength="16" name="form_3569c020-19d2-41e9-870b-2d81b1812820_postal" type="text" value="" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></div></div>
后，点击<div class="action"><button class="default" onclick="if (Form.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Save</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div> 中的<button class="default" onclick="if (Form.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Save</button> 按钮

会出现一个弹窗<div class="dialog" style="width: 500px;" role="dialog" aria-hidden="false" aria-label="Validate Address"><div class="header ui-draggable-handle">Validate Address</div><div class="content"><p class="error">We were unable to validate the address you entered.<br><span>Would you like to correct the address entered?</span></p><div style="padding-left: 35px;"><span>51418 Sporer Terrace Suite 649</span><br><span>Centre, AL 35960 </span></div></div><div class="action"><button class="default">Fix Address</button><button>Skip Validation</button></div></div>
我们选择<div class="action"><button class="default">Fix Address</button><button>Skip Validation</button></div> 中的<button>Skip Validation</button>

完成后再次监听点击<tr class="row_hover"><td colspan="4"><a class="widget_add" data-href="/apply/frm?cmd=widget&amp;form=99fbaab0-296b-458c-906c-07e5326ab2de&amp;field=05674088-7978-41f7-ae2f-764704458ecf&amp;scope=app_hosted_page" href="//" onclick="return FW.Lazy.Popup(this);">Add Address</a></td></tr>
会再次出现弹窗，<div class="dialog dialog_closeable" data-href="/apply/frm?cmd=widget&amp;form=99fbaab0-296b-458c-906c-07e5326ab2de&amp;field=05674088-7978-41f7-ae2f-764704458ecf&amp;scope=app_hosted_page" role="dialog" aria-hidden="false" aria-label="Addresses"><style></style><form action="https://apply.osuokc.edu/apply/frm?cmd=widget&amp;form=99fbaab0-296b-458c-906c-07e5326ab2de&amp;field=05674088-7978-41f7-ae2f-764704458ecf&amp;scope=app_hosted_page" enctype="multipart/form-data" method="post" data-fw-changed-attribute="dirty" style="width: 650px;" novalidate="novalidate" data-fw-form="1" autocomplete="off" data-ready="1"><div class="header ui-draggable-handle dialog_closeable">Addresses<div class="dialog_close" aria-label="Close" role="button" aria-pressed="false" title="Close" data-clickable="1" tabindex="0"></div></div><div class="content" style="height: 500px;"><div id="form_contents"><script>/*<![CDATA[*/new function($) { var renderForm = function() { var form = window['Form'] = window['form_99fbaab0-296b-458c-906c-07e5326ab2de'] = new FormFiller($('#form_page_99fbaab0-296b-458c-906c-07e5326ab2de')); try {

} catch (ex) { if (console.log) console.log(ex); } form.ready(); }; $(renderForm); }(FW.$);/*]]>*/</script><div class="form_container"><div id="form_page_99fbaab0-296b-458c-906c-07e5326ab2de"><div class="form_pages"><div id="form_page_1" data-id="99fbaab0-296b-458c-906c-07e5326ab2de" data-page="1" class="form_page form_page_1" style="clear: left;"><div class="form_question form_radio form_question_1ab06946-e773-42e2-8ea9-156c0aa3e7c2 form_layout_table" id="form_question_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" style="clear: left;" data-id="1ab06946-e773-42e2-8ea9-156c0aa3e7c2" data-type="radio" data-export="sys:address:type" data-required="1"><fieldset id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2"><legend>Type</legend><div class="form_label">Type</div><div class="form_responses"><div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_1" type="radio" data-text="Mailing Address" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="mailing" autocomplete="off" required="required"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_1">Mailing Address</label></div><div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_2" type="radio" data-text="Permanent Address" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="permanent" autocomplete="off" required="required"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_2">Permanent Address</label></div><div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_3" type="radio" data-text="Secondary Address for Residency" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="residency" autocomplete="off" required="required"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_3">Secondary Address for Residency</label></div></div></fieldset></div><div class="form_question form_address form_question_3569c020-19d2-41e9-870b-2d81b1812820 form_layout_table" id="form_question_3569c020-19d2-41e9-870b-2d81b1812820" style="clear: left;" data-id="3569c020-19d2-41e9-870b-2d81b1812820" data-type="address" data-export="sys:address_block" data-required="1"><fieldset><legend>Street Address</legend><div class="form_label">Street Address</div><div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_country">Country</label><div class="form_responses"><select id="form_3569c020-19d2-41e9-870b-2d81b1812820_country" name="form_3569c020-19d2-41e9-870b-2d81b1812820_country" size="1" autocomplete="off" required="required"><option value="AF">Afghanistan</option><option value="AX">Aland Islands</option><option value="AL">Albania</option><option value="AG">Algeria</option><option value="AN">Andorra</option><option value="AO">Angola</option><option value="AV">Anguilla</option><option value="AC">Antigua and Barbuda</option><option value="AR">Argentina</option><option value="AM">Armenia</option><option value="AA">Aruba</option><option value="AT">Ashmore and Cartier Islands</option><option value="AS">Australia</option><option value="AU">Austria</option><option value="AJ">Azerbaijan</option><option value="BF">Bahamas, The</option><option value="BA">Bahrain</option><option value="BG">Bangladesh</option><option value="BB">Barbados</option><option value="BS">Bassas Da India</option><option value="BO">Belarus</option><option value="BE">Belgium</option><option value="BH">Belize</option><option value="BN">Benin</option><option value="BD">Bermuda</option><option value="BT">Bhutan</option><option value="BL">Bolivia</option><option value="BQ">Bonaire, Sint Eustatius, and ...</option><option value="BK">Bosnia and Herzegovina</option><option value="BC">Botswana</option><option value="BV">Bouvet Island</option><option value="BR">Brazil</option><option value="IO">British Indian Ocean Territory</option><option value="VI">British Virgin Islands</option><option value="BX">Brunei</option><option value="BU">Bulgaria</option><option value="UV">Burkina Faso</option><option value="BY">Burundi</option><option value="CB">Cambodia</option><option value="CM">Cameroon</option><option value="CA">Canada</option><option value="CV">Cape Verde</option><option value="CJ">Cayman Islands</option><option value="CT">Central African Republic</option><option value="CD">Chad</option><option value="CI">Chile</option><option value="CH">China</option><option value="KT">Christmas Island</option><option value="IP">Clipperton Island</option><option value="CK">Cocos Islands (Keeling Islands)</option><option value="CO">Colombia</option><option value="CN">Comoros</option><option value="CF">Congo (Brazzaville)</option><option value="CG">Congo (Kinshasa)</option><option value="CW">Cook Islands</option><option value="CR">Coral Sea Islands</option><option value="CS">Costa Rica</option><option value="IV">Cote D'Ivoire</option><option value="HR">Croatia</option><option value="CU">Cuba</option><option value="UC">Curacao</option><option value="CY">Cyprus</option><option value="EZ">Czech Republic</option><option value="DA">Denmark</option><option value="DJ">Djibouti</option><option value="DO">Dominica</option><option value="DR">Dominican Republic</option><option value="EC">Ecuador</option><option value="EG">Egypt</option><option value="ES">El Salvador</option><option value="EK">Equatorial Guinea</option><option value="ER">Eritrea</option><option value="EN">Estonia</option><option value="WZ">Eswatini</option><option value="ET">Ethiopia</option><option value="EU">Europa Island</option><option value="FK">Falkland Islands (Islas Malvi...</option><option value="FO">Faroe Islands</option><option value="FM">Federated States of Micronesia</option><option value="FJ">Fiji</option><option value="FI">Finland</option><option value="FR">France</option><option value="FG">French Guiana</option><option value="FP">French Polynesia</option><option value="FS">French Southern and Antarctic...</option><option value="GB">Gabon</option><option value="GA">Gambia, The</option><option value="GG">Georgia</option><option value="GM">Germany</option><option value="GH">Ghana</option><option value="GI">Gibraltar</option><option value="GO">Glorioso Islands</option><option value="GR">Greece</option><option value="GL">Greenland</option><option value="GJ">Grenada</option><option value="GP">Guadeloupe</option><option value="GT">Guatemala</option><option value="GK">Guernsey</option><option value="GV">Guinea</option><option value="PU">Guinea-Bissau</option><option value="GY">Guyana</option><option value="HA">Haiti</option><option value="HM">Heard Island and McDonald Isl...</option><option value="HO">Honduras</option><option value="HK">Hong Kong S.A.R.</option><option value="HU">Hungary</option><option value="IC">Iceland</option><option value="IN">India</option><option value="ID">Indonesia</option><option value="IR">Iran</option><option value="IZ">Iraq</option><option value="EI">Ireland</option><option value="IS">Israel</option><option value="IT">Italy</option><option value="JM">Jamaica</option><option value="JN">Jan Mayen</option><option value="JA">Japan</option><option value="JE">Jersey</option><option value="JO">Jordan</option><option value="JU">Juan De Nova Island</option><option value="KZ">Kazakhstan</option><option value="KE">Kenya</option><option value="KR">Kiribati</option><option value="KN">Korea, North</option><option value="KS">Korea, South</option><option value="KV">Kosovo</option><option value="KU">Kuwait</option><option value="KG">Kyrgyzstan</option><option value="LA">Laos</option><option value="LG">Latvia</option><option value="LE">Lebanon</option><option value="LT">Lesotho</option><option value="LI">Liberia</option><option value="LY">Libya</option><option value="LS">Liechtenstein</option><option value="LH">Lithuania</option><option value="LU">Luxembourg</option><option value="MC">Macau S.A.R.</option><option value="MK">Macedonia, Republic of North</option><option value="MA">Madagascar</option><option value="MI">Malawi</option><option value="MY">Malaysia</option><option value="MV">Maldives</option><option value="ML">Mali</option><option value="MT">Malta</option><option value="IM">Man, Isle of</option><option value="RM">Marshall Islands</option><option value="MB">Martinique</option><option value="MR">Mauritania</option><option value="MP">Mauritius</option><option value="MF">Mayotte</option><option value="MX">Mexico</option><option value="MD">Moldova</option><option value="MN">Monaco</option><option value="MG">Mongolia</option><option value="MJ">Montenegro</option><option value="MH">Montserrat</option><option value="MO">Morocco</option><option value="MZ">Mozambique</option><option value="BM">Myanmar</option><option value="WA">Namibia</option><option value="NR">Nauru</option><option value="NP">Nepal</option><option value="NL">Netherlands</option><option value="NC">New Caledonia</option><option value="NZ">New Zealand</option><option value="NU">Nicaragua</option><option value="NG">Niger</option><option value="NI">Nigeria</option><option value="NE">Niue</option><option value="NF">Norfolk Island</option><option value="NO">Norway</option><option value="MU">Oman</option><option value="PK">Pakistan</option><option value="PS">Palau, the Pacific Islands of</option><option value="WE">Palestine</option><option value="PM">Panama</option><option value="PP">Papua New Guinea</option><option value="PA">Paraguay</option><option value="PE">Peru</option><option value="RP">Philippines</option><option value="PC">Pitcairn Islands</option><option value="PL">Poland</option><option value="PO">Portugal</option><option value="QA">Qatar</option><option value="RE">Reunion</option><option value="RO">Romania</option><option value="RS">Russia</option><option value="RW">Rwanda</option><option value="TB">Saint Barthelemy</option><option value="SH">Saint Helena</option><option value="SC">Saint Kitts and Nevis</option><option value="ST">Saint Lucia</option><option value="RN">Saint Martin</option><option value="SB">Saint Pierre and Miquelon</option><option value="VC">Saint Vincent and the Grenadines</option><option value="WS">Samoa</option><option value="SM">San Marino</option><option value="TP">Sao Tome and Principe</option><option value="SA">Saudi Arabia</option><option value="SG">Senegal</option><option value="RB">Serbia</option><option value="SE">Seychelles</option><option value="SL">Sierra Leone</option><option value="SN">Singapore</option><option value="NN">Sint Maarten</option><option value="LO">Slovakia</option><option value="SI">Slovenia</option><option value="BP">Solomon Islands</option><option value="SO">Somalia</option><option value="SF">South Africa</option><option value="SX">South Georgia and the South S...</option><option value="OD">South Sudan</option><option value="SP">Spain</option><option value="PG">Spratly Islands</option><option value="CE">Sri Lanka</option><option value="SU">Sudan</option><option value="NS">Suriname</option><option value="SV">Svalbard</option><option value="SW">Sweden</option><option value="SZ">Switzerland</option><option value="SY">Syria</option><option value="TW">Taiwan</option><option value="TI">Tajikistan</option><option value="TZ">Tanzania</option><option value="TH">Thailand</option><option value="TT">Timor-Leste</option><option value="TO">Togo</option><option value="TL">Tokelau</option><option value="TN">Tonga</option><option value="TD">Trinidad and Tobago</option><option value="TE">Tromelin Island</option><option value="TS">Tunisia</option><option value="TU">Turkey</option><option value="TX">Turkmenistan</option><option value="TK">Turks and Caicos Islands</option><option value="TV">Tuvalu</option><option value="UG">Uganda</option><option value="UP">Ukraine</option><option value="AE">United Arab Emirates</option><option value="UK">United Kingdom</option><option value="US">United States</option><option value="UY">Uruguay</option><option value="UZ">Uzbekistan</option><option value="NH">Vanuatu</option><option value="VT">Vatican City</option><option value="VE">Venezuela</option><option value="VM">Vietnam</option><option value="WF">Wallis and Futuna Islands</option><option value="WI">Western Sahara</option><option value="YM">Yemen</option><option value="ZA">Zambia</option><option value="ZI">Zimbabwe</option></select></div></div><div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_street">Street</label><div class="form_responses"><textarea cols="64" id="form_3569c020-19d2-41e9-870b-2d81b1812820_street" maxlength="256" name="form_3569c020-19d2-41e9-870b-2d81b1812820_street" rows="2" wrap="soft" spellcheck="false" autocomplete="off" aria-autocomplete="list" aria-expanded="false" role="combobox" aria-owns="suggest_f31d485f-dfeb-41c8-bbf5-f139db8182b8" aria-controls="suggest_f31d485f-dfeb-41c8-bbf5-f139db8182b8" class="maxlength_enabled" required="required"></textarea></div></div><div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_city">City</label><div class="form_responses"><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_city" maxlength="64" name="form_3569c020-19d2-41e9-870b-2d81b1812820_city" size="48" type="text" value="" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></div></div><div class="address-row"><label class="form_label" id="form_3569c020-19d2-41e9-870b-2d81b1812820_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_region" data-default="">State</label><div class="form_responses"><select id="form_3569c020-19d2-41e9-870b-2d81b1812820_region" name="form_3569c020-19d2-41e9-870b-2d81b1812820_region" size="1" style="margin-right: 10px;" autocomplete="off" required="required"><option value="">Select State</option><option value="AL">Alabama</option><option value="AK">Alaska</option><option value="AS">American Samoa</option><option value="AA">APO/FPO (AA)</option><option value="AE">APO/FPO (AE)</option><option value="AP">APO/FPO (AP)</option><option value="AZ">Arizona</option><option value="AR">Arkansas</option><option value="CA">California</option><option value="CO">Colorado</option><option value="CT">Connecticut</option><option value="DE">Delaware</option><option value="DC">District of Columbia</option><option value="FM">Federated States of Micronesia</option><option value="FL">Florida</option><option value="GA">Georgia</option><option value="GU">Guam</option><option value="HI">Hawaii</option><option value="ID">Idaho</option><option value="IL">Illinois</option><option value="IN">Indiana</option><option value="IA">Iowa</option><option value="KS">Kansas</option><option value="KY">Kentucky</option><option value="LA">Louisiana</option><option value="ME">Maine</option><option value="MP">Mariana Islands</option><option value="MH">Marshall Islands</option><option value="MD">Maryland</option><option value="MA">Massachusetts</option><option value="MI">Michigan</option><option value="MN">Minnesota</option><option value="MS">Mississippi</option><option value="MO">Missouri</option><option value="MT">Montana</option><option value="NE">Nebraska</option><option value="NV">Nevada</option><option value="NH">New Hampshire</option><option value="NJ">New Jersey</option><option value="NM">New Mexico</option><option value="NY">New York</option><option value="NC">North Carolina</option><option value="ND">North Dakota</option><option value="OH">Ohio</option><option value="OK">Oklahoma</option><option value="OR">Oregon</option><option value="PW">Palau</option><option value="PA">Pennsylvania</option><option value="PR">Puerto Rico</option><option value="RI">Rhode Island</option><option value="SC">South Carolina</option><option value="SD">South Dakota</option><option value="TN">Tennessee</option><option value="TX">Texas</option><option value="UT">Utah</option><option value="VT">Vermont</option><option value="VI">Virgin Islands, U.S.</option><option value="VA">Virginia</option><option value="WA">Washington</option><option value="WV">West Virginia</option><option value="WI">Wisconsin</option><option value="WY">Wyoming</option></select><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_region_static" type="hidden" value=""></div></div><div><label class="form_label" for="form_3569c020-19d2-41e9-870b-2d81b1812820_postal">Postal Code</label><div class="form_responses"><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_postal" maxlength="16" name="form_3569c020-19d2-41e9-870b-2d81b1812820_postal" type="text" value="" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></div></div><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_quality" name="form_3569c020-19d2-41e9-870b-2d81b1812820_quality" type="hidden" value=""><input id="form_3569c020-19d2-41e9-870b-2d81b1812820_dirty" name="form_3569c020-19d2-41e9-870b-2d81b1812820_dirty" type="hidden" value="0"><script>/*<![CDATA[*/new function($) { FW.Require('address.js?v=' + FW.Version, function() { new FW.Address.Client($('#form_3569c020-19d2-41e9-870b-2d81b1812820_street'),$('#form_3569c020-19d2-41e9-870b-2d81b1812820_city'), $('#form_3569c020-19d2-41e9-870b-2d81b1812820_region'), $('#form_3569c020-19d2-41e9-870b-2d81b1812820_postal'),$('#form_3569c020-19d2-41e9-870b-2d81b1812820_country'), $('#form_3569c020-19d2-41e9-870b-2d81b1812820_label'), null, null, 'validate', null, null, $('#form_3569c020-19d2-41e9-870b-2d81b1812820_quality'), $('#form_3569c020-19d2-41e9-870b-2d81b1812820_dirty')); }); }(FW.$);/*]]>*/</script></fieldset></div></div></div></div></div><div style="clear: left;"></div></div></div><div class="action"><button class="default" onclick="if (Form.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Save</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div></form></div>
这次我们选择<div class="form_response"><input id="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_2" type="radio" data-text="Permanent Address" name="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2" value="permanent" autocomplete="off" required="required" data-gtm-form-interact-field-id="5"><label for="form_1ab06946-e773-42e2-8ea9-156c0aa3e7c2_2">Permanent Address</label></div>
再按相同的方法完成一次上述步骤

然后监听
下滑找到<div class="form_question form_text form_question_782d4000-f89e-4bbc-b634-e858e7129d39 form_layout_table" id="form_question_782d4000-f89e-4bbc-b634-e858e7129d39" style="clear: left;" data-id="782d4000-f89e-4bbc-b634-e858e7129d39" data-type="text" data-export="sys:phone" data-datatype="tel"><label class="form_label" for="form_782d4000-f89e-4bbc-b634-e858e7129d39">Home</label><div class="form_responses"><input type="tel" size="24" id="form_782d4000-f89e-4bbc-b634-e858e7129d39" name="form_782d4000-f89e-4bbc-b634-e858e7129d39" value="" onchange="FW.Phone.Validate(this);" autocomplete="off" spellcheck="false" aria-invalid="false"></div></div>
以及
<div class="form_question form_text form_question_9b26e3f3-b9a5-41b6-b182-4075a9796da8 form_layout_table" id="form_question_9b26e3f3-b9a5-41b6-b182-4075a9796da8" style="clear: left;" data-id="9b26e3f3-b9a5-41b6-b182-4075a9796da8" data-type="text" data-export="sys:mobile" data-datatype="tel"><label class="form_label" for="form_9b26e3f3-b9a5-41b6-b182-4075a9796da8">Cell</label><div class="form_responses"><input type="tel" size="24" id="form_9b26e3f3-b9a5-41b6-b182-4075a9796da8" name="form_9b26e3f3-b9a5-41b6-b182-4075a9796da8" value="" onchange="FW.Phone.Validate(this);" autocomplete="off" spellcheck="false" aria-invalid="false"></div></div>

填入在线接收免费美国短信 +1(254)249-2894
在线接收免费美国短信 +1(254)249-2895
在线接收免费加拿大短信 +1(647)812-8893
在线接收免费加拿大短信 +1(289)427-8884 中的随机一个号码，注意是两个文本框填写同一个
然后<div class="form_question form_select form_question_57e3fec1-4310-409f-b785-f8d54d561ebf form_layout_stacked" id="form_question_57e3fec1-4310-409f-b785-f8d54d561ebf" style="clear: left;" data-id="57e3fec1-4310-409f-b785-f8d54d561ebf" data-type="select" data-export="sys:field:text"><label class="form_label" for="form_57e3fec1-4310-409f-b785-f8d54d561ebf">Do you allow OSU-OKC to contact you via text message regarding important college information?</label><div class="form_responses"><select size="1" id="form_57e3fec1-4310-409f-b785-f8d54d561ebf" name="form_57e3fec1-4310-409f-b785-f8d54d561ebf" autocomplete="off"><option></option><option value="1" data-text="Yes">Yes</option><option value="0" data-text="No">No</option></select></div></div> 选择No
然后<div class="form_question form_select form_question_16584d8b-9358-48c1-924d-6c81a75dcd3d form_layout_table" id="form_question_16584d8b-9358-48c1-924d-6c81a75dcd3d" style="clear: left;" data-id="16584d8b-9358-48c1-924d-6c81a75dcd3d" data-type="select" data-export="sys:sex"><label class="form_label" for="form_16584d8b-9358-48c1-924d-6c81a75dcd3d">Gender<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_16584d8b-9358-48c1-924d-6c81a75dcd3d" name="form_16584d8b-9358-48c1-924d-6c81a75dcd3d" autocomplete="off"><option></option><option value="F" data-text="Female">Female</option><option value="M" data-text="Male">Male</option></select></div></div> 从库中选择性别，然后<div class="form_question form_select form_question_5c8dc896-17fe-4964-97d8-3d41292ffb40 form_layout_table" id="form_question_5c8dc896-17fe-4964-97d8-3d41292ffb40" style="clear: left;" data-id="5c8dc896-17fe-4964-97d8-3d41292ffb40" data-type="select" data-export="sys:field:birthnation"><label class="form_label" for="form_5c8dc896-17fe-4964-97d8-3d41292ffb40">Birth Country<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_5c8dc896-17fe-4964-97d8-3d41292ffb40" name="form_5c8dc896-17fe-4964-97d8-3d41292ffb40" autocomplete="off" data-handler-change="1"><option></option><option value="AF" data-text="Afghanistan">Afghanistan</option><option value="AX" data-text="Aland Islands">Aland Islands</option><option value="AL" data-text="Albania">Albania</option><option value="AG" data-text="Algeria">Algeria</option><option value="AN" data-text="Andorra">Andorra</option><option value="AO" data-text="Angola">Angola</option><option value="AV" data-text="Anguilla">Anguilla</option><option value="AC" data-text="Antigua and Barbuda">Antigua and Barbuda</option><option value="AR" data-text="Argentina">Argentina</option><option value="AM" data-text="Armenia">Armenia</option><option value="AA" data-text="Aruba">Aruba</option><option value="AT" data-text="Ashmore and Cartier Islands">Ashmore and Cartier Islands</option><option value="AS" data-text="Australia">Australia</option><option value="AU" data-text="Austria">Austria</option><option value="AJ" data-text="Azerbaijan">Azerbaijan</option><option value="BF" data-text="Bahamas, The">Bahamas, The</option><option value="BA" data-text="Bahrain">Bahrain</option><option value="BG" data-text="Bangladesh">Bangladesh</option><option value="BB" data-text="Barbados">Barbados</option><option value="BS" data-text="Bassas Da India">Bassas Da India</option><option value="BO" data-text="Belarus">Belarus</option><option value="BE" data-text="Belgium">Belgium</option><option value="BH" data-text="Belize">Belize</option><option value="BN" data-text="Benin">Benin</option><option value="BD" data-text="Bermuda">Bermuda</option><option value="BT" data-text="Bhutan">Bhutan</option><option value="BL" data-text="Bolivia">Bolivia</option><option value="BQ" data-text="Bonaire, Sint Eustatius, and Saba">Bonaire, Sint Eustatius, and Saba</option><option value="BK" data-text="Bosnia and Herzegovina">Bosnia and Herzegovina</option><option value="BC" data-text="Botswana">Botswana</option><option value="BV" data-text="Bouvet Island">Bouvet Island</option><option value="BR" data-text="Brazil">Brazil</option><option value="IO" data-text="British Indian Ocean Territory">British Indian Ocean Territory</option><option value="VI" data-text="British Virgin Islands">British Virgin Islands</option><option value="BX" data-text="Brunei">Brunei</option><option value="BU" data-text="Bulgaria">Bulgaria</option><option value="UV" data-text="Burkina Faso">Burkina Faso</option><option value="BY" data-text="Burundi">Burundi</option><option value="CB" data-text="Cambodia">Cambodia</option><option value="CM" data-text="Cameroon">Cameroon</option><option value="CA" data-text="Canada">Canada</option><option value="CV" data-text="Cape Verde">Cape Verde</option><option value="CJ" data-text="Cayman Islands">Cayman Islands</option><option value="CT" data-text="Central African Republic">Central African Republic</option><option value="CD" data-text="Chad">Chad</option><option value="CI" data-text="Chile">Chile</option><option value="CH" data-text="China">China</option><option value="KT" data-text="Christmas Island">Christmas Island</option><option value="IP" data-text="Clipperton Island">Clipperton Island</option><option value="CK" data-text="Cocos Islands (Keeling Islands)">Cocos Islands (Keeling Islands)</option><option value="CO" data-text="Colombia">Colombia</option><option value="CN" data-text="Comoros">Comoros</option><option value="CF" data-text="Congo (Brazzaville)">Congo (Brazzaville)</option><option value="CG" data-text="Congo (Kinshasa)">Congo (Kinshasa)</option><option value="CW" data-text="Cook Islands">Cook Islands</option><option value="CR" data-text="Coral Sea Islands">Coral Sea Islands</option><option value="CS" data-text="Costa Rica">Costa Rica</option><option value="IV" data-text="Cote D'Ivoire">Cote D'Ivoire</option><option value="HR" data-text="Croatia">Croatia</option><option value="CU" data-text="Cuba">Cuba</option><option value="UC" data-text="Curacao">Curacao</option><option value="CY" data-text="Cyprus">Cyprus</option><option value="EZ" data-text="Czech Republic">Czech Republic</option><option value="DA" data-text="Denmark">Denmark</option><option value="DJ" data-text="Djibouti">Djibouti</option><option value="DO" data-text="Dominica">Dominica</option><option value="DR" data-text="Dominican Republic">Dominican Republic</option><option value="EC" data-text="Ecuador">Ecuador</option><option value="EG" data-text="Egypt">Egypt</option><option value="ES" data-text="El Salvador">El Salvador</option><option value="EK" data-text="Equatorial Guinea">Equatorial Guinea</option><option value="ER" data-text="Eritrea">Eritrea</option><option value="EN" data-text="Estonia">Estonia</option><option value="WZ" data-text="Eswatini">Eswatini</option><option value="ET" data-text="Ethiopia">Ethiopia</option><option value="EU" data-text="Europa Island">Europa Island</option><option value="FK" data-text="Falkland Islands (Islas Malvinas)">Falkland Islands (Islas Malvinas)</option><option value="FO" data-text="Faroe Islands">Faroe Islands</option><option value="FM" data-text="Federated States of Micronesia">Federated States of Micronesia</option><option value="FJ" data-text="Fiji">Fiji</option><option value="FI" data-text="Finland">Finland</option><option value="FR" data-text="France">France</option><option value="FG" data-text="French Guiana">French Guiana</option><option value="FP" data-text="French Polynesia">French Polynesia</option><option value="FS" data-text="French Southern and Antarctic Lands">French Southern and Antarctic Lands</option><option value="GB" data-text="Gabon">Gabon</option><option value="GA" data-text="Gambia, The">Gambia, The</option><option value="GG" data-text="Georgia">Georgia</option><option value="GM" data-text="Germany">Germany</option><option value="GH" data-text="Ghana">Ghana</option><option value="GI" data-text="Gibraltar">Gibraltar</option><option value="GO" data-text="Glorioso Islands">Glorioso Islands</option><option value="GR" data-text="Greece">Greece</option><option value="GL" data-text="Greenland">Greenland</option><option value="GJ" data-text="Grenada">Grenada</option><option value="GP" data-text="Guadeloupe">Guadeloupe</option><option value="GT" data-text="Guatemala">Guatemala</option><option value="GK" data-text="Guernsey">Guernsey</option><option value="GV" data-text="Guinea">Guinea</option><option value="PU" data-text="Guinea-Bissau">Guinea-Bissau</option><option value="GY" data-text="Guyana">Guyana</option><option value="HA" data-text="Haiti">Haiti</option><option value="HM" data-text="Heard Island and McDonald Islands">Heard Island and McDonald Islands</option><option value="HO" data-text="Honduras">Honduras</option><option value="HK" data-text="Hong Kong S.A.R.">Hong Kong S.A.R.</option><option value="HU" data-text="Hungary">Hungary</option><option value="IC" data-text="Iceland">Iceland</option><option value="IN" data-text="India">India</option><option value="ID" data-text="Indonesia">Indonesia</option><option value="IR" data-text="Iran">Iran</option><option value="IZ" data-text="Iraq">Iraq</option><option value="EI" data-text="Ireland">Ireland</option><option value="IS" data-text="Israel">Israel</option><option value="IT" data-text="Italy">Italy</option><option value="JM" data-text="Jamaica">Jamaica</option><option value="JN" data-text="Jan Mayen">Jan Mayen</option><option value="JA" data-text="Japan">Japan</option><option value="JE" data-text="Jersey">Jersey</option><option value="JO" data-text="Jordan">Jordan</option><option value="JU" data-text="Juan De Nova Island">Juan De Nova Island</option><option value="KZ" data-text="Kazakhstan">Kazakhstan</option><option value="KE" data-text="Kenya">Kenya</option><option value="KR" data-text="Kiribati">Kiribati</option><option value="KN" data-text="Korea, North">Korea, North</option><option value="KS" data-text="Korea, South">Korea, South</option><option value="KV" data-text="Kosovo">Kosovo</option><option value="KU" data-text="Kuwait">Kuwait</option><option value="KG" data-text="Kyrgyzstan">Kyrgyzstan</option><option value="LA" data-text="Laos">Laos</option><option value="LG" data-text="Latvia">Latvia</option><option value="LE" data-text="Lebanon">Lebanon</option><option value="LT" data-text="Lesotho">Lesotho</option><option value="LI" data-text="Liberia">Liberia</option><option value="LY" data-text="Libya">Libya</option><option value="LS" data-text="Liechtenstein">Liechtenstein</option><option value="LH" data-text="Lithuania">Lithuania</option><option value="LU" data-text="Luxembourg">Luxembourg</option><option value="MC" data-text="Macau S.A.R.">Macau S.A.R.</option><option value="MK" data-text="Macedonia, Republic of North">Macedonia, Republic of North</option><option value="MA" data-text="Madagascar">Madagascar</option><option value="MI" data-text="Malawi">Malawi</option><option value="MY" data-text="Malaysia">Malaysia</option><option value="MV" data-text="Maldives">Maldives</option><option value="ML" data-text="Mali">Mali</option><option value="MT" data-text="Malta">Malta</option><option value="IM" data-text="Man, Isle of">Man, Isle of</option><option value="RM" data-text="Marshall Islands">Marshall Islands</option><option value="MB" data-text="Martinique">Martinique</option><option value="MR" data-text="Mauritania">Mauritania</option><option value="MP" data-text="Mauritius">Mauritius</option><option value="MF" data-text="Mayotte">Mayotte</option><option value="MX" data-text="Mexico">Mexico</option><option value="MD" data-text="Moldova">Moldova</option><option value="MN" data-text="Monaco">Monaco</option><option value="MG" data-text="Mongolia">Mongolia</option><option value="MJ" data-text="Montenegro">Montenegro</option><option value="MH" data-text="Montserrat">Montserrat</option><option value="MO" data-text="Morocco">Morocco</option><option value="MZ" data-text="Mozambique">Mozambique</option><option value="BM" data-text="Myanmar">Myanmar</option><option value="WA" data-text="Namibia">Namibia</option><option value="NR" data-text="Nauru">Nauru</option><option value="NP" data-text="Nepal">Nepal</option><option value="NL" data-text="Netherlands">Netherlands</option><option value="NC" data-text="New Caledonia">New Caledonia</option><option value="NZ" data-text="New Zealand">New Zealand</option><option value="NU" data-text="Nicaragua">Nicaragua</option><option value="NG" data-text="Niger">Niger</option><option value="NI" data-text="Nigeria">Nigeria</option><option value="NE" data-text="Niue">Niue</option><option value="NF" data-text="Norfolk Island">Norfolk Island</option><option value="NO" data-text="Norway">Norway</option><option value="MU" data-text="Oman">Oman</option><option value="PK" data-text="Pakistan">Pakistan</option><option value="PS" data-text="Palau, the Pacific Islands of">Palau, the Pacific Islands of</option><option value="WE" data-text="Palestine">Palestine</option><option value="PM" data-text="Panama">Panama</option><option value="PP" data-text="Papua New Guinea">Papua New Guinea</option><option value="PA" data-text="Paraguay">Paraguay</option><option value="PE" data-text="Peru">Peru</option><option value="RP" data-text="Philippines">Philippines</option><option value="PC" data-text="Pitcairn Islands">Pitcairn Islands</option><option value="PL" data-text="Poland">Poland</option><option value="PO" data-text="Portugal">Portugal</option><option value="QA" data-text="Qatar">Qatar</option><option value="RE" data-text="Reunion">Reunion</option><option value="RO" data-text="Romania">Romania</option><option value="RS" data-text="Russia">Russia</option><option value="RW" data-text="Rwanda">Rwanda</option><option value="TB" data-text="Saint Barthelemy">Saint Barthelemy</option><option value="SH" data-text="Saint Helena">Saint Helena</option><option value="SC" data-text="Saint Kitts and Nevis">Saint Kitts and Nevis</option><option value="ST" data-text="Saint Lucia">Saint Lucia</option><option value="RN" data-text="Saint Martin">Saint Martin</option><option value="SB" data-text="Saint Pierre and Miquelon">Saint Pierre and Miquelon</option><option value="VC" data-text="Saint Vincent and the Grenadines">Saint Vincent and the Grenadines</option><option value="WS" data-text="Samoa">Samoa</option><option value="SM" data-text="San Marino">San Marino</option><option value="TP" data-text="Sao Tome and Principe">Sao Tome and Principe</option><option value="SA" data-text="Saudi Arabia">Saudi Arabia</option><option value="SG" data-text="Senegal">Senegal</option><option value="RB" data-text="Serbia">Serbia</option><option value="SE" data-text="Seychelles">Seychelles</option><option value="SL" data-text="Sierra Leone">Sierra Leone</option><option value="SN" data-text="Singapore">Singapore</option><option value="NN" data-text="Sint Maarten">Sint Maarten</option><option value="LO" data-text="Slovakia">Slovakia</option><option value="SI" data-text="Slovenia">Slovenia</option><option value="BP" data-text="Solomon Islands">Solomon Islands</option><option value="SO" data-text="Somalia">Somalia</option><option value="SF" data-text="South Africa">South Africa</option><option value="SX" data-text="South Georgia and the South Sandwich Islands">South Georgia and the South Sandwich Islands</option><option value="OD" data-text="South Sudan">South Sudan</option><option value="SP" data-text="Spain">Spain</option><option value="PG" data-text="Spratly Islands">Spratly Islands</option><option value="CE" data-text="Sri Lanka">Sri Lanka</option><option value="SU" data-text="Sudan">Sudan</option><option value="NS" data-text="Suriname">Suriname</option><option value="SV" data-text="Svalbard">Svalbard</option><option value="SW" data-text="Sweden">Sweden</option><option value="SZ" data-text="Switzerland">Switzerland</option><option value="SY" data-text="Syria">Syria</option><option value="TW" data-text="Taiwan">Taiwan</option><option value="TI" data-text="Tajikistan">Tajikistan</option><option value="TZ" data-text="Tanzania">Tanzania</option><option value="TH" data-text="Thailand">Thailand</option><option value="TT" data-text="Timor-Leste">Timor-Leste</option><option value="TO" data-text="Togo">Togo</option><option value="TL" data-text="Tokelau">Tokelau</option><option value="TN" data-text="Tonga">Tonga</option><option value="TD" data-text="Trinidad and Tobago">Trinidad and Tobago</option><option value="TE" data-text="Tromelin Island">Tromelin Island</option><option value="TS" data-text="Tunisia">Tunisia</option><option value="TU" data-text="Turkey">Turkey</option><option value="TX" data-text="Turkmenistan">Turkmenistan</option><option value="TK" data-text="Turks and Caicos Islands">Turks and Caicos Islands</option><option value="TV" data-text="Tuvalu">Tuvalu</option><option value="UG" data-text="Uganda">Uganda</option><option value="UP" data-text="Ukraine">Ukraine</option><option value="AE" data-text="United Arab Emirates">United Arab Emirates</option><option value="UK" data-text="United Kingdom">United Kingdom</option><option value="US" data-text="United States">United States</option><option value="UY" data-text="Uruguay">Uruguay</option><option value="UZ" data-text="Uzbekistan">Uzbekistan</option><option value="NH" data-text="Vanuatu">Vanuatu</option><option value="VT" data-text="Vatican City">Vatican City</option><option value="VE" data-text="Venezuela">Venezuela</option><option value="VM" data-text="Vietnam">Vietnam</option><option value="WF" data-text="Wallis and Futuna Islands">Wallis and Futuna Islands</option><option value="WI" data-text="Western Sahara">Western Sahara</option><option value="YM" data-text="Yemen">Yemen</option><option value="ZA" data-text="Zambia">Zambia</option><option value="ZI" data-text="Zimbabwe">Zimbabwe</option></select></div></div> 出生国家就选美国





然后<div class="form_question form_select form_question_3ca01355-cb44-4e4e-ac25-8b9665284bc2 form_layout_stacked" id="form_question_3ca01355-cb44-4e4e-ac25-8b9665284bc2" style="clear: left;" data-id="3ca01355-cb44-4e4e-ac25-8b9665284bc2" data-type="select" data-export="sys:field:citz_status"><label class="form_label" for="form_3ca01355-cb44-4e4e-ac25-8b9665284bc2">Please choose which option best reflects your citizenship status.<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_3ca01355-cb44-4e4e-ac25-8b9665284bc2" name="form_3ca01355-cb44-4e4e-ac25-8b9665284bc2" autocomplete="off" data-handler-change="1"><option></option><option value="a1ab9237-9968-49ee-84c4-73e584abfc5c" data-text="I was born in the United States and am a citizen of the United States.">I was born in the United States and am a citizen of the United States.</option><option value="02e096dc-738c-4995-8b1f-f23a76940d6a" data-text="I was not born in the United States, and I am a citizen of the United States.">I was not born in the United States, and I am a citizen of the United States.</option><option value="4c28c5f4-a982-4039-a98c-dcc06d4c396c" data-text="I am a permanent resident of the United States and have a permanent resident card.">I am a permanent resident of the United States and have a permanent resident card.</option><option value="914d05eb-8f3a-417b-9ddf-775b7dea3bc3" data-text="I am not a US citizen or permanent resident, and I am currently living in the United States on a dependent visa (such as E-2, H-4, L-2, A-2, etc.).">I am not a US citizen or permanent resident, and I am currently living in the United States on a dependent visa (such as E-2, H-4, L-2, A-2, etc.).</option><option value="a0b3d3ad-d85d-4325-81b1-a36ab0a185eb" data-text="I am currently living in the United States for the purpose of attending high school, college, or university and am on an F-1 visa.">I am currently living in the United States for the purpose of attending high school, college, or university and am on an F-1 visa.</option><option value="3fb6cc56-65dd-478e-8493-0e77891162be" data-text="I am not a US citizen or permanent resident, and I am currently living outside the United States and will need an F-1 visa to enter and study in the United States.">I am not a US citizen or permanent resident, and I am currently living outside the United States and will need an F-1 visa to enter and study in the United States.</option><option value="516b8529-66e4-48c0-9812-1e8bf4de48e7" data-text="I was not born in the United States, and I entered the United States without a documented immigration status and do not currently have a documented immigration status (includes DACA and DREAMer).">I was not born in the United States, and I entered the United States without a documented immigration status and do not currently have a documented immigration status (includes DACA and DREAMer).</option><option value="7cd70ae0-9b83-454b-9489-d45ce349a396" data-text="I am currently living in the United States and have been granted asylee status.">I am currently living in the United States and have been granted asylee status.</option><option value="df13255c-7699-4c3e-ae20-971d9551dad8" data-text="I am currently living in the United States and have been granted refugee status.">I am currently living in the United States and have been granted refugee status.</option><option value="cb08e9be-1490-4b46-80fe-6e3e68fb8349" data-text="I am currently in the United States and have applied for permanent residency, but it has not been granted.">I am currently in the United States and have applied for permanent residency, but it has not been granted.</option><option value="ab5ab490-a3b0-4636-bc59-04580dc688f3" data-text="I am currently in the United States and have applied for asylum, but it has not been granted.">I am currently in the United States and have applied for asylum, but it has not been granted.</option><option value="0339f5a5-1c82-4607-8682-04fe4c137f16" data-text="I entered the United Status as a parolee, but cannot provide documentation of citizenship or a permanent status in the US.">I entered the United Status as a parolee, but cannot provide documentation of citizenship or a permanent status in the US.</option><option value="ec699fb4-a6b1-443b-a46c-1278f483b9e8" data-text="I was not born in the United States, and I entered the United States with a documented immigration status, but that status is no longer current.">I was not born in the United States, and I entered the United States with a documented immigration status, but that status is no longer current.</option><option value="11ba019c-eecd-4d3f-a630-fd428e997715" data-text="I am not a US citizen or permanent resident, and I am currently living in the United States on a dependent visa (such as E-2, H-4, L-2, A-2, etc.) and planning a change of status to F-1 within the United States.">I am not a US citizen or permanent resident, and I am currently living in the United States on a dependent visa (such as E-2, H-4, L-2, A-2, etc.) and planning a change of status to F-1 within the United States.</option></select></div></div> 
选择I was born in the United States and am a citizen of the United States. 选项
然后<div class="form_question form_select form_question_f0032eea-5928-4221-af1f-0e1e8ea78efe form_layout_stacked" id="form_question_f0032eea-5928-4221-af1f-0e1e8ea78efe" style="clear: left;" data-id="f0032eea-5928-4221-af1f-0e1e8ea78efe" data-type="select" data-export="sys:app:dual_citizenship"><label class="form_label" for="form_f0032eea-5928-4221-af1f-0e1e8ea78efe">Do you have dual citizenship with another country?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_f0032eea-5928-4221-af1f-0e1e8ea78efe" name="form_f0032eea-5928-4221-af1f-0e1e8ea78efe" autocomplete="off" data-handler-change="1"><option></option><option value="1" data-text="Yes">Yes</option><option value="0" data-text="No">No</option></select></div></div> 选择No

然后<div class="form_question form_text form_question_dbfafeb1-662e-4081-906a-526729f20163 form_layout_table" id="form_question_dbfafeb1-662e-4081-906a-526729f20163" style="clear: left;" data-id="dbfafeb1-662e-4081-906a-526729f20163" data-type="text" data-export="sys:ssn"><label class="form_label" for="form_dbfafeb1-662e-4081-906a-526729f20163">Social Security Number<span style="color:red">*</span></label><div class="form_responses"><input type="text" size="10" id="form_dbfafeb1-662e-4081-906a-526729f20163" name="form_dbfafeb1-662e-4081-906a-526729f20163" value="" maxlength="9" data-maxlength="{ help: '#form_dbfafeb1-662e-4081-906a-526729f20163_maxlength' }" aria-describedby="form_dbfafeb1-662e-4081-906a-526729f20163_maxlength" placeholder="(Omit Dashes)" autocomplete="off" spellcheck="false" class="maxlength_enabled"></div></div> 输入库中的社保号码（去掉斜杠连字符）

然后<div class="form_question form_select form_question_bf0347dc-27cb-4226-aa7f-71bd269f6307 form_layout_stacked" id="form_question_bf0347dc-27cb-4226-aa7f-71bd269f6307" style="clear: left;" data-id="bf0347dc-27cb-4226-aa7f-71bd269f6307" data-type="select" data-export="sys:field:hispanic"><label class="form_label" for="form_bf0347dc-27cb-4226-aa7f-71bd269f6307">Are you Hispanic or Latino?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_bf0347dc-27cb-4226-aa7f-71bd269f6307" name="form_bf0347dc-27cb-4226-aa7f-71bd269f6307" autocomplete="off"><option></option><option value="1" data-text="Yes">Yes</option><option value="0" data-text="No">No</option></select></div></div> 选择No

<div class="form_question form_checkbox form_question_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2 form_layout_stacked" id="form_question_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2" style="clear: left;" data-id="dbfe739b-27c7-4218-a761-ff1c4ce2b3e2" data-type="checkbox" data-export="sys:field:race" aria-label="Race"><fieldset id="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2"><div class="form_label"></div><div class="form_responses"><div class="form_response"><input id="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_1" type="checkbox" data-text="American Indian or Alaska Native" name="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2" value="adfcfcb5-5394-4fca-991f-2e460e924fee" autocomplete="off" data-handler-change="1"><label for="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_1">American Indian or Alaska Native</label></div><div class="form_response"><input id="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_2" type="checkbox" data-text="Asian" name="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2" value="5c5b0b7a-73a6-4797-8d82-562aa4e31107" autocomplete="off" data-handler-change="1"><label for="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_2">Asian</label></div><div class="form_response"><input id="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_3" type="checkbox" data-text="Black or African American" name="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2" value="12b49f2e-6e7d-4402-b58d-c6eab43b789a" autocomplete="off" data-handler-change="1"><label for="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_3">Black or African American</label></div><div class="form_response"><input id="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_4" type="checkbox" data-text="Native Hawaiian or Other Pacific" name="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2" value="3815164f-99e7-4333-9006-4b6a3e3f3a0e" autocomplete="off" data-handler-change="1"><label for="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_4">Native Hawaiian or Other Pacific</label></div><div class="form_response"><input id="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_5" type="checkbox" data-text="White" name="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2" value="cd6b1bac-e441-4649-a7d3-a61ccf8c860c" autocomplete="off" data-handler-change="1"><label for="form_dbfe739b-27c7-4218-a761-ff1c4ce2b3e2_5">White</label></div></div></fieldset></div> 选择White

<div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Lazy.Commit(this);" type="button">Continue</button></div> 最后点击Continue 按钮，先做到这一步



接下来我们就进入到了新页面
监听
<div class="form_question form_select form_question_e6a5af0a-c68f-46ba-9f0d-fa170da8fcde form_layout_stacked" id="form_question_e6a5af0a-c68f-46ba-9f0d-fa170da8fcde" style="clear: left;" data-id="e6a5af0a-c68f-46ba-9f0d-fa170da8fcde" data-type="select" data-export="sys:app:live_in_OK"><label class="form_label" for="form_e6a5af0a-c68f-46ba-9f0d-fa170da8fcde">Are you a resident of Oklahoma?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_e6a5af0a-c68f-46ba-9f0d-fa170da8fcde" name="form_e6a5af0a-c68f-46ba-9f0d-fa170da8fcde" autocomplete="off" data-handler-change="1"><option></option><option value="1" data-text="Yes">Yes</option><option value="0" data-text="No">No</option></select></div></div> 选择Yes
<div class="form_question form_select form_question_7667c3fe-4226-4a2a-99ff-20263d2b8c4c form_layout_stacked" id="form_question_7667c3fe-4226-4a2a-99ff-20263d2b8c4c" style="clear: left;" data-id="7667c3fe-4226-4a2a-99ff-20263d2b8c4c" data-type="select" data-export="sys:app:res_time"><label class="form_label" for="form_7667c3fe-4226-4a2a-99ff-20263d2b8c4c">How long have you lived in Oklahoma?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_7667c3fe-4226-4a2a-99ff-20263d2b8c4c" name="form_7667c3fe-4226-4a2a-99ff-20263d2b8c4c" autocomplete="off" data-handler-change="1"><option></option><option value="a47fd02b-5b9a-4861-8dae-04c240ad5b47" data-text="Less than one year">Less than one year</option><option value="f534766c-0b6d-4722-bf7b-c4be2137cbc3" data-text="1 - 2 years">1 - 2 years</option><option value="018b3251-bbfc-4678-a0c8-4eecf83c9101" data-text="2 - 3 years">2 - 3 years</option><option value="45bb9b18-5685-4783-8dd8-7d8b86cfc1b1" data-text="3 - 4 years">3 - 4 years</option><option value="527e9257-b0de-4929-8986-05942bf37e78" data-text="4 - 5 years">4 - 5 years</option><option value="e673f12f-5129-47f7-b9d6-96fac25eaecd" data-text="5 - 6 years">5 - 6 years</option><option value="23912f8e-ba51-42a5-aa98-6fce5b0d5e9d" data-text="6 - 7 years">6 - 7 years</option><option value="5bc8e211-a25b-49ca-b220-064a837f8113" data-text="7 - 8 years">7 - 8 years</option><option value="b9fdbad2-92af-4cbb-a6af-02513ffeb2f3" data-text="More than 8 years">More than 8 years</option><option value="3e4f8bb7-2d06-4dd1-badc-7ed95861a7d1" data-text="Since birth (Never have lived in any other state)">Since birth (Never have lived in any other state)</option></select></div></div>
随机选择1-8年
然后
<div class="form_question form_select form_question_0d5050d1-8142-4a91-a24d-c751a8fe198f form_layout_stacked" id="form_question_0d5050d1-8142-4a91-a24d-c751a8fe198f" style="clear: left;" data-id="0d5050d1-8142-4a91-a24d-c751a8fe198f" data-type="select" data-export="sys:app:res_intent" aria-hidden="false"><label class="form_label" for="form_0d5050d1-8142-4a91-a24d-c751a8fe198f">Please indicate the primary reason you moved to Oklahoma.<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_0d5050d1-8142-4a91-a24d-c751a8fe198f" name="form_0d5050d1-8142-4a91-a24d-c751a8fe198f" autocomplete="off" data-handler-change="1"><option></option><option value="d4e08ef0-4685-4b96-bacb-c962d0e7df3d" data-text="To attend School">To attend School</option><option value="2456ba5e-f419-4f77-b5c3-332ca2d689e4" data-text="Moved with spouse who is attending school">Moved with spouse who is attending school</option><option value="1e48e0af-b065-496a-892f-eec46877c57f" data-text="Job relocation (you or spouse)">Job relocation (you or spouse)</option><option value="bf04977d-e13c-4493-abf6-1a456535d54a" data-text="Parents relocation">Parents relocation</option><option value="8ae65e2e-e607-4960-930f-6e6bbacce9d3" data-text="To live with a parent who is a resident">To live with a parent who is a resident</option><option value="bf672bbc-1a0a-4bc3-9e17-85c7cc873da7" data-text="Active duty military or spouse/dependent of active duty military stationed in Oklahoma">Active duty military or spouse/dependent of active duty military stationed in Oklahoma</option><option value="151d03f8-b4f9-4b80-b85a-b830f040538e" data-text="Other">Other</option></select></div></div>
随机选择，大概率选择To attend School

然后<div class="form_question form_select form_question_f0c4f1e3-b8db-4581-b3b5-da4a36e10bc4 form_layout_stacked" id="form_question_f0c4f1e3-b8db-4581-b3b5-da4a36e10bc4" style="clear: left;" data-id="f0c4f1e3-b8db-4581-b3b5-da4a36e10bc4" data-type="select" data-export="sys:app:county_current"><label class="form_label" for="form_f0c4f1e3-b8db-4581-b3b5-da4a36e10bc4">What county do you current live in?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_f0c4f1e3-b8db-4581-b3b5-da4a36e10bc4" name="form_f0c4f1e3-b8db-4581-b3b5-da4a36e10bc4" autocomplete="off"><option></option><option value="fca3421a-df23-421e-9e3d-07aeff6b9480" data-text="Adair">Adair</option><option value="fb1f757c-aebf-42eb-b8af-8322b573ed20" data-text="Alfalfa">Alfalfa</option><option value="82dde7c6-4909-4875-bbd9-11bb2ecb0433" data-text="Atoka">Atoka</option><option value="ffea5703-96df-4487-a357-4954ccec86b6" data-text="Beaver">Beaver</option><option value="56e71f6d-9f9c-4483-a33f-275221cc73d5" data-text="Beckham">Beckham</option><option value="f0c8c3b4-f7dd-4c3a-959e-9f7bb67af7ef" data-text="Blaine">Blaine</option><option value="7d30134d-7e0f-42af-abc4-f19ecf0183c6" data-text="Bryan">Bryan</option><option value="db9c5bff-391a-4463-8958-5ed2ab53a273" data-text="Caddo">Caddo</option><option value="aa4e8980-d5a1-42ef-92ab-fff5280e286a" data-text="Canadian">Canadian</option><option value="1c26e114-fc02-4e61-9bef-63e903d729b6" data-text="Carter">Carter</option><option value="7f255cdc-b242-4a6f-aeab-16259e9239a5" data-text="Cherokee">Cherokee</option><option value="632d891c-332a-4007-9dc6-89a012e37a8d" data-text="Choctaw">Choctaw</option><option value="a467496d-fac1-4f89-9e69-ad537d2dc0ae" data-text="Cimarron">Cimarron</option><option value="3e2fd851-95b8-4ee1-bea8-77b426341b55" data-text="Cleveland">Cleveland</option><option value="bb2b7db8-c588-452a-8369-a053e475dee4" data-text="Coal">Coal</option><option value="ff5ca7b3-5e94-47de-887b-60754d6cef86" data-text="Comanche">Comanche</option><option value="e80695a6-f736-4545-a86b-b79463fb1e28" data-text="Cotton">Cotton</option><option value="f344b89b-c3ff-4d58-9fcc-714d9573f01e" data-text="Craig">Craig</option><option value="c53f7d09-8a15-4545-96c4-2ce7bc7ab059" data-text="Creek">Creek</option><option value="1e85ff43-7dbd-4d39-a2d3-08d6dbe5c94f" data-text="Custer">Custer</option><option value="ce4b4eec-4664-4f45-ad06-c2443a9a5359" data-text="Delaware">Delaware</option><option value="9d4066b4-8e50-4dd2-aa7e-9478949fee4d" data-text="Dewey">Dewey</option><option value="06a35e4b-66bf-49b7-bd5b-180b4542cf32" data-text="Ellis">Ellis</option><option value="ef87d8f5-b083-4e9f-bc31-03bfc9b527c4" data-text="Garfield">Garfield</option><option value="80ddcf5a-5a67-4bf2-8c3b-f9605582a7ae" data-text="Garvin">Garvin</option><option value="8e2f6262-9f25-4bf5-93b9-639aa51778c0" data-text="Grady">Grady</option><option value="a7a1e37a-6a97-4de3-9355-0195522c6b82" data-text="Grant">Grant</option><option value="f98b6260-53cb-4f32-9daf-86883397c232" data-text="Greer">Greer</option><option value="403f07a0-79e0-471e-bc20-a2dea683b99e" data-text="Harmon">Harmon</option><option value="c0d747ab-194f-4e93-bcec-2409240756c8" data-text="Harper">Harper</option><option value="6f7b8544-e266-4b16-958c-2cb125d1b824" data-text="Haskell">Haskell</option><option value="f9ac54b5-cd90-4fea-910d-e9a681b2d183" data-text="Hughes">Hughes</option><option value="f5645113-12d8-4278-b356-400525a4b123" data-text="Jackson">Jackson</option><option value="5ef1e7ec-c569-4ba2-9d58-dbc097f6e517" data-text="Jefferson">Jefferson</option><option value="28873b07-9940-4c87-a614-391da65d7afa" data-text="Johnston">Johnston</option><option value="f64779f4-73a1-4518-97cd-c55384c71fe2" data-text="Kay">Kay</option><option value="be7539ce-5738-4b33-b90e-10c764ef59c5" data-text="Kingfisher">Kingfisher</option><option value="57405d45-854c-487a-9a06-3750672aa38b" data-text="Kiowa">Kiowa</option><option value="09d63a6d-d457-4886-843c-9294a9c95aea" data-text="Latimer">Latimer</option><option value="5eb22b9a-59c2-4ce1-91ee-e8adc4bf754c" data-text="LeFlore">LeFlore</option><option value="6cffeb9b-05e2-40fb-8f21-d27ab110de10" data-text="Lincoln">Lincoln</option><option value="00920ece-c51b-42ac-b310-f8eb3cb6a00f" data-text="Logan">Logan</option><option value="1b49669a-8b1c-4c82-a4e9-55a6a3db6816" data-text="Love">Love</option><option value="eb3c8c72-c698-4157-9a86-3e57289f7361" data-text="McClain">McClain</option><option value="73027478-40f5-4b22-b98a-ef39aa054cac" data-text="McCurtain">McCurtain</option><option value="7758aadd-c615-4e43-9bac-a7a9d342dcf8" data-text="McIntosh">McIntosh</option><option value="e8314383-315d-4ff1-bd81-73e41e2e5efd" data-text="Major">Major</option><option value="7c203603-a2f1-44bb-8297-9625524b95aa" data-text="Marshall">Marshall</option><option value="b33dda33-f29f-455b-92b2-fa4843b9fd9d" data-text="Mayes">Mayes</option><option value="3abe9a07-15f5-4882-a73e-0ecad7f06d32" data-text="Murray">Murray</option><option value="7a5d0e1b-2f68-4750-89aa-fa70222099f2" data-text="Muskogee">Muskogee</option><option value="19921613-f6e3-4879-9e36-f163a5cf6c7a" data-text="Noble">Noble</option><option value="c1f37538-826f-4d26-b60a-f6ad719db1a7" data-text="Nowata">Nowata</option><option value="0ba541df-89da-4add-9cb2-89bf32515ec1" data-text="Okfuskee">Okfuskee</option><option value="fc9156c8-4f3c-4518-bd61-226d2236b21d" data-text="Oklahoma">Oklahoma</option><option value="2212b0a6-bdd6-4dcf-99c2-e91e628a4c5d" data-text="Okmulgee">Okmulgee</option><option value="96654c54-cbbc-489d-b7c4-8df88f494c37" data-text="Osage">Osage</option><option value="a8071116-2355-49ef-888c-968c80b41140" data-text="Ottawa">Ottawa</option><option value="8ccc2877-85ce-4254-95bf-e1a4ab93d2d6" data-text="Pawnee">Pawnee</option><option value="14031dae-a21d-4b0d-94a0-3fc38f98acde" data-text="Payne">Payne</option><option value="d65f632e-8068-43c6-9981-e899d26825fb" data-text="Pittsburg">Pittsburg</option><option value="9e57d2ec-2314-4d60-9bbf-3c5d10c5ff50" data-text="Pontotoc">Pontotoc</option><option value="eca45031-b187-465a-a0f2-259807bd2d89" data-text="Pottawatomie">Pottawatomie</option><option value="eac18bb0-c44d-442f-9e59-9b544cdd59e7" data-text="Pushmataha">Pushmataha</option><option value="f7144328-485c-4ba4-af48-37ce11848d1c" data-text="Roger Mills">Roger Mills</option><option value="53928948-e827-4437-bc82-8363953cd1f4" data-text="Rogers">Rogers</option><option value="77e60997-de7f-4b6b-9e30-9b6304dc4af9" data-text="Seminole">Seminole</option><option value="17770dc2-5440-44fa-9ec2-38f159f33b76" data-text="Sequoyah">Sequoyah</option><option value="86895413-0a13-4471-a7c8-2463a016a879" data-text="Stephens">Stephens</option><option value="f2d48f04-de32-4bb7-8913-a3e3c544714b" data-text="Texas">Texas</option><option value="5e09c505-d1bd-485c-ae21-613327f181a9" data-text="Tillman">Tillman</option><option value="799c14b9-b286-49b5-b4e8-cad34b2ffcd4" data-text="Tulsa">Tulsa</option><option value="3b1d630e-eb24-44b3-bad4-28424a1c8a09" data-text="Wagoner">Wagoner</option><option value="a57beb80-e231-4be0-a9d8-e549eabbe5cf" data-text="Washington">Washington</option><option value="6cce20ae-f618-42d0-bd4a-72440bee5a00" data-text="Washita">Washita</option><option value="858e24ba-d314-4142-8589-1dec0fd86ddf" data-text="Woods">Woods</option><option value="9cd2ac3a-11eb-4bee-b558-94aeb8c5041f" data-text="Woodward">Woodward</option></select></div></div> 选择 俄克拉荷马

然后<div class="form_question form_select form_question_5d7d29f0-a5b7-486c-b964-4fd5c3e3aca7 form_layout_stacked" id="form_question_5d7d29f0-a5b7-486c-b964-4fd5c3e3aca7" style="clear: left;" data-id="5d7d29f0-a5b7-486c-b964-4fd5c3e3aca7" data-type="select" data-export="sys:app:military_veteran"><label class="form_label" for="form_5d7d29f0-a5b7-486c-b964-4fd5c3e3aca7">Do you have military affiliation?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_5d7d29f0-a5b7-486c-b964-4fd5c3e3aca7" name="form_5d7d29f0-a5b7-486c-b964-4fd5c3e3aca7" autocomplete="off" data-handler-change="1"><option></option><option value="1" data-text="Yes">Yes</option><option value="0" data-text="No">No</option></select></div></div> 选择No
<div class="form_question form_select form_question_c3333039-6f72-4a06-9bf7-4271cd47863b form_layout_stacked" id="form_question_c3333039-6f72-4a06-9bf7-4271cd47863b" style="clear: left;" data-id="c3333039-6f72-4a06-9bf7-4271cd47863b" data-type="select" data-export="sys:app:va_benefits_intent"><label class="form_label" for="form_c3333039-6f72-4a06-9bf7-4271cd47863b">Do you plan on using VA Education Benefits?</label><div class="form_responses"><select size="1" id="form_c3333039-6f72-4a06-9bf7-4271cd47863b" name="form_c3333039-6f72-4a06-9bf7-4271cd47863b" autocomplete="off" data-handler-change="1"><option></option><option value="1" data-text="Yes">Yes</option><option value="0" data-text="No">No</option></select></div></div> 选择No
然后点击<div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Lazy.Commit(this);" type="button">Continue</button></div>


接下来会进入新页面
监听<div class="form_question form_select form_question_9ce31871-86e6-40de-9c61-78a208eb7750 form_layout_stacked" id="form_question_9ce31871-86e6-40de-9c61-78a208eb7750" style="clear: left;" data-id="9ce31871-86e6-40de-9c61-78a208eb7750" data-type="select" data-export="sys:app:apptype_fr"><label class="form_label" for="form_9ce31871-86e6-40de-9c61-78a208eb7750">Have you graduated high school?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_9ce31871-86e6-40de-9c61-78a208eb7750" name="form_9ce31871-86e6-40de-9c61-78a208eb7750" autocomplete="off" data-handler-change="1"><option></option><option value="0e3a79bb-026e-465a-a010-03796d88ae28" data-text="No, I am currently in high school.">No, I am currently in high school.</option><option value="f7f591e4-7e63-4995-976c-eacf8b118150" data-text="Yes, I have graduated high school and have earned less than 7 college credit hours AFTER graduating high school.">Yes, I have graduated high school and have earned less than 7 college credit hours AFTER graduating high school.</option><option value="b483c926-9f2d-4937-b0ed-5a944b9ea097" data-text="Yes, I have graduated high school and have earned more than 6 college credit hours AFTER graduating high school.">Yes, I have graduated high school and have earned more than 6 college credit hours AFTER graduating high school.</option><option value="f543e637-0e81-40e7-8e34-5fa9bc52a1bb" data-text="No, I did not graduate high school.">No, I did not graduate high school.</option></select></div></div>
选择No, l am currently in high school.

<div class="form_question form_select form_question_297ce475-09b6-43f0-95c8-904781b0e81f form_layout_stacked" id="form_question_297ce475-09b6-43f0-95c8-904781b0e81f" style="clear: left;" data-id="297ce475-09b6-43f0-95c8-904781b0e81f" data-type="select" data-export="sys:app:app_readmit"><label class="form_label" for="form_297ce475-09b6-43f0-95c8-904781b0e81f">Have you ever taken classes at OSU-Oklahoma City?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_297ce475-09b6-43f0-95c8-904781b0e81f" name="form_297ce475-09b6-43f0-95c8-904781b0e81f" autocomplete="off"><option></option><option value="3ee216d1-af15-4af0-bd78-c77663762b25" data-text="No, I have not previously attended OSU-Oklahoma City">No, I have not previously attended OSU-Oklahoma City</option><option value="9161417c-e6be-4ff6-a720-1a7a88e02637" data-text="Yes, I have previously attended OSU-Oklahoma City and HAVE NOT taken classes at any other college since leaving">Yes, I have previously attended OSU-Oklahoma City and HAVE NOT taken classes at any other college since leaving</option><option value="0a94e754-ba14-4160-a63f-d90cc4e0129f" data-text="Yes, I have previously attended OSU-Oklahoma City and HAVE taken classes another college since leaving">Yes, I have previously attended OSU-Oklahoma City and HAVE taken classes another college since leaving</option></select></div></div> 选择
No, I have not previously attended osU-Oklahoma city
然后
<div class="form_question form_select form_question_1d8ce90c-ca39-4b45-a7cb-ed61438c4396 form_layout_stacked" id="form_question_1d8ce90c-ca39-4b45-a7cb-ed61438c4396" style="clear: left;" data-id="1d8ce90c-ca39-4b45-a7cb-ed61438c4396" data-type="select" data-export="sys:app:hs_grad_year" aria-hidden="false"><label class="form_label" for="form_1d8ce90c-ca39-4b45-a7cb-ed61438c4396">What year have you or will you graduate high school?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_1d8ce90c-ca39-4b45-a7cb-ed61438c4396" name="form_1d8ce90c-ca39-4b45-a7cb-ed61438c4396" autocomplete="off"><option></option><option value="cb53e0a0-40f7-4ad9-8111-07a5f7b24fd2" data-text="2025">2025</option><option value="85fb25c1-6a7c-402e-ac28-6dd81366fd1e" data-text="2026">2026</option><option value="d07c69c2-bf08-48e2-9bba-7021dd180313" data-text="2027">2027</option><option value="ce4887d1-c427-4fc0-bc81-c8a3a6e17486" data-text="2028">2028</option><option value="4098412b-0e15-4018-b0c4-a53d4e1b524c" data-text="2029">2029</option></select></div></div> 选择2025 或者2026
然后<div class="form_question form_select form_question_a8910a84-7232-40c2-a0d2-ff4e99ea38d8 form_layout_stacked" id="form_question_a8910a84-7232-40c2-a0d2-ff4e99ea38d8" style="clear: left;" data-id="a8910a84-7232-40c2-a0d2-ff4e99ea38d8" data-type="select" data-export="sys:app:education_goal"><label class="form_label" for="form_a8910a84-7232-40c2-a0d2-ff4e99ea38d8">Education Goal<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_a8910a84-7232-40c2-a0d2-ff4e99ea38d8" name="form_a8910a84-7232-40c2-a0d2-ff4e99ea38d8" autocomplete="off" data-handler-change="1"><option></option><option value="be061155-fe5a-4ed3-ae16-151569ff2d74" data-text="I DO NOT plan to pursue a certificate or degree">I DO NOT plan to pursue a certificate or degree</option><option value="281c8c7c-6641-4a9a-9aa8-e889391e76fc" data-text="I plan to pursue a Certificate program.">I plan to pursue a Certificate program.</option><option value="6706742c-0020-4b4a-9bca-bce2ccde50f4" data-text="I plan to pursue an Associate's degree.">I plan to pursue an Associate's degree.</option><option value="8e94aa7a-e050-4458-928a-8d81223498ce" data-text="I plan to pursue a Bachelor of Technology">I plan to pursue a Bachelor of Technology</option></select></div></div> 选择I DO NOT plan to pursue a certificate or degree
然后<div class="form_responses"><select size="1" id="form_1a33ccf8-8c79-480a-9578-fcf05d828aa7" name="form_1a33ccf8-8c79-480a-9578-fcf05d828aa7" autocomplete="off"><option></option><option value="88ec4a62-c92b-4ee0-976f-52ac49479f1f" data-text="No College">No College</option><option value="bc00f5bd-bd45-400d-b6ec-48ad765f232c" data-text="Some College">Some College</option><option value="bc52d42a-8d84-4592-a9c0-27470d15def3" data-text="Associate's Degree">Associate's Degree</option><option value="14688796-87d8-4822-8fa3-1f10851e0473" data-text="Bachelor's Degree">Bachelor's Degree</option><option value="f25b4ee2-2199-4e99-be48-6d966bf588f6" data-text="Graduate College/Beyond">Graduate College/Beyond</option></select></div>选择No College以及Some College随机
然后点击<div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Lazy.Commit(this);" type="button">Continue</button></div> 继续按钮，先做到这里



5接下来我们进入了一个新的页面
<div class="form_question form_select form_question_d681951f-77f0-4586-827b-b378130d0d74 form_layout_stacked" id="form_question_d681951f-77f0-4586-827b-b378130d0d74" style="clear: left;" data-id="d681951f-77f0-4586-827b-b378130d0d74" data-type="select" data-export="sys:app:attend_career_tech"><label class="form_label" for="form_d681951f-77f0-4586-827b-b378130d0d74">Have you previously attended a career technology center in Oklahoma?<span style="color:red">*</span></label><div class="form_responses"><select size="1" id="form_d681951f-77f0-4586-827b-b378130d0d74" name="form_d681951f-77f0-4586-827b-b378130d0d74" autocomplete="off"><option></option><option value="1" data-text="Yes">Yes</option><option value="0" data-text="No">No</option></select></div></div> 选择No
然后找到<div class="form_responses"><table data-id="50dab743-d5ae-4f19-a355-1f094de6c6b2" class="table sortable sortable_completed"><colgroup><col style="width: 40%;"><col><col style="width: 25%;"></colgroup><thead><tr class="column"><th class="sortable_header">Institution</th><th class="sortable_header">Degree or Level of Study</th><th class="sortable_header">Dates Attended</th></tr><tr class="row_hover"><td colspan="3"><a class="widget_add" data-href="/apply/frm?cmd=widget&amp;form=307969fb-bf77-4c14-81c4-d24d13fc6807&amp;field=50dab743-d5ae-4f19-a355-1f094de6c6b2&amp;scope=app_hosted_page" href="//" onclick="return FW.Lazy.Popup(this);">Add School</a></td></tr></thead><tbody></tbody></table></div>
点击
<td colspan="3"><a class="widget_add" data-href="/apply/frm?cmd=widget&amp;form=307969fb-bf77-4c14-81c4-d24d13fc6807&amp;field=50dab743-d5ae-4f19-a355-1f094de6c6b2&amp;scope=app_hosted_page" href="//" onclick="return FW.Lazy.Popup(this);">Add School</a></td>
会出现这个新弹窗
<div class="dialog dialog_closeable" data-href="/apply/frm?cmd=widget&amp;form=307969fb-bf77-4c14-81c4-d24d13fc6807&amp;field=50dab743-d5ae-4f19-a355-1f094de6c6b2&amp;scope=app_hosted_page" role="dialog" aria-hidden="false" aria-label="Academic History"><style></style><form action="https://apply.osuokc.edu/apply/frm?cmd=widget&amp;form=307969fb-bf77-4c14-81c4-d24d13fc6807&amp;field=50dab743-d5ae-4f19-a355-1f094de6c6b2&amp;scope=app_hosted_page" enctype="multipart/form-data" method="post" data-fw-changed-attribute="dirty" style="width: 650px;" novalidate="novalidate" data-fw-form="1" autocomplete="off" data-ready="1"><div class="header ui-draggable-handle dialog_closeable">Academic History<div class="dialog_close" aria-label="Close" role="button" aria-pressed="false" title="Close" data-clickable="1" tabindex="0"></div></div><div class="content" style="height: 500px;"><div id="form_contents"><script>/*<![CDATA[*/new function($) { var renderForm = function() { var form = window['Form'] = window['form_307969fb-bf77-4c14-81c4-d24d13fc6807'] = new FormFiller($('#form_page_307969fb-bf77-4c14-81c4-d24d13fc6807')); var keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326 = {}; keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326['form_a3e6d925-5740-7e51-f8a1-9f4ed538a326'] = 'name'; keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326['form_4c9ad284-b47b-2533-6e22-b88d53c5ebbe'] = 'key'; keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326['form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_street'] = 'street'; keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326['form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_city'] = 'city'; keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326['form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_country'] = 'country'; keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326['form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_postal'] = 'postal'; keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326['form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_region'] = 'region'; new Suggest({ input: $('#form_a3e6d925-5740-7e51-f8a1-9f4ed538a326'), service: 'https://apply.osuokc.edu/manage/service/lookup?type=school&q=', callback: function(result) { var keys = keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326; for (var key in keys) { if (keys[key] == 'country') if (result[keys[key]] == null) continue; if ($('#' + key).length > 0) $('#' + key).val(result[keys[key]]).triggerHandler('change'); } }, clear: function() { var keys = keys_a3e6d925_5740_7e51_f8a1_9f4ed538a326; for (var key in keys) { if ($('#' + key).length > 0 && !$('#' + key).is(this.input)) $('#' + key).val('').triggerHandler('change'); } } }); try {

} catch (ex) { if (console.log) console.log(ex); } form.ready(); }; $(renderForm); }(FW.$);/*]]>*/</script><div class="form_container"><div id="form_page_307969fb-bf77-4c14-81c4-d24d13fc6807"><div class="form_pages"><div id="form_page_1" data-id="307969fb-bf77-4c14-81c4-d24d13fc6807" data-page="1" class="form_page form_page_1" style="clear: left;"><div class="form_question form_p form_question_3d847227-2572-426e-94f5-c8f5246850b4 form_layout_table" id="form_question_3d847227-2572-426e-94f5-c8f5246850b4" style="clear: left;" data-id="3d847227-2572-426e-94f5-c8f5246850b4" data-type="p"><div class="form_label"><ul><li><span style="font-size:14px;">If you have earned a GED, please include it below by searching for GED as the institution or typing <strong>DH0002</strong>&nbsp;into the Institution field and selecting GED.&nbsp;</span></li><li><span style="font-size:14px;">If you were home schooled, please include it below by searching for Home Schooled&nbsp;as the Institution or by typing <strong>DH0004&nbsp;</strong>into the Institution field and selecting Home Schooled.&nbsp;You will also need to add&nbsp;any other high school that you have attended.</span></li><li><span style="font-size:14px;">For each school, if you graduated or plan to graduate from this school, please select the type of degree you will earn in the "Degree" field and complete the graduation date fields.&nbsp;<strong>Leaving degree or degree date incomplete can delay the processing of applications.</strong></span></li><li><span style="font-size:14px;">If you attended an international high school and cannot find your institution listed, please use International High Schools&nbsp;as the Institution attended or type&nbsp;<strong>FH0023</strong>&nbsp;into the Institution Name field and then select International High Schools.</span></li><li><span style="font-size:14px;">Do not include any high school from which you will not graduate unless you are a home school student and attended another high school. Oklahoma Career Tech schools should NOT be listed as a school. Career Tech students should only list their actual high school.</span></li></ul></div></div><div class="form_question form_text form_question_a3e6d925-5740-7e51-f8a1-9f4ed538a326 form_layout_table" id="form_question_a3e6d925-5740-7e51-f8a1-9f4ed538a326" style="clear: left;" data-id="a3e6d925-5740-7e51-f8a1-9f4ed538a326" data-type="text" data-export="sys:school:name" data-required="1"><label class="form_label" for="form_a3e6d925-5740-7e51-f8a1-9f4ed538a326">Institution</label><div class="form_responses"><input type="text" size="48" id="form_a3e6d925-5740-7e51-f8a1-9f4ed538a326" name="form_a3e6d925-5740-7e51-f8a1-9f4ed538a326" value="" autocomplete="off" spellcheck="false" required="required" aria-autocomplete="list" aria-expanded="false" role="combobox" aria-owns="suggest_bff0b5bf-cf99-4775-b33a-1e54762ffc00" aria-controls="suggest_bff0b5bf-cf99-4775-b33a-1e54762ffc00"></div></div><div class="form_question form_text form_question_4c9ad284-b47b-2533-6e22-b88d53c5ebbe hidden form_layout_table" id="form_question_4c9ad284-b47b-2533-6e22-b88d53c5ebbe" style="clear: left;" data-id="4c9ad284-b47b-2533-6e22-b88d53c5ebbe" data-type="text" data-export="sys:school:key" data-invisible="1"><label class="form_label" for="form_4c9ad284-b47b-2533-6e22-b88d53c5ebbe">CEEB (Hidden)</label><div class="form_responses"><input type="text" size="8" id="form_4c9ad284-b47b-2533-6e22-b88d53c5ebbe" name="form_4c9ad284-b47b-2533-6e22-b88d53c5ebbe" value="" autocomplete="off" spellcheck="false"></div></div><div class="form_question form_location_nopostal form_question_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92 form_layout_table" id="form_question_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92" style="clear: left;" data-id="2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92" data-type="location_nopostal" data-export="sys:school:location" aria-label="School Address"><fieldset><div><label class="form_label" for="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_country">Country</label><div class="form_responses"><select id="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_country" name="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_country" size="1" autocomplete="off"><option value="AF">Afghanistan</option><option value="AX">Aland Islands</option><option value="AL">Albania</option><option value="AG">Algeria</option><option value="AN">Andorra</option><option value="AO">Angola</option><option value="AV">Anguilla</option><option value="AC">Antigua and Barbuda</option><option value="AR">Argentina</option><option value="AM">Armenia</option><option value="AA">Aruba</option><option value="AT">Ashmore and Cartier Islands</option><option value="AS">Australia</option><option value="AU">Austria</option><option value="AJ">Azerbaijan</option><option value="BF">Bahamas, The</option><option value="BA">Bahrain</option><option value="BG">Bangladesh</option><option value="BB">Barbados</option><option value="BS">Bassas Da India</option><option value="BO">Belarus</option><option value="BE">Belgium</option><option value="BH">Belize</option><option value="BN">Benin</option><option value="BD">Bermuda</option><option value="BT">Bhutan</option><option value="BL">Bolivia</option><option value="BQ">Bonaire, Sint Eustatius, and ...</option><option value="BK">Bosnia and Herzegovina</option><option value="BC">Botswana</option><option value="BV">Bouvet Island</option><option value="BR">Brazil</option><option value="IO">British Indian Ocean Territory</option><option value="VI">British Virgin Islands</option><option value="BX">Brunei</option><option value="BU">Bulgaria</option><option value="UV">Burkina Faso</option><option value="BY">Burundi</option><option value="CB">Cambodia</option><option value="CM">Cameroon</option><option value="CA">Canada</option><option value="CV">Cape Verde</option><option value="CJ">Cayman Islands</option><option value="CT">Central African Republic</option><option value="CD">Chad</option><option value="CI">Chile</option><option value="CH">China</option><option value="KT">Christmas Island</option><option value="IP">Clipperton Island</option><option value="CK">Cocos Islands (Keeling Islands)</option><option value="CO">Colombia</option><option value="CN">Comoros</option><option value="CF">Congo (Brazzaville)</option><option value="CG">Congo (Kinshasa)</option><option value="CW">Cook Islands</option><option value="CR">Coral Sea Islands</option><option value="CS">Costa Rica</option><option value="IV">Cote D'Ivoire</option><option value="HR">Croatia</option><option value="CU">Cuba</option><option value="UC">Curacao</option><option value="CY">Cyprus</option><option value="EZ">Czech Republic</option><option value="DA">Denmark</option><option value="DJ">Djibouti</option><option value="DO">Dominica</option><option value="DR">Dominican Republic</option><option value="EC">Ecuador</option><option value="EG">Egypt</option><option value="ES">El Salvador</option><option value="EK">Equatorial Guinea</option><option value="ER">Eritrea</option><option value="EN">Estonia</option><option value="WZ">Eswatini</option><option value="ET">Ethiopia</option><option value="EU">Europa Island</option><option value="FK">Falkland Islands (Islas Malvi...</option><option value="FO">Faroe Islands</option><option value="FM">Federated States of Micronesia</option><option value="FJ">Fiji</option><option value="FI">Finland</option><option value="FR">France</option><option value="FG">French Guiana</option><option value="FP">French Polynesia</option><option value="FS">French Southern and Antarctic...</option><option value="GB">Gabon</option><option value="GA">Gambia, The</option><option value="GG">Georgia</option><option value="GM">Germany</option><option value="GH">Ghana</option><option value="GI">Gibraltar</option><option value="GO">Glorioso Islands</option><option value="GR">Greece</option><option value="GL">Greenland</option><option value="GJ">Grenada</option><option value="GP">Guadeloupe</option><option value="GT">Guatemala</option><option value="GK">Guernsey</option><option value="GV">Guinea</option><option value="PU">Guinea-Bissau</option><option value="GY">Guyana</option><option value="HA">Haiti</option><option value="HM">Heard Island and McDonald Isl...</option><option value="HO">Honduras</option><option value="HK">Hong Kong S.A.R.</option><option value="HU">Hungary</option><option value="IC">Iceland</option><option value="IN">India</option><option value="ID">Indonesia</option><option value="IR">Iran</option><option value="IZ">Iraq</option><option value="EI">Ireland</option><option value="IS">Israel</option><option value="IT">Italy</option><option value="JM">Jamaica</option><option value="JN">Jan Mayen</option><option value="JA">Japan</option><option value="JE">Jersey</option><option value="JO">Jordan</option><option value="JU">Juan De Nova Island</option><option value="KZ">Kazakhstan</option><option value="KE">Kenya</option><option value="KR">Kiribati</option><option value="KN">Korea, North</option><option value="KS">Korea, South</option><option value="KV">Kosovo</option><option value="KU">Kuwait</option><option value="KG">Kyrgyzstan</option><option value="LA">Laos</option><option value="LG">Latvia</option><option value="LE">Lebanon</option><option value="LT">Lesotho</option><option value="LI">Liberia</option><option value="LY">Libya</option><option value="LS">Liechtenstein</option><option value="LH">Lithuania</option><option value="LU">Luxembourg</option><option value="MC">Macau S.A.R.</option><option value="MK">Macedonia, Republic of North</option><option value="MA">Madagascar</option><option value="MI">Malawi</option><option value="MY">Malaysia</option><option value="MV">Maldives</option><option value="ML">Mali</option><option value="MT">Malta</option><option value="IM">Man, Isle of</option><option value="RM">Marshall Islands</option><option value="MB">Martinique</option><option value="MR">Mauritania</option><option value="MP">Mauritius</option><option value="MF">Mayotte</option><option value="MX">Mexico</option><option value="MD">Moldova</option><option value="MN">Monaco</option><option value="MG">Mongolia</option><option value="MJ">Montenegro</option><option value="MH">Montserrat</option><option value="MO">Morocco</option><option value="MZ">Mozambique</option><option value="BM">Myanmar</option><option value="WA">Namibia</option><option value="NR">Nauru</option><option value="NP">Nepal</option><option value="NL">Netherlands</option><option value="NC">New Caledonia</option><option value="NZ">New Zealand</option><option value="NU">Nicaragua</option><option value="NG">Niger</option><option value="NI">Nigeria</option><option value="NE">Niue</option><option value="NF">Norfolk Island</option><option value="NO">Norway</option><option value="MU">Oman</option><option value="PK">Pakistan</option><option value="PS">Palau, the Pacific Islands of</option><option value="WE">Palestine</option><option value="PM">Panama</option><option value="PP">Papua New Guinea</option><option value="PA">Paraguay</option><option value="PE">Peru</option><option value="RP">Philippines</option><option value="PC">Pitcairn Islands</option><option value="PL">Poland</option><option value="PO">Portugal</option><option value="QA">Qatar</option><option value="RE">Reunion</option><option value="RO">Romania</option><option value="RS">Russia</option><option value="RW">Rwanda</option><option value="TB">Saint Barthelemy</option><option value="SH">Saint Helena</option><option value="SC">Saint Kitts and Nevis</option><option value="ST">Saint Lucia</option><option value="RN">Saint Martin</option><option value="SB">Saint Pierre and Miquelon</option><option value="VC">Saint Vincent and the Grenadines</option><option value="WS">Samoa</option><option value="SM">San Marino</option><option value="TP">Sao Tome and Principe</option><option value="SA">Saudi Arabia</option><option value="SG">Senegal</option><option value="RB">Serbia</option><option value="SE">Seychelles</option><option value="SL">Sierra Leone</option><option value="SN">Singapore</option><option value="NN">Sint Maarten</option><option value="LO">Slovakia</option><option value="SI">Slovenia</option><option value="BP">Solomon Islands</option><option value="SO">Somalia</option><option value="SF">South Africa</option><option value="SX">South Georgia and the South S...</option><option value="OD">South Sudan</option><option value="SP">Spain</option><option value="PG">Spratly Islands</option><option value="CE">Sri Lanka</option><option value="SU">Sudan</option><option value="NS">Suriname</option><option value="SV">Svalbard</option><option value="SW">Sweden</option><option value="SZ">Switzerland</option><option value="SY">Syria</option><option value="TW">Taiwan</option><option value="TI">Tajikistan</option><option value="TZ">Tanzania</option><option value="TH">Thailand</option><option value="TT">Timor-Leste</option><option value="TO">Togo</option><option value="TL">Tokelau</option><option value="TN">Tonga</option><option value="TD">Trinidad and Tobago</option><option value="TE">Tromelin Island</option><option value="TS">Tunisia</option><option value="TU">Turkey</option><option value="TX">Turkmenistan</option><option value="TK">Turks and Caicos Islands</option><option value="TV">Tuvalu</option><option value="UG">Uganda</option><option value="UP">Ukraine</option><option value="AE">United Arab Emirates</option><option value="UK">United Kingdom</option><option value="US">United States</option><option value="UY">Uruguay</option><option value="UZ">Uzbekistan</option><option value="NH">Vanuatu</option><option value="VT">Vatican City</option><option value="VE">Venezuela</option><option value="VM">Vietnam</option><option value="WF">Wallis and Futuna Islands</option><option value="WI">Western Sahara</option><option value="YM">Yemen</option><option value="ZA">Zambia</option><option value="ZI">Zimbabwe</option></select></div></div><div><label class="form_label" for="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_city">City</label><div class="form_responses"><input id="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_city" maxlength="64" name="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_city" size="48" type="text" value="" autocomplete="off" spellcheck="false" class="maxlength_enabled"></div></div><div class="address-row"><label class="form_label" id="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_label" for="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_region" data-default="">State</label><div class="form_responses"><select id="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_region" name="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_region" size="1" style="margin-right: 10px;" autocomplete="off"><option value="">Select State</option><option value="AL">Alabama</option><option value="AK">Alaska</option><option value="AS">American Samoa</option><option value="AA">APO/FPO (AA)</option><option value="AE">APO/FPO (AE)</option><option value="AP">APO/FPO (AP)</option><option value="AZ">Arizona</option><option value="AR">Arkansas</option><option value="CA">California</option><option value="CO">Colorado</option><option value="CT">Connecticut</option><option value="DE">Delaware</option><option value="DC">District of Columbia</option><option value="FM">Federated States of Micronesia</option><option value="FL">Florida</option><option value="GA">Georgia</option><option value="GU">Guam</option><option value="HI">Hawaii</option><option value="ID">Idaho</option><option value="IL">Illinois</option><option value="IN">Indiana</option><option value="IA">Iowa</option><option value="KS">Kansas</option><option value="KY">Kentucky</option><option value="LA">Louisiana</option><option value="ME">Maine</option><option value="MP">Mariana Islands</option><option value="MH">Marshall Islands</option><option value="MD">Maryland</option><option value="MA">Massachusetts</option><option value="MI">Michigan</option><option value="MN">Minnesota</option><option value="MS">Mississippi</option><option value="MO">Missouri</option><option value="MT">Montana</option><option value="NE">Nebraska</option><option value="NV">Nevada</option><option value="NH">New Hampshire</option><option value="NJ">New Jersey</option><option value="NM">New Mexico</option><option value="NY">New York</option><option value="NC">North Carolina</option><option value="ND">North Dakota</option><option value="OH">Ohio</option><option value="OK">Oklahoma</option><option value="OR">Oregon</option><option value="PW">Palau</option><option value="PA">Pennsylvania</option><option value="PR">Puerto Rico</option><option value="RI">Rhode Island</option><option value="SC">South Carolina</option><option value="SD">South Dakota</option><option value="TN">Tennessee</option><option value="TX">Texas</option><option value="UT">Utah</option><option value="VT">Vermont</option><option value="VI">Virgin Islands, U.S.</option><option value="VA">Virginia</option><option value="WA">Washington</option><option value="WV">West Virginia</option><option value="WI">Wisconsin</option><option value="WY">Wyoming</option></select><input id="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_region_static" type="hidden" value=""></div></div><input id="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_quality" name="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_quality" type="hidden" value=""><input id="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_dirty" name="form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_dirty" type="hidden" value="0"><script>/*<![CDATA[*/new function($) { FW.Require('address.js?v=' + FW.Version, function() { new FW.Address.Client(null,$('#form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_city'), $('#form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_region'), $('#form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_postal'),$('#form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_country'), $('#form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_label'), null, null, 'validate', null, null, $('#form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_quality'), $('#form_2e3ae3ad-e532-c7ca-4fa9-f8f05bad1e92_dirty')); }); }(FW.$);/*]]>*/</script></fieldset></div><div class="form_question form_dateym form_question_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c form_layout_table" id="form_question_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c" style="clear: left;" data-id="0da44054-4b2b-bbfa-bfc1-c3c2ff53642c" data-type="dateym" data-export="sys:school:from" data-datatype="date" data-required="1"><fieldset><legend>Start Date</legend><div class="form_label">Start Date</div><div class="form_responses"><select aria-label="Month" id="form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_m" name="form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_m" size="1" autocomplete="off" required="required"><option value=""></option><option value="01">January</option><option value="02">February</option><option value="03">March</option><option value="04">April</option><option value="05">May</option><option value="06">June</option><option value="07">July</option><option value="08">August</option><option value="09">September</option><option value="10">October</option><option value="11">November</option><option value="12">December</option></select><select aria-label="Year" id="form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_y" name="form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_y" size="1" style="margin-left: 5px;" autocomplete="off" required="required"><option></option><option value="2025">2025</option><option value="2024">2024</option><option value="2023">2023</option><option value="2022">2022</option><option value="2021">2021</option><option value="2020">2020</option><option value="2019">2019</option><option value="2018">2018</option><option value="2017">2017</option><option value="2016">2016</option><option value="2015">2015</option><option value="2014">2014</option><option value="2013">2013</option><option value="2012">2012</option><option value="2011">2011</option><option value="2010">2010</option><option value="2009">2009</option><option value="2008">2008</option><option value="2007">2007</option><option value="2006">2006</option><option value="2005">2005</option><option value="2004">2004</option><option value="2003">2003</option><option value="2002">2002</option><option value="2001">2001</option><option value="2000">2000</option><option value="1999">1999</option><option value="1998">1998</option><option value="1997">1997</option><option value="1996">1996</option><option value="1995">1995</option><option value="1994">1994</option><option value="1993">1993</option><option value="1992">1992</option><option value="1991">1991</option><option value="1990">1990</option><option value="1989">1989</option><option value="1988">1988</option><option value="1987">1987</option><option value="1986">1986</option><option value="1985">1985</option><option value="1984">1984</option><option value="1983">1983</option><option value="1982">1982</option><option value="1981">1981</option><option value="1980">1980</option><option value="1979">1979</option><option value="1978">1978</option><option value="1977">1977</option><option value="1976">1976</option><option value="1975">1975</option></select></div></fieldset></div><div class="form_question form_dateym form_question_7de0c966-ad67-f20d-f0b0-43eb5ce6b436 form_layout_table" id="form_question_7de0c966-ad67-f20d-f0b0-43eb5ce6b436" style="clear: left;" data-id="7de0c966-ad67-f20d-f0b0-43eb5ce6b436" data-type="dateym" data-export="sys:school:to" data-datatype="date" data-required="1"><fieldset><legend>End Date</legend><div class="form_label">End Date</div><div class="form_responses"><select aria-label="Month" id="form_7de0c966-ad67-f20d-f0b0-43eb5ce6b436_m" name="form_7de0c966-ad67-f20d-f0b0-43eb5ce6b436_m" size="1" autocomplete="off" required="required"><option value=""></option><option value="01">January</option><option value="02">February</option><option value="03">March</option><option value="04">April</option><option value="05">May</option><option value="06">June</option><option value="07">July</option><option value="08">August</option><option value="09">September</option><option value="10">October</option><option value="11">November</option><option value="12">December</option></select><select aria-label="Year" id="form_7de0c966-ad67-f20d-f0b0-43eb5ce6b436_y" name="form_7de0c966-ad67-f20d-f0b0-43eb5ce6b436_y" size="1" style="margin-left: 5px;" autocomplete="off" required="required"><option></option><option value="2028">2028</option><option value="2027">2027</option><option value="2026">2026</option><option value="2025">2025</option><option value="2024">2024</option><option value="2023">2023</option><option value="2022">2022</option><option value="2021">2021</option><option value="2020">2020</option><option value="2019">2019</option><option value="2018">2018</option><option value="2017">2017</option><option value="2016">2016</option><option value="2015">2015</option><option value="2014">2014</option><option value="2013">2013</option><option value="2012">2012</option><option value="2011">2011</option><option value="2010">2010</option><option value="2009">2009</option><option value="2008">2008</option><option value="2007">2007</option><option value="2006">2006</option><option value="2005">2005</option><option value="2004">2004</option><option value="2003">2003</option><option value="2002">2002</option><option value="2001">2001</option><option value="2000">2000</option><option value="1999">1999</option><option value="1998">1998</option><option value="1997">1997</option><option value="1996">1996</option><option value="1995">1995</option><option value="1994">1994</option><option value="1993">1993</option><option value="1992">1992</option><option value="1991">1991</option><option value="1990">1990</option><option value="1989">1989</option><option value="1988">1988</option><option value="1987">1987</option><option value="1986">1986</option><option value="1985">1985</option><option value="1984">1984</option><option value="1983">1983</option><option value="1982">1982</option><option value="1981">1981</option><option value="1980">1980</option><option value="1979">1979</option><option value="1978">1978</option><option value="1977">1977</option><option value="1976">1976</option><option value="1975">1975</option></select></div></fieldset></div><div class="form_question form_select form_question_75448f10-0e05-8259-eff2-985b08e2e245 form_layout_table" id="form_question_75448f10-0e05-8259-eff2-985b08e2e245" style="clear: left;" data-id="75448f10-0e05-8259-eff2-985b08e2e245" data-type="select" data-export="sys:school:type" data-required="1"><label class="form_label" for="form_75448f10-0e05-8259-eff2-985b08e2e245">Level of Study</label><div class="form_responses"><select size="1" id="form_75448f10-0e05-8259-eff2-985b08e2e245" name="form_75448f10-0e05-8259-eff2-985b08e2e245" autocomplete="off" required="required"><option></option><option value="H" data-text="High School">High School</option><option value="G" data-text="Master's/Graduate Program">Master's/Graduate Program</option><option value="U" data-text="Undergraduate">Undergraduate</option></select></div></div><div class="form_question form_dateym form_question_a984087c-3536-82c6-0d10-a07f43c320f1 form_layout_table" id="form_question_a984087c-3536-82c6-0d10-a07f43c320f1" style="clear: left;" data-id="a984087c-3536-82c6-0d10-a07f43c320f1" data-type="dateym" data-export="sys:school:conferred" data-datatype="date"><fieldset><legend>Graduation Date</legend><div class="form_label">Graduation Date</div><div class="form_responses"><select aria-label="Month" id="form_a984087c-3536-82c6-0d10-a07f43c320f1_m" name="form_a984087c-3536-82c6-0d10-a07f43c320f1_m" size="1" autocomplete="off"><option value=""></option><option value="01">January</option><option value="02">February</option><option value="03">March</option><option value="04">April</option><option value="05">May</option><option value="06">June</option><option value="07">July</option><option value="08">August</option><option value="09">September</option><option value="10">October</option><option value="11">November</option><option value="12">December</option></select><select aria-label="Year" id="form_a984087c-3536-82c6-0d10-a07f43c320f1_y" name="form_a984087c-3536-82c6-0d10-a07f43c320f1_y" size="1" style="margin-left: 5px;" autocomplete="off"><option></option><option value="2028">2028</option><option value="2027">2027</option><option value="2026">2026</option><option value="2025">2025</option><option value="2024">2024</option><option value="2023">2023</option><option value="2022">2022</option><option value="2021">2021</option><option value="2020">2020</option><option value="2019">2019</option><option value="2018">2018</option><option value="2017">2017</option><option value="2016">2016</option><option value="2015">2015</option><option value="2014">2014</option><option value="2013">2013</option><option value="2012">2012</option><option value="2011">2011</option><option value="2010">2010</option><option value="2009">2009</option><option value="2008">2008</option><option value="2007">2007</option><option value="2006">2006</option><option value="2005">2005</option><option value="2004">2004</option><option value="2003">2003</option><option value="2002">2002</option><option value="2001">2001</option><option value="2000">2000</option><option value="1999">1999</option><option value="1998">1998</option><option value="1997">1997</option><option value="1996">1996</option><option value="1995">1995</option><option value="1994">1994</option><option value="1993">1993</option><option value="1992">1992</option><option value="1991">1991</option><option value="1990">1990</option><option value="1989">1989</option><option value="1988">1988</option><option value="1987">1987</option><option value="1986">1986</option><option value="1985">1985</option><option value="1984">1984</option><option value="1983">1983</option><option value="1982">1982</option><option value="1981">1981</option><option value="1980">1980</option><option value="1979">1979</option><option value="1978">1978</option><option value="1977">1977</option><option value="1976">1976</option><option value="1975">1975</option></select></div></fieldset></div><div class="form_question form_radio form_question_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce form_layout_table" id="form_question_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce" style="clear: left;" data-id="dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce" data-type="radio" data-export="sys:school:field:recent_attend"><fieldset id="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce"><legend>Most Recently Attended</legend><div class="form_label">Most Recently Attended</div><div class="form_responses"><div class="form_response"><input id="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce_1" type="radio" data-text="Yes" name="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce" value="1" autocomplete="off"><label for="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce_1">Yes</label></div><div class="form_response"><input id="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce_2" type="radio" data-text="No" name="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce" value="0" autocomplete="off"><label for="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce_2">No</label></div></div></fieldset></div></div><div id="form_page_2" data-id="4ad8445f-25bc-e7d0-57df-51eaa43bdb6c" data-page="1" class="form_page form_page_2" style="clear: left;"></div><div id="form_page_3" data-id="2e41b856-1bf5-9b5f-4457-70c7a40f0c50" data-page="1" class="form_page form_page_3" style="clear: left;"><div class="form_question form_header form_question_6543876b-a0c9-a5b2-bc8d-ea310eb7a07f form_layout_table" id="form_question_6543876b-a0c9-a5b2-bc8d-ea310eb7a07f" style="clear: left;" data-id="6543876b-a0c9-a5b2-bc8d-ea310eb7a07f" data-type="header"><div class="form_label">Submit Transcript</div></div><div class="form_question form_p form_question_da5a569e-b8c5-fb64-59b5-4a89cc0fa0af form_layout_table" id="form_question_da5a569e-b8c5-fb64-59b5-4a89cc0fa0af" style="clear: left;" data-id="da5a569e-b8c5-fb64-59b5-4a89cc0fa0af" data-type="p"><div class="form_label">If available, a copy of your transcript from this institution. You may upload those pages now as a single- or multi-page PDF, or each page as an image file. Your scanned document may be large and may take several minutes to upload depending upon the speed of your connection.
<hr></div></div><div class="form_question form_plugin:material form_question_0569c1cc-8eee-9e81-7e34-0808e4af88b0 form_layout_table" id="form_question_0569c1cc-8eee-9e81-7e34-0808e4af88b0" style="clear: left;" data-id="0569c1cc-8eee-9e81-7e34-0808e4af88b0" data-type="plugin:material" data-export="sys:transcript"><label class="form_label" for="material_0569c1cc-8eee-9e81-7e34-0808e4af88b0">Upload copy</label><div class="form_responses"><div class="form_response"><div class="form_response_material_upload"><p><input id="material_0569c1cc-8eee-9e81-7e34-0808e4af88b0" name="material_0569c1cc-8eee-9e81-7e34-0808e4af88b0" type="file" accept=".bmp,.doc,.docx,.jpg,.jpeg,.pdf,.png,.ppt,.pptx,.txt,application/msword,application/pdf,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.openxmlformats-officedocument.wordprocessingml.document,image/*,text/*" autocomplete="off"></p></div></div></div></div></div></div></div></div><div style="clear: left;"></div></div></div><div class="action"><button class="default" onclick="if (Form.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Save</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div></form></div>

在<div class="form_question form_text form_question_a3e6d925-5740-7e51-f8a1-9f4ed538a326 form_layout_table" id="form_question_a3e6d925-5740-7e51-f8a1-9f4ed538a326" style="clear: left;" data-id="a3e6d925-5740-7e51-f8a1-9f4ed538a326" data-type="text" data-export="sys:school:name" data-required="1"><label class="form_label" for="form_a3e6d925-5740-7e51-f8a1-9f4ed538a326">Institution</label><div class="form_responses"><input type="text" size="48" id="form_a3e6d925-5740-7e51-f8a1-9f4ed538a326" name="form_a3e6d925-5740-7e51-f8a1-9f4ed538a326" value="" autocomplete="off" spellcheck="false" required="required" aria-autocomplete="list" aria-expanded="false" role="combobox" aria-owns="suggest_bff0b5bf-cf99-4775-b33a-1e54762ffc00" aria-controls="suggest_bff0b5bf-cf99-4775-b33a-1e54762ffc00" data-gtm-form-interact-field-id="1"></div></div> 中输入alb或者p或者c 

<input type="text" size="48" id="form_a3e6d925-5740-7e51-f8a1-9f4ed538a326" name="form_a3e6d925-5740-7e51-f8a1-9f4ed538a326" value="" autocomplete="off" spellcheck="false" required="required" aria-autocomplete="list" aria-expanded="false" role="combobox" aria-owns="suggest_bff0b5bf-cf99-4775-b33a-1e54762ffc00" aria-controls="suggest_bff0b5bf-cf99-4775-b33a-1e54762ffc00" data-gtm-form-interact-field-id="1"> 这里面会出现很多个元素 suggest_item会从0.1-0.10 里面你选择一个

然后<fieldset><legend>Start Date</legend><div class="form_label">Start Date</div><div class="form_responses"><select aria-label="Month" id="form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_m" name="form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_m" size="1" autocomplete="off" required="required"><option value=""></option><option value="01">January</option><option value="02">February</option><option value="03">March</option><option value="04">April</option><option value="05">May</option><option value="06">June</option><option value="07">July</option><option value="08">August</option><option value="09">September</option><option value="10">October</option><option value="11">November</option><option value="12">December</option></select><select aria-label="Year" id="form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_y" name="form_0da44054-4b2b-bbfa-bfc1-c3c2ff53642c_y" size="1" style="margin-left: 5px;" autocomplete="off" required="required"><option></option><option value="2025">2025</option><option value="2024">2024</option><option value="2023">2023</option><option value="2022">2022</option><option value="2021">2021</option><option value="2020">2020</option><option value="2019">2019</option><option value="2018">2018</option><option value="2017">2017</option><option value="2016">2016</option><option value="2015">2015</option><option value="2014">2014</option><option value="2013">2013</option><option value="2012">2012</option><option value="2011">2011</option><option value="2010">2010</option><option value="2009">2009</option><option value="2008">2008</option><option value="2007">2007</option><option value="2006">2006</option><option value="2005">2005</option><option value="2004">2004</option><option value="2003">2003</option><option value="2002">2002</option><option value="2001">2001</option><option value="2000">2000</option><option value="1999">1999</option><option value="1998">1998</option><option value="1997">1997</option><option value="1996">1996</option><option value="1995">1995</option><option value="1994">1994</option><option value="1993">1993</option><option value="1992">1992</option><option value="1991">1991</option><option value="1990">1990</option><option value="1989">1989</option><option value="1988">1988</option><option value="1987">1987</option><option value="1986">1986</option><option value="1985">1985</option><option value="1984">1984</option><option value="1983">1983</option><option value="1982">1982</option><option value="1981">1981</option><option value="1980">1980</option><option value="1979">1979</option><option value="1978">1978</option><option value="1977">1977</option><option value="1976">1976</option><option value="1975">1975</option></select></div></fieldset> 开始时间填做2021或者2022  九月份，然后 毕业时间根据前面填写的 时间来填写，月份是六月
然后<div class="form_question form_dateym form_question_a984087c-3536-82c6-0d10-a07f43c320f1 form_layout_table" id="form_question_a984087c-3536-82c6-0d10-a07f43c320f1" style="clear: left;" data-id="a984087c-3536-82c6-0d10-a07f43c320f1" data-type="dateym" data-export="sys:school:conferred" data-datatype="date"><fieldset><legend>Graduation Date</legend><div class="form_label">Graduation Date</div><div class="form_responses"><select aria-label="Month" id="form_a984087c-3536-82c6-0d10-a07f43c320f1_m" name="form_a984087c-3536-82c6-0d10-a07f43c320f1_m" size="1" autocomplete="off" data-gtm-form-interact-field-id="8"><option value=""></option><option value="01">January</option><option value="02">February</option><option value="03">March</option><option value="04">April</option><option value="05">May</option><option value="06">June</option><option value="07">July</option><option value="08">August</option><option value="09">September</option><option value="10">October</option><option value="11">November</option><option value="12">December</option></select><select aria-label="Year" id="form_a984087c-3536-82c6-0d10-a07f43c320f1_y" name="form_a984087c-3536-82c6-0d10-a07f43c320f1_y" size="1" style="margin-left: 5px;" autocomplete="off"><option></option><option value="2028">2028</option><option value="2027">2027</option><option value="2026">2026</option><option value="2025">2025</option><option value="2024">2024</option><option value="2023">2023</option><option value="2022">2022</option><option value="2021">2021</option><option value="2020">2020</option><option value="2019">2019</option><option value="2018">2018</option><option value="2017">2017</option><option value="2016">2016</option><option value="2015">2015</option><option value="2014">2014</option><option value="2013">2013</option><option value="2012">2012</option><option value="2011">2011</option><option value="2010">2010</option><option value="2009">2009</option><option value="2008">2008</option><option value="2007">2007</option><option value="2006">2006</option><option value="2005">2005</option><option value="2004">2004</option><option value="2003">2003</option><option value="2002">2002</option><option value="2001">2001</option><option value="2000">2000</option><option value="1999">1999</option><option value="1998">1998</option><option value="1997">1997</option><option value="1996">1996</option><option value="1995">1995</option><option value="1994">1994</option><option value="1993">1993</option><option value="1992">1992</option><option value="1991">1991</option><option value="1990">1990</option><option value="1989">1989</option><option value="1988">1988</option><option value="1987">1987</option><option value="1986">1986</option><option value="1985">1985</option><option value="1984">1984</option><option value="1983">1983</option><option value="1982">1982</option><option value="1981">1981</option><option value="1980">1980</option><option value="1979">1979</option><option value="1978">1978</option><option value="1977">1977</option><option value="1976">1976</option><option value="1975">1975</option></select></div></fieldset></div>
和毕业时间相同

然后<div class="form_question form_radio form_question_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce form_layout_table" id="form_question_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce" style="clear: left;" data-id="dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce" data-type="radio" data-export="sys:school:field:recent_attend"><fieldset id="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce"><legend>Most Recently Attended</legend><div class="form_label">Most Recently Attended</div><div class="form_responses"><div class="form_response"><input id="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce_1" type="radio" data-text="Yes" name="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce" value="1" autocomplete="off"><label for="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce_1">Yes</label></div><div class="form_response"><input id="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce_2" type="radio" data-text="No" name="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce" value="0" autocomplete="off"><label for="form_dd3cb2a0-1b07-4ce2-893b-82ab51d6e2ce_2">No</label></div></div></fieldset></div> 选择yes
最后点击save



