#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSU OKC 激活邮件数据管理器
负责激活邮件信息的存储、查询和统计
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Optional
import threading
from pathlib import Path

class ActivationDataManager:
    """激活邮件数据管理器"""
    
    def __init__(self, storage_dir: str = "activation_emails"):
        """
        初始化数据管理器
        
        Args:
            storage_dir: 数据存储目录
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.daily_dir = self.storage_dir / "daily"
        self.summary_dir = self.storage_dir / "summary"
        self.raw_dir = self.storage_dir / "raw"
        
        for dir_path in [self.daily_dir, self.summary_dir, self.raw_dir]:
            dir_path.mkdir(exist_ok=True)
        
        self._lock = threading.Lock()
        
    def get_daily_filename(self, date: datetime = None) -> str:
        """
        生成日期文件名
        
        Args:
            date: 日期对象，默认为当前日期
            
        Returns:
            str: 文件名
        """
        if date is None:
            date = datetime.now()
        return f"activation_emails_{date.strftime('%Y%m%d')}.json"
    
    def save_activation_emails(self, activation_emails: List[Dict]) -> str:
        """
        保存激活邮件数据
        
        Args:
            activation_emails: 激活邮件列表
            
        Returns:
            str: 保存的文件路径
        """
        if not activation_emails:
            print("⚠️ 没有激活邮件数据需要保存")
            return ""
        
        with self._lock:
            current_time = datetime.now()
            filename = self.get_daily_filename(current_time)
            file_path = self.daily_dir / filename
            
            # 准备保存的数据
            save_data = {
                "scan_timestamp": current_time.isoformat(),
                "total_emails": len(activation_emails),
                "emails": []
            }
            
            # 处理每封邮件
            for i, email in enumerate(activation_emails):
                # 分离原始邮件内容和处理后的数据
                processed_email = email.copy()
                raw_email_data = processed_email.pop("raw_email", {})
                
                # 保存原始邮件到raw目录
                if raw_email_data:
                    raw_filename = f"raw_email_{current_time.strftime('%Y%m%d_%H%M%S')}_{i+1}.json"
                    raw_file_path = self.raw_dir / raw_filename
                    
                    try:
                        with open(raw_file_path, 'w', encoding='utf-8') as f:
                            json.dump(raw_email_data, f, ensure_ascii=False, indent=2)
                        processed_email["raw_email_file"] = str(raw_filename)
                    except Exception as e:
                        print(f"⚠️ 保存原始邮件失败: {e}")
                        processed_email["raw_email_file"] = None
                
                save_data["emails"].append(processed_email)
            
            # 保存处理后的数据
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2)
                
                print(f"✅ 激活邮件数据已保存: {file_path}")
                print(f"📊 保存了 {len(activation_emails)} 封激活邮件")
                
                # 更新摘要
                self._update_summary(save_data)
                
                return str(file_path)
                
            except Exception as e:
                print(f"❌ 保存激活邮件数据失败: {e}")
                return ""
    
    def _update_summary(self, new_data: Dict):
        """
        更新数据摘要
        
        Args:
            new_data: 新的数据
        """
        summary_file = self.summary_dir / "activation_emails_summary.json"
        
        try:
            # 读取现有摘要
            if summary_file.exists():
                with open(summary_file, 'r', encoding='utf-8') as f:
                    summary = json.load(f)
            else:
                summary = {
                    "total_scans": 0,
                    "total_emails": 0,
                    "last_scan": None,
                    "daily_stats": {}
                }
            
            # 更新摘要
            scan_date = datetime.fromisoformat(new_data["scan_timestamp"]).strftime('%Y-%m-%d')
            
            summary["total_scans"] += 1
            summary["total_emails"] += new_data["total_emails"]
            summary["last_scan"] = new_data["scan_timestamp"]
            
            if scan_date not in summary["daily_stats"]:
                summary["daily_stats"][scan_date] = {
                    "scans": 0,
                    "emails": 0
                }
            
            summary["daily_stats"][scan_date]["scans"] += 1
            summary["daily_stats"][scan_date]["emails"] += new_data["total_emails"]
            
            # 保存摘要
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
                
            print(f"📈 摘要已更新: 总计 {summary['total_emails']} 封激活邮件")
            
        except Exception as e:
            print(f"⚠️ 更新摘要失败: {e}")
    
    def load_daily_emails(self, date: datetime = None) -> List[Dict]:
        """
        加载指定日期的激活邮件
        
        Args:
            date: 日期，默认为今天
            
        Returns:
            List[Dict]: 激活邮件列表
        """
        filename = self.get_daily_filename(date)
        file_path = self.daily_dir / filename
        
        if not file_path.exists():
            print(f"📭 没有找到日期 {date.strftime('%Y-%m-%d') if date else '今天'} 的激活邮件数据")
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            emails = data.get("emails", [])
            print(f"📧 加载了 {len(emails)} 封激活邮件 ({date.strftime('%Y-%m-%d') if date else '今天'})")
            return emails
            
        except Exception as e:
            print(f"❌ 加载激活邮件数据失败: {e}")
            return []
    
    def get_summary(self) -> Dict:
        """
        获取数据摘要
        
        Returns:
            Dict: 摘要信息
        """
        summary_file = self.summary_dir / "activation_emails_summary.json"
        
        if not summary_file.exists():
            return {
                "total_scans": 0,
                "total_emails": 0,
                "last_scan": None,
                "daily_stats": {}
            }
        
        try:
            with open(summary_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 读取摘要失败: {e}")
            return {}
    
    def search_emails_by_code(self, temporary_code: str) -> List[Dict]:
        """
        根据临时验证码搜索邮件
        
        Args:
            temporary_code: 临时验证码
            
        Returns:
            List[Dict]: 匹配的邮件列表
        """
        matching_emails = []
        
        # 搜索所有日期文件
        for daily_file in self.daily_dir.glob("activation_emails_*.json"):
            try:
                with open(daily_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for email in data.get("emails", []):
                    if email.get("temporary_code") == temporary_code:
                        matching_emails.append(email)
                        
            except Exception as e:
                print(f"⚠️ 搜索文件 {daily_file} 时出错: {e}")
        
        return matching_emails
    
    def search_emails_by_link_pattern(self, pattern: str) -> List[Dict]:
        """
        根据激活链接模式搜索邮件
        
        Args:
            pattern: 链接包含的模式
            
        Returns:
            List[Dict]: 匹配的邮件列表
        """
        matching_emails = []
        
        for daily_file in self.daily_dir.glob("activation_emails_*.json"):
            try:
                with open(daily_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for email in data.get("emails", []):
                    activation_link = email.get("activation_link", "")
                    if pattern.lower() in activation_link.lower():
                        matching_emails.append(email)
                        
            except Exception as e:
                print(f"⚠️ 搜索文件 {daily_file} 时出错: {e}")
        
        return matching_emails
    
    def export_all_emails(self, output_file: str = None) -> str:
        """
        导出所有激活邮件到单个文件
        
        Args:
            output_file: 输出文件路径，默认自动生成
            
        Returns:
            str: 导出文件路径
        """
        if output_file is None:
            output_file = self.storage_dir / f"all_activation_emails_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        all_emails = []
        
        # 收集所有邮件
        for daily_file in sorted(self.daily_dir.glob("activation_emails_*.json")):
            try:
                with open(daily_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    all_emails.extend(data.get("emails", []))
            except Exception as e:
                print(f"⚠️ 读取文件 {daily_file} 时出错: {e}")
        
        # 导出数据
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "total_emails": len(all_emails),
            "emails": all_emails
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已导出 {len(all_emails)} 封激活邮件到: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return ""
    
    def print_summary(self):
        """打印数据摘要"""
        summary = self.get_summary()
        
        print("\n📊 激活邮件数据摘要:")
        print(f"   总扫描次数: {summary.get('total_scans', 0)}")
        print(f"   总邮件数量: {summary.get('total_emails', 0)}")
        print(f"   最后扫描: {summary.get('last_scan', 'N/A')}")
        
        daily_stats = summary.get('daily_stats', {})
        if daily_stats:
            print("\n📅 每日统计:")
            for date, stats in sorted(daily_stats.items()):
                print(f"   {date}: {stats['emails']} 封邮件 ({stats['scans']} 次扫描)")

if __name__ == "__main__":
    # 测试用例
    manager = ActivationDataManager()
    
    # 创建测试数据
    test_emails = [
        {
            "activation_link": "https://example.com/activate?token=123",
            "temporary_code": "*********",
            "extraction_timestamp": datetime.now().isoformat(),
            "email_metadata": {
                "subject": "Activate your OSU application account",
                "from_mail": "<EMAIL>",
                "to_mail": "<EMAIL>"
            }
        }
    ]
    
    # 测试保存
    manager.save_activation_emails(test_emails)
    
    # 测试加载
    loaded_emails = manager.load_daily_emails()
    print(f"测试结果: 加载了 {len(loaded_emails)} 封邮件")
    
    # 打印摘要
    manager.print_summary() 