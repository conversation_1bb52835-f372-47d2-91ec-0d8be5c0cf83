#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册信息与激活邮件数据合并器
将registrations目录下的注册信息与activation_emails中的激活邮件数据合并
生成完整的综合JSON文件，包含去重和匹配功能
"""

import json
import os
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from difflib import SequenceMatcher

class RegistrationActivationMerger:
    """注册信息与激活邮件数据合并器"""
    
    def __init__(self, registrations_dir: str = "registrations", 
                 activation_emails_dir: str = "activation_emails/daily"):
        """
        初始化合并器
        
        Args:
            registrations_dir: 注册信息目录
            activation_emails_dir: 激活邮件目录
        """
        self.registrations_dir = Path(registrations_dir)
        self.activation_emails_dir = Path(activation_emails_dir)
        self.merged_data = []
        self.statistics = {
            "total_registrations": 0,
            "total_activation_emails": 0,
            "matched_records": 0,
            "unmatched_registrations": 0,
            "unmatched_activation_emails": 0,
            "duplicate_emails_removed": 0
        }
    
    def normalize_email(self, email: str) -> str:
        """
        标准化邮箱地址格式
        
        Args:
            email: 原始邮箱地址
            
        Returns:
            str: 标准化后的邮箱地址
        """
        # 去除角括号和空格
        email = re.sub(r'[<>\s]', '', email.lower())
        return email
    
    def calculate_name_similarity(self, name1: str, name2: str) -> float:
        """
        计算两个姓名的相似度
        
        Args:
            name1: 姓名1
            name2: 姓名2
            
        Returns:
            float: 相似度（0-1）
        """
        if not name1 or not name2:
            return 0.0
        
        # 标准化姓名（去除多余空格，转小写）
        name1 = re.sub(r'\s+', ' ', name1.lower().strip())
        name2 = re.sub(r'\s+', ' ', name2.lower().strip())
        
        # 完全匹配
        if name1 == name2:
            return 1.0
        
        # 使用序列匹配算法
        similarity = SequenceMatcher(None, name1, name2).ratio()
        
        # 检查名字和姓氏的交叉匹配
        name1_parts = name1.split()
        name2_parts = name2.split()
        
        if len(name1_parts) >= 2 and len(name2_parts) >= 2:
            # 检查名字姓氏倒置的情况
            reverse_match = (name1_parts[0] in name2_parts and name1_parts[1] in name2_parts)
            if reverse_match:
                similarity = max(similarity, 0.8)
        
        return similarity
    
    def load_registration_data(self) -> List[Dict]:
        """
        加载所有注册信息
        
        Returns:
            List[Dict]: 注册信息列表
        """
        registrations = []
        
        if not self.registrations_dir.exists():
            print(f"⚠️ 注册信息目录不存在: {self.registrations_dir}")
            return registrations
        
        print(f"📂 正在加载注册信息: {self.registrations_dir}")
        
        for json_file in self.registrations_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    registration = json.load(f)
                    registration['registration_file'] = json_file.name
                    registrations.append(registration)
            except Exception as e:
                print(f"⚠️ 加载注册信息失败 {json_file}: {e}")
        
        self.statistics["total_registrations"] = len(registrations)
        print(f"✅ 加载了 {len(registrations)} 条注册信息")
        return registrations
    
    def load_activation_emails(self) -> List[Dict]:
        """
        加载所有激活邮件数据
        
        Returns:
            List[Dict]: 激活邮件列表
        """
        activation_emails = []
        
        if not self.activation_emails_dir.exists():
            print(f"⚠️ 激活邮件目录不存在: {self.activation_emails_dir}")
            return activation_emails
        
        print(f"📧 正在加载激活邮件: {self.activation_emails_dir}")
        
        for json_file in self.activation_emails_dir.glob("activation_emails_*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    email_data = json.load(f)
                    emails = email_data.get("emails", [])
                    
                    # 为每封邮件添加来源文件信息
                    for email in emails:
                        email['activation_file'] = json_file.name
                    
                    activation_emails.extend(emails)
                    print(f"📄 从 {json_file.name} 加载了 {len(emails)} 封激活邮件")
            except Exception as e:
                print(f"⚠️ 加载激活邮件失败 {json_file}: {e}")
        
        self.statistics["total_activation_emails"] = len(activation_emails)
        print(f"✅ 总共加载了 {len(activation_emails)} 封激活邮件")
        return activation_emails
    
    def find_matching_activation(self, registration: Dict, activation_emails: List[Dict]) -> Optional[Dict]:
        """
        为注册信息找到匹配的激活邮件
        
        Args:
            registration: 注册信息
            activation_emails: 激活邮件列表
            
        Returns:
            Optional[Dict]: 匹配的激活邮件
        """
        reg_email = self.normalize_email(registration.get("email", ""))
        reg_name = registration.get("full_name", "")
        
        best_match = None
        best_score = 0.0
        
        for email in activation_emails:
            # 邮箱地址匹配
            email_to = self.normalize_email(email.get("email_metadata", {}).get("to_mail", ""))
            email_match = (reg_email == email_to)
            
            # 姓名相似度匹配
            email_recipient = email.get("recipient_name", "")
            name_similarity = self.calculate_name_similarity(reg_name, email_recipient)
            
            # 综合评分（邮箱匹配权重更高）
            if email_match:
                score = 0.7 + (name_similarity * 0.3)  # 邮箱匹配70%，姓名匹配30%
            else:
                score = name_similarity * 0.5  # 仅姓名匹配时降低权重
            
            # 更新最佳匹配
            if score > best_score and score > 0.6:  # 设置最低匹配阈值
                best_score = score
                best_match = email
                best_match['match_score'] = score
                best_match['match_details'] = {
                    'email_match': email_match,
                    'name_similarity': name_similarity,
                    'compared_emails': f"{reg_email} <-> {email_to}",
                    'compared_names': f"{reg_name} <-> {email_recipient}"
                }
        
        return best_match
    
    def merge_data(self) -> List[Dict]:
        """
        合并注册信息和激活邮件数据
        
        Returns:
            List[Dict]: 合并后的数据列表
        """
        print("\n🔄 开始数据合并...")
        
        # 加载数据
        registrations = self.load_registration_data()
        activation_emails = self.load_activation_emails()
        
        if not registrations and not activation_emails:
            print("❌ 没有找到任何数据文件")
            return []
        
        merged_records = []
        used_activation_emails = set()
        email_deduplication = {}  # 用于去重
        
        print(f"\n🔍 开始匹配注册信息和激活邮件...")
        
        # 为每个注册信息查找匹配的激活邮件
        for i, registration in enumerate(registrations, 1):
            reg_email = self.normalize_email(registration.get("email", ""))
            
            # 检查去重
            if reg_email in email_deduplication:
                print(f"🚫 发现重复邮箱，跳过: {reg_email}")
                self.statistics["duplicate_emails_removed"] += 1
                continue
            
            # 查找匹配的激活邮件
            available_emails = [email for email in activation_emails 
                              if email.get("email_metadata", {}).get("mail_id") not in used_activation_emails]
            
            matching_email = self.find_matching_activation(registration, available_emails)
            
            # 创建合并记录
            merged_record = {
                "registration_info": registration,
                "activation_email": matching_email,
                "merge_timestamp": datetime.now().isoformat(),
                "status": "matched" if matching_email else "registration_only",
                "unique_id": f"reg_{i}_{reg_email.replace('@', '_at_').replace('.', '_')}"
            }
            
            if matching_email:
                used_activation_emails.add(matching_email.get("email_metadata", {}).get("mail_id"))
                self.statistics["matched_records"] += 1
                print(f"✅ [{i:3d}] 匹配成功: {registration.get('full_name')} -> {matching_email.get('recipient_name')} (评分: {matching_email.get('match_score', 0):.2f})")
            else:
                self.statistics["unmatched_registrations"] += 1
                print(f"⚠️ [{i:3d}] 未找到匹配: {registration.get('full_name')} ({reg_email})")
            
            merged_records.append(merged_record)
            email_deduplication[reg_email] = True
        
        # 处理未匹配的激活邮件
        unmatched_emails = [email for email in activation_emails 
                           if email.get("email_metadata", {}).get("mail_id") not in used_activation_emails]
        
        for email in unmatched_emails:
            email_to = self.normalize_email(email.get("email_metadata", {}).get("to_mail", ""))
            
            # 检查去重
            if email_to in email_deduplication:
                continue
            
            merged_record = {
                "registration_info": None,
                "activation_email": email,
                "merge_timestamp": datetime.now().isoformat(),
                "status": "activation_only",
                "unique_id": f"email_{email.get('email_metadata', {}).get('mail_id', 'unknown')}"
            }
            
            merged_records.append(merged_record)
            email_deduplication[email_to] = True
            self.statistics["unmatched_activation_emails"] += 1
        
        self.merged_data = merged_records
        print(f"\n📊 数据合并完成！")
        return merged_records
    
    def generate_report(self) -> Dict:
        """
        生成合并统计报告
        
        Returns:
            Dict: 统计报告
        """
        report = {
            "merge_summary": self.statistics.copy(),
            "merge_timestamp": datetime.now().isoformat(),
            "data_sources": {
                "registrations_dir": str(self.registrations_dir),
                "activation_emails_dir": str(self.activation_emails_dir)
            },
            "quality_metrics": {
                "match_rate": self.statistics["matched_records"] / max(self.statistics["total_registrations"], 1) * 100,
                "email_coverage": self.statistics["matched_records"] / max(self.statistics["total_activation_emails"], 1) * 100,
                "duplicate_rate": self.statistics["duplicate_emails_removed"] / max(self.statistics["total_registrations"] + self.statistics["total_activation_emails"], 1) * 100
            }
        }
        
        return report
    
    def save_merged_data(self, output_file: str = None) -> str:
        """
        保存合并后的数据到JSON文件
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            str: 保存的文件路径
        """
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"merged_registration_activation_{timestamp}.json"
        
        # 生成完整输出数据
        output_data = {
            "report": self.generate_report(),
            "merged_records": self.merged_data
        }
        
        # 保存文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 合并数据已保存到: {output_file}")
        print(f"📁 文件大小: {os.path.getsize(output_file) / 1024:.1f} KB")
        
        return output_file
    
    def print_statistics(self):
        """打印合并统计信息"""
        stats = self.statistics
        
        print(f"\n📊 合并统计报告")
        print(f"=" * 50)
        print(f"📝 总注册信息数: {stats['total_registrations']}")
        print(f"📧 总激活邮件数: {stats['total_activation_emails']}")
        print(f"✅ 成功匹配数: {stats['matched_records']}")
        print(f"⚠️ 未匹配注册: {stats['unmatched_registrations']}")
        print(f"⚠️ 未匹配邮件: {stats['unmatched_activation_emails']}")
        print(f"🚫 去重移除数: {stats['duplicate_emails_removed']}")
        
        if stats['total_registrations'] > 0:
            match_rate = stats['matched_records'] / stats['total_registrations'] * 100
            print(f"📈 匹配成功率: {match_rate:.1f}%")
        
        print(f"=" * 50)

def main():
    """主函数"""
    print("🔗 注册信息与激活邮件数据合并器")
    print("=" * 60)
    
    # 创建合并器
    merger = RegistrationActivationMerger()
    
    # 执行数据合并
    merged_data = merger.merge_data()
    
    if merged_data:
        # 显示统计信息
        merger.print_statistics()
        
        # 保存合并数据
        output_file = merger.save_merged_data()
        
        print(f"\n🎉 数据合并完成！")
        print(f"📄 输出文件: {output_file}")
        print(f"📝 总记录数: {len(merged_data)}")
        
        # 询问是否查看详细信息
        choice = input("\n是否查看详细匹配信息？(y/n): ").strip().lower()
        if choice == 'y':
            print(f"\n📋 详细匹配信息：")
            for i, record in enumerate(merged_data[:10], 1):  # 只显示前10条
                status = record['status']
                if record['registration_info']:
                    reg_name = record['registration_info'].get('full_name', 'N/A')
                    reg_email = record['registration_info'].get('email', 'N/A')
                else:
                    reg_name = reg_email = 'N/A'
                
                if record['activation_email']:
                    act_name = record['activation_email'].get('recipient_name', 'N/A')
                    act_email = record['activation_email'].get('email_metadata', {}).get('to_mail', 'N/A')
                    match_score = record['activation_email'].get('match_score', 0)
                else:
                    act_name = act_email = 'N/A'
                    match_score = 0
                
                print(f"{i:2d}. 状态: {status}")
                print(f"    注册: {reg_name} ({reg_email})")
                print(f"    邮件: {act_name} ({act_email})")
                if match_score > 0:
                    print(f"    匹配分: {match_score:.2f}")
                print()
    else:
        print("❌ 没有找到任何数据进行合并")

if __name__ == "__main__":
    main() 