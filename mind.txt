任务详情：
1进入：https://apply.osuokc.edu/apply/
快速定位到：<table class="fixed" role="none" style="width: 700px;"><colgroup><col style="width: 50%;"><col style="width: 50%;"></colgroup><tbody><tr><td><h2>Returning users:</h2>
    If you started an application but did not finish, load your account using your personal email address and password.
  <p><b><a href="/account/login?r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f">Log in</a></b></p></td><td><h2>First-time users:</h2>
    Create an account and get started!
  <p><b><a href="/account/register?r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f"><span>Create an account</span></a></b></p></td></tr></tbody></table>

点击<p><b><a href="/account/register?r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f"><span>Create an account</span></a></b></p>

会跳转到一个新的页面
等待元素加载后
<table class="plain" role="none"><colgroup><col style="width: 150px;"><col></colgroup><tbody><tr><td><label for="email" data-required="1">Email Address</label></td><td><input id="email" maxlength="64" name="email" size="48" type="email" value="" data-validate="{ required: true, format: 'email', help: 'Email address is required.' }" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></td></tr><tr><td><label for="first" data-required="1">First Name</label></td><td><input id="first" maxlength="64" name="first" size="32" type="text" value="" data-validate="{ required: true, help: 'First name is required.' }" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></td></tr><tr><td><label for="last" data-required="1">Last Name</label></td><td><input id="last" maxlength="64" name="last" size="32" type="text" value="" data-validate="{ required: true, help: 'Last name is required.' }" autocomplete="off" spellcheck="false" class="maxlength_enabled" required="required"></td></tr><tr><td><label for="birthdate">Birthdate</label></td><td><fieldset id="birthdate"><select aria-label="Month" id="birthdate_m" name="birthdate_m" size="1" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option value=""></option><option value="01">January</option><option value="02">February</option><option value="03">March</option><option value="04">April</option><option value="05">May</option><option value="06">June</option><option value="07">July</option><option value="08">August</option><option value="09">September</option><option value="10">October</option><option value="11">November</option><option value="12">December</option></select><select aria-label="Day" id="birthdate_d" name="birthdate_d" size="1" style="margin-left: 5px;" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option value=""></option><option value="01">1</option><option value="02">2</option><option value="03">3</option><option value="04">4</option><option value="05">5</option><option value="06">6</option><option value="07">7</option><option value="08">8</option><option value="09">9</option><option value="10">10</option><option value="11">11</option><option value="12">12</option><option value="13">13</option><option value="14">14</option><option value="15">15</option><option value="16">16</option><option value="17">17</option><option value="18">18</option><option value="19">19</option><option value="20">20</option><option value="21">21</option><option value="22">22</option><option value="23">23</option><option value="24">24</option><option value="25">25</option><option value="26">26</option><option value="27">27</option><option value="28">28</option><option value="29">29</option><option value="30">30</option><option value="31">31</option></select><select aria-label="Year" id="birthdate_y" name="birthdate_y" size="1" style="margin-left: 5px;" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option></option><option value="2015">2015</option><option value="2014">2014</option><option value="2013">2013</option><option value="2012">2012</option><option value="2011">2011</option><option value="2010">2010</option><option value="2009">2009</option><option value="2008">2008</option><option value="2007">2007</option><option value="2006">2006</option><option value="2005">2005</option><option value="2004">2004</option><option value="2003">2003</option><option value="2002">2002</option><option value="2001">2001</option><option value="2000">2000</option><option value="1999">1999</option><option value="1998">1998</option><option value="1997">1997</option><option value="1996">1996</option><option value="1995">1995</option><option value="1994">1994</option><option value="1993">1993</option><option value="1992">1992</option><option value="1991">1991</option><option value="1990">1990</option><option value="1989">1989</option><option value="1988">1988</option><option value="1987">1987</option><option value="1986">1986</option><option value="1985">1985</option><option value="1984">1984</option><option value="1983">1983</option><option value="1982">1982</option><option value="1981">1981</option><option value="1980">1980</option><option value="1979">1979</option><option value="1978">1978</option><option value="1977">1977</option><option value="1976">1976</option><option value="1975">1975</option><option value="1974">1974</option><option value="1973">1973</option><option value="1972">1972</option><option value="1971">1971</option><option value="1970">1970</option><option value="1969">1969</option><option value="1968">1968</option><option value="1967">1967</option><option value="1966">1966</option><option value="1965">1965</option><option value="1964">1964</option><option value="1963">1963</option><option value="1962">1962</option><option value="1961">1961</option><option value="1960">1960</option><option value="1959">1959</option><option value="1958">1958</option><option value="1957">1957</option><option value="1956">1956</option><option value="1955">1955</option><option value="1954">1954</option><option value="1953">1953</option><option value="1952">1952</option><option value="1951">1951</option><option value="1950">1950</option><option value="1949">1949</option><option value="1948">1948</option><option value="1947">1947</option><option value="1946">1946</option><option value="1945">1945</option><option value="1944">1944</option><option value="1943">1943</option><option value="1942">1942</option><option value="1941">1941</option><option value="1940">1940</option><option value="1939">1939</option><option value="1938">1938</option><option value="1937">1937</option><option value="1936">1936</option><option value="1935">1935</option><option value="1934">1934</option><option value="1933">1933</option><option value="1932">1932</option><option value="1931">1931</option><option value="1930">1930</option><option value="1929">1929</option><option value="1928">1928</option><option value="1927">1927</option><option value="1926">1926</option><option value="1925">1925</option><option value="1924">1924</option><option value="1923">1923</option><option value="1922">1922</option><option value="1921">1921</option><option value="1920">1920</option><option value="1919">1919</option><option value="1918">1918</option><option value="1917">1917</option><option value="1916">1916</option><option value="1915">1915</option></select></fieldset></td></tr></tbody></table>


发现了该表单
上面填入随机生成的邮箱地址，我们的邮箱地址
person_info.csv库中对应的姓名
出生日期统一为01/01/2005

填写完成后点击
<div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Postback(this, { cmd: 'submit' }); return false;" type="submit">Continue</button></div> 中的<button class="default" onclick="if (FW.Validate(this)) FW.Postback(this, { cmd: 'submit' }); return false;" type="submit">Continue</button> 按钮

然后监听
<p class="success">A temporary PIN has been sent to your email address. If you do not receive this message in the next few minutes, please check your junk mail folder.</p>
出现后，去邮件中寻找pin码
邮件格式：
<div id=":mv" class="a3s aiL msg4571657460116420926"><u></u>






<div>
<table border="0" cellpadding="0" cellspacing="0" width="100%"><tbody><tr><td align="center" bgcolor="#D0D0CE" style="background-color:#d0d0ce"><table align="center" border="0" cellpadding="0" cellspacing="0" width="500"><tbody><tr><td align="center" bgcolor="#D0D0CE" style="font-size:0;padding-left:0px;padding-bottom:0px;padding-right:0px;border-bottom:0px" valign="top"><table align="center" border="0" cellpadding="0" cellspacing="0" width="250"><tbody><tr><td align="left"><img alt="OSU OKC Office of Admissions" style="display:block;width:600px" width="600" src="https://ci3.googleusercontent.com/meips/ADKq_NYF35f5CBXs4gU5TFHya_r9rHW1HNJlA8ZtSXKd_TTMPIahhU1NnOODcJQYH9pPGyD55pgUtQoQksVS2glledRgoB8EFsMSloUwAqY6S-uSxocRITFmicUV698r1HkEPOQETdqKefmA=s0-d-e1-ft#https://apply.osuokc.edu/www/images/Admissions/Office%20of%20Admissions_Header.png" class="CToWUd a6T" data-bit="iit" tabindex="0"><div class="a6S" dir="ltr" style="opacity: 0.01; left: 718.5px; top: 148px;"><span data-is-tooltip-wrapper="true" class="a5q" jsaction="JIbuQc:.CLIENT"><button class="VYBDae-JX-I VYBDae-JX-I-ql-ay5-ays CgzRE" jscontroller="PIVayb" jsaction="click:h5M12e; clickmod:h5M12e;pointerdown:FEiYhc;pointerup:mF5Elf;pointerenter:EX0mI;pointerleave:vpvbp;pointercancel:xyn4sd;contextmenu:xexox;focus:h06R8; blur:zjh6rb;mlnRJb:fLiPzd;" data-idom-class="CgzRE" data-use-native-focus-logic="true" jsname="hRZeKc" aria-label="Download attachment " data-tooltip-enabled="true" data-tooltip-id="tt-c39" data-tooltip-classes="AZPksf" id="" jslog="91252; u014N:cOuCgd,Kr2w4b,xr6bB; 4:WyIjbXNnLWY6MTg0MDExNjQyODQwOTQ3NzQxMCJd; 43:WyJpbWFnZS9qcGVnIl0."><span class="OiePBf-zPjgPe VYBDae-JX-UHGRz"></span><span class="bHC-Q" jscontroller="LBaJxb" jsname="m9ZlFb" soy-skip="" ssk="6:RWVI5c"></span><span class="VYBDae-JX-ank-Rtc0Jf" jsname="S5tZuc" aria-hidden="true"><span class="notranslate bzc-ank" aria-hidden="true"><svg viewBox="0 -960 960 960" height="20" width="20" focusable="false" class=" aoH"><path d="M480-336L288-528l51-51L444-474V-816h72v342L621-579l51,51L480-336ZM263.72-192Q234-192 213-213.15T192-264v-72h72v72H696v-72h72v72q0,29.7-21.16,50.85T695.96-192H263.72Z"></path></svg></span></span><div class="VYBDae-JX-ano"></div></button><div class="ne2Ple-oshW8e-J9" id="tt-c39" role="tooltip" aria-hidden="true">Download</div></span></div></td></tr></tbody></table></td></tr><tr><td align="center" bgcolor="#FFFFFF" style="padding:20px;background-color:#ffffff"><table align="center" border="0" cellpadding="0" cellspacing="0" width="100%"><tbody><tr><td style="font-family:Open Sans,Helvetica,Arial,sans-serif;font-size:16px;font-weight:400;line-height:24px;padding-top:5px;padding-bottom:0px"><p>
                        Dear Danial Sawayn,<span class="im"><br><br>
                        Thank you for registering your application account with Oklahoma State University - OKC.<br><br>
                        Before you can apply, you need to <strong><a href="https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz6EMp1X11oSY-PJedVf8sXVFULJzTvbCv5ULLpWVEqMt6Dv2o0QYCavp6ytMvGlIXZ_YYmEWVJcTGAVgR8St102_ZArxNaVtaUlc5d4pi2TAAg-E1KXu46j-hPpm_ZQC2t5f7KEBth1BroJoVdSFKow/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h0/h001.0QbvijZ5xjFRmRoHqF9QIGdeE1oaK4T9kdoHh2MiyYU" title="https://apply.osuokc.edu/account/login?eid=Ozn7zH5UF2Ofw7OYYAIHamtTo6DpFhV4R5q1WbiugPaRGQwtJ3LyFA&amp;s=n&amp;r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz6EMp1X11oSY-PJedVf8sXVFULJzTvbCv5ULLpWVEqMt6Dv2o0QYCavp6ytMvGlIXZ_YYmEWVJcTGAVgR8St102_ZArxNaVtaUlc5d4pi2TAAg-E1KXu46j-hPpm_ZQC2t5f7KEBth1BroJoVdSFKow/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h0/h001.0QbvijZ5xjFRmRoHqF9QIGdeE1oaK4T9kdoHh2MiyYU&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw0xSdvwpPZ1G4RhYhlNhu4t">activate your account</a></strong>.<br><br></span>
                        When activating, please use this temporary code:&nbsp;<strong>*********</strong></p></td></tr></tbody></table></td></tr><tr><td align="center" bgcolor="#D0D0CE" style="padding-left:0px;padding-right:0px;background-color:#d0d0ce"><table align="center" border="0" cellpadding="0" cellspacing="0" width="100%"><tbody><tr><td align="center" bgcolor="#D0D0CE" style="font-size:0;padding-left:0px;padding-bottom:0px;padding-right:0px" valign="top"></td></tr><tr><td align="center" bgcolor="#D0D0CE" style="padding:0px;background-color:#d0d0ce"><table align="center" border="0" cellpadding="0" cellspacing="0" width="100%"><tbody><tr><td align="left" style="font-family:Open Sans,Helvetica,Arial,sans-serif;font-size:16px;font-weight:400;line-height:10px;padding-top:5px;padding-bottom:0px"><table align="center" cellpadding="5" cellspacing="0" style="width:500px" width="100%"><tbody><tr><td><div align="left" id="m_4571657460116420926container2"><div style="display:inline-block"><span style="font-family:Verdana,Geneva,sans-serif"><span style="font-size:9px"><strong>Office of Admissions</strong></span></span><br><span style="font-family:Verdana,Geneva,sans-serif"><span style="font-size:9px">Oklahoma State University - OKC</span></span><br><span style="font-family:Verdana,Geneva,sans-serif"><span style="font-size:9px">900 N. Portland Ave. | Oklahoma City, OK 73107</span></span><br><span style="font-family:Verdana,Geneva,sans-serif"><span style="font-size:9px"><a href="tel:************" title="tel:************" target="_blank">************</a> | <strong><a href="https://mx.technolutions.net/ss/c/u001.XEfBm45kvnMxKIc7tKAAI6IevuM8fwjSGTNxZf8i_C4/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h1/h001._6XO0PhQ5JMbqVsV2ESl4aPcXf8hCJfa9CbImRmotc4" title="https://osuokc.edu/" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.XEfBm45kvnMxKIc7tKAAI6IevuM8fwjSGTNxZf8i_C4/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h1/h001._6XO0PhQ5JMbqVsV2ESl4aPcXf8hCJfa9CbImRmotc4&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw3AkNFtmDW3HwPMX51YD0BP">osuokc.edu</a></strong></span></span></div></div></td><td style="width:246px;white-space:nowrap;text-align:right" valign="center"><a href="https://mx.technolutions.net/ss/c/u001.jxaZRujEt1lhE0WEp4QewFt-Xjd8vEyIQf0VhGguqDLnN8dIg9edM4Cd36X5qLef/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h2/h001.-u3esek8uiCnMoPnXYm7YxA6F6tGwF5WO3-ma5adr5I" title="https://www.facebook.com/osuokc" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.jxaZRujEt1lhE0WEp4QewFt-Xjd8vEyIQf0VhGguqDLnN8dIg9edM4Cd36X5qLef/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h2/h001.-u3esek8uiCnMoPnXYm7YxA6F6tGwF5WO3-ma5adr5I&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw1QeL_dTq5xEcRwIb7-kHap"><img alt="Facebook" style="width:45px;height:45px" height="45" width="45" src="https://ci3.googleusercontent.com/meips/ADKq_NYiWEAZw_u2uENv-4_EgfjVKgH-B5nIZi03Wf23UPoNkgi1S0vgNQG9WF0wsxL8lXu83moqpWQfSFbEdStrlIMlt6AFrauv5w7NSQ=s0-d-e1-ft#https://orange.okstate.edu/www/images/new-icon-fb.png" class="CToWUd" data-bit="iit"></a>&nbsp;<a href="https://mx.technolutions.net/ss/c/u001.gdErue62yx9LFCvq4pzlnmPXkrX7YbUsoJRFtrt6Wjs/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h3/h001.5Kk3R8U2RTM7am9edImCQAtRmLA-T4kTgnMaqyuEEgo" title="https://x.com/OSUOKC" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.gdErue62yx9LFCvq4pzlnmPXkrX7YbUsoJRFtrt6Wjs/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h3/h001.5Kk3R8U2RTM7am9edImCQAtRmLA-T4kTgnMaqyuEEgo&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw3dWyQwCSXx3QoOfaQBCwo9"><img alt="Twitter" style="width:45px;height:45px" height="45" width="45" src="https://ci3.googleusercontent.com/meips/ADKq_Nb9icjoWvDj1AKG5-8rxzdiU6sMoayICok9WZsDTV--7CiXHLF1g08w9FrF32FDkd6VKFkNwkSngPKz69KPg2F8qkHKHGLqZdk9=s0-d-e1-ft#https://orange.okstate.edu/www/images/new-icon-x.png" class="CToWUd" data-bit="iit"></a>&nbsp;<a href="https://mx.technolutions.net/ss/c/u001.jxaZRujEt1lhE0WEp4QewNyUWzt-M6pZ_FXvPJGcmVpHKNG5NudWcTeL0BSdlywa/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h4/h001.0qX3M_eQhmI8TrZbVfFzsxTqyDAbu0FtY0_1XaQrjPc" title="https://www.instagram.com/osu.okc" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.jxaZRujEt1lhE0WEp4QewNyUWzt-M6pZ_FXvPJGcmVpHKNG5NudWcTeL0BSdlywa/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h4/h001.0qX3M_eQhmI8TrZbVfFzsxTqyDAbu0FtY0_1XaQrjPc&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw2v8QzQ5H8X6UEhJ7EZXnUY"><img alt="Instagram" style="width:45px;height:45px" height="45" width="45" src="https://ci3.googleusercontent.com/meips/ADKq_Nanvy5JZLejcubh8E_LHK-nupJrl47AbQKriRmdnsb6VCXbTW922ZwbgJa0OZ465uqityoSeGChspM8aDId53fFxdGdZowBOPA103EzKg=s0-d-e1-ft#https://orange.okstate.edu/www/images/new-icon-insta.png" class="CToWUd" data-bit="iit"></a>&nbsp;<a href="mailto:<EMAIL>" title="mailto:<EMAIL>" target="_blank"><img alt="Email" style="width:45px;height:45px" height="45" width="45" src="https://ci3.googleusercontent.com/meips/ADKq_NYNDbCDd1mxpViFtx6ujGcRVQRGmL-63dwBaff0sIbNGlcoPT40ISsRpznfe4ZwgcqJUL5wHSRTuby1DFKIk7b_bLLfYz74mGLLzo7sww=s0-d-e1-ft#https://orange.okstate.edu/www/images/new-icon-email.png" class="CToWUd" data-bit="iit"></a></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><br>
          &nbsp;</td></tr></tbody></table>
<img src="https://ci3.googleusercontent.com/meips/ADKq_NYQFheFOG-M19LR864exP2QzixuoIMiV8NydYjNldjQBQkr5qFXn4KxiE3uCHlWuMNvuilnI1gujPdw6vYiohnQV-aO3yghwQ-g4nxCFnoBBG8bAcchu_QxbfMgQJ0cBvMFVB0zhG3ddiCFwtCcZ1eFmigrAA=s0-d-e1-ft#https://mx.technolutions.net/ss/o/u001.l7ggKG0fqQ6Gg4_ean_HcQ/4iy/qNjjPtp0TjSMqMnbQS8ULQ/ho.gif" alt="" width="1" height="1" border="0" style="height:1px!important;width:1px!important;border-width:0!important;margin-top:0!important;margin-bottom:0!important;margin-right:0!important;margin-left:0!important;padding-top:0!important;padding-bottom:0!important;padding-right:0!important;padding-left:0!important" class="CToWUd" data-bit="iit"></div><div class="yj6qo"></div><div class="adL">
</div></div>
中的 <td style="font-family:Open Sans,Helvetica,Arial,sans-serif;font-size:16px;font-weight:400;line-height:24px;padding-top:5px;padding-bottom:0px"><p>
                        Dear Danial Sawayn,<span class="im"><br><br>
                        Thank you for registering your application account with Oklahoma State University - OKC.<br><br>
                        Before you can apply, you need to <strong><a href="https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz6EMp1X11oSY-PJedVf8sXVFULJzTvbCv5ULLpWVEqMt6Dv2o0QYCavp6ytMvGlIXZ_YYmEWVJcTGAVgR8St102_ZArxNaVtaUlc5d4pi2TAAg-E1KXu46j-hPpm_ZQC2t5f7KEBth1BroJoVdSFKow/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h0/h001.0QbvijZ5xjFRmRoHqF9QIGdeE1oaK4T9kdoHh2MiyYU" title="https://apply.osuokc.edu/account/login?eid=Ozn7zH5UF2Ofw7OYYAIHamtTo6DpFhV4R5q1WbiugPaRGQwtJ3LyFA&amp;s=n&amp;r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://mx.technolutions.net/ss/c/u001.cpQnkll3XXHbQrBNB1xm0ExSHM1xBL3_rW5yvoABnlQKYJcYWacLz_njMXTY9tsz6EMp1X11oSY-PJedVf8sXVFULJzTvbCv5ULLpWVEqMt6Dv2o0QYCavp6ytMvGlIXZ_YYmEWVJcTGAVgR8St102_ZArxNaVtaUlc5d4pi2TAAg-E1KXu46j-hPpm_ZQC2t5f7KEBth1BroJoVdSFKow/4iy/qNjjPtp0TjSMqMnbQS8ULQ/h0/h001.0QbvijZ5xjFRmRoHqF9QIGdeE1oaK4T9kdoHh2MiyYU&amp;source=gmail&amp;ust=****************&amp;usg=AOvVaw0xSdvwpPZ1G4RhYhlNhu4t">activate your account</a></strong>.<br><br></span>
                        When activating, please use this temporary code:&nbsp;<strong>*********</strong></p></td> 中的 <strong>*********</strong> 元素里面会有pin码

获取到pin之后
在页面中输入
<table class="plain" role="none"><colgroup><col style="width: 150px;"><col></colgroup><tbody><tr><th>Email</th><td><EMAIL><a href="/account/login?id=&amp;r=https%3a%2f%2fapply.osuokc.edu%2fapply%2f" style="margin-left: 1em;">switch</a></td></tr><tr><th>Account</th><td><input name="eid" type="hidden" value="Ozn7zH5UF2Ofw7OYYAIHamtTo6DpFhV4R5q1WbiugPaRGQwtJ3LyFA">Sawayn, Danial</td></tr><tr><th><label for="password" data-required="1">Temporary PIN</label></th><td><input id="password" maxlength="64" name="password" size="48" style="width: 200px;" type="password" value="" data-validate="{ required: true, help: 'Password is required.' }" autocomplete="off" spellcheck="false" required="required"></td></tr><tr><th><label for="birthdate">Birthdate</label></th><td><fieldset id="birthdate"><select aria-label="Month" id="birthdate_m" name="birthdate_m" size="1" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option value=""></option><option value="01">January</option><option value="02">February</option><option value="03">March</option><option value="04">April</option><option value="05">May</option><option value="06">June</option><option value="07">July</option><option value="08">August</option><option value="09">September</option><option value="10">October</option><option value="11">November</option><option value="12">December</option></select><select aria-label="Day" id="birthdate_d" name="birthdate_d" size="1" style="margin-left: 5px;" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option value=""></option><option value="01">1</option><option value="02">2</option><option value="03">3</option><option value="04">4</option><option value="05">5</option><option value="06">6</option><option value="07">7</option><option value="08">8</option><option value="09">9</option><option value="10">10</option><option value="11">11</option><option value="12">12</option><option value="13">13</option><option value="14">14</option><option value="15">15</option><option value="16">16</option><option value="17">17</option><option value="18">18</option><option value="19">19</option><option value="20">20</option><option value="21">21</option><option value="22">22</option><option value="23">23</option><option value="24">24</option><option value="25">25</option><option value="26">26</option><option value="27">27</option><option value="28">28</option><option value="29">29</option><option value="30">30</option><option value="31">31</option></select><select aria-label="Year" id="birthdate_y" name="birthdate_y" size="1" style="margin-left: 5px;" data-validate="{ required: true, help: 'Birthdate is required.' }" autocomplete="off" required="required"><option></option><option value="2025">2025</option><option value="2024">2024</option><option value="2023">2023</option><option value="2022">2022</option><option value="2021">2021</option><option value="2020">2020</option><option value="2019">2019</option><option value="2018">2018</option><option value="2017">2017</option><option value="2016">2016</option><option value="2015">2015</option><option value="2014">2014</option><option value="2013">2013</option><option value="2012">2012</option><option value="2011">2011</option><option value="2010">2010</option><option value="2009">2009</option><option value="2008">2008</option><option value="2007">2007</option><option value="2006">2006</option><option value="2005">2005</option><option value="2004">2004</option><option value="2003">2003</option><option value="2002">2002</option><option value="2001">2001</option><option value="2000">2000</option><option value="1999">1999</option><option value="1998">1998</option><option value="1997">1997</option><option value="1996">1996</option><option value="1995">1995</option><option value="1994">1994</option><option value="1993">1993</option><option value="1992">1992</option><option value="1991">1991</option><option value="1990">1990</option><option value="1989">1989</option><option value="1988">1988</option><option value="1987">1987</option><option value="1986">1986</option><option value="1985">1985</option><option value="1984">1984</option><option value="1983">1983</option><option value="1982">1982</option><option value="1981">1981</option><option value="1980">1980</option><option value="1979">1979</option><option value="1978">1978</option><option value="1977">1977</option><option value="1976">1976</option><option value="1975">1975</option><option value="1974">1974</option><option value="1973">1973</option><option value="1972">1972</option><option value="1971">1971</option><option value="1970">1970</option><option value="1969">1969</option><option value="1968">1968</option><option value="1967">1967</option><option value="1966">1966</option><option value="1965">1965</option><option value="1964">1964</option><option value="1963">1963</option><option value="1962">1962</option><option value="1961">1961</option><option value="1960">1960</option><option value="1959">1959</option><option value="1958">1958</option><option value="1957">1957</option><option value="1956">1956</option><option value="1955">1955</option><option value="1954">1954</option><option value="1953">1953</option><option value="1952">1952</option><option value="1951">1951</option><option value="1950">1950</option><option value="1949">1949</option><option value="1948">1948</option><option value="1947">1947</option><option value="1946">1946</option><option value="1945">1945</option><option value="1944">1944</option><option value="1943">1943</option><option value="1942">1942</option><option value="1941">1941</option><option value="1940">1940</option><option value="1939">1939</option><option value="1938">1938</option><option value="1937">1937</option><option value="1936">1936</option><option value="1935">1935</option><option value="1934">1934</option><option value="1933">1933</option><option value="1932">1932</option><option value="1931">1931</option><option value="1930">1930</option><option value="1929">1929</option><option value="1928">1928</option><option value="1927">1927</option><option value="1926">1926</option><option value="1925">1925</option><option value="1924">1924</option><option value="1923">1923</option><option value="1922">1922</option><option value="1921">1921</option><option value="1920">1920</option><option value="1919">1919</option><option value="1918">1918</option><option value="1917">1917</option><option value="1916">1916</option><option value="1915">1915</option></select></fieldset></td></tr></tbody></table>
pin码以及我们先前的生日
最后点击
<div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Postback(this); return false;" type="submit">Login</button></div>
登录按钮，
等待监听元素：进入密码设置页面
<form method="post" onsubmit="return false;" novalidate="novalidate" data-fw-form="1" autocomplete="off"><h1>Set Password</h1><p>To protect the security of your account, please specify a new password. The password must meet complexity requirements.</p><div class="password_change" style="margin: 25px 0;"><div class="password_requirements"><div id="password_letter">At least one letter</div><div id="password_capital">At least one capital letter</div><div id="password_number">At least one number</div><div id="password_length">Be at least 12 characters</div><div id="password_mismatch">New passwords must match</div></div><input class="hidden" value="<EMAIL>" autocomplete="off"><table class="plain password_table"><colgroup><col style="width: 150px;"><col></colgroup><tbody><tr><th><label for="p1" data-required="1">New Password</label></th><td><input class="expanded" id="p1" maxlength="32" name="p1" oninput="password_check();" size="32" type="password" value="" data-validate="{ required: true, help: 'New password is required.' }" autofocus="autofocus" autocomplete="off" spellcheck="false" required="required"></td></tr><tr><th><label for="p2" data-required="1">New Password (again)</label></th><td><input class="expanded" id="p2" maxlength="32" name="p2" oninput="password_check();" size="32" type="password" value="" data-validate="{ required: true, help: 'New password is required again.' }" autocomplete="off" spellcheck="false" required="required"></td></tr></tbody></table></div><div class="action"><button class="default" onclick="if (FW.Validate(this)) if (password_verify()) FW.Postback(this); return false;" type="submit">Set Password</button></div></form>

中<tbody><tr><th><label for="p1" data-required="1">New Password</label></th><td><input class="expanded" id="p1" maxlength="32" name="p1" oninput="password_check();" size="32" type="password" value="" data-validate="{ required: true, help: 'New password is required.' }" autofocus="autofocus" autocomplete="off" spellcheck="false" required="required"></td></tr><tr><th><label for="p2" data-required="1">New Password (again)</label></th><td><input class="expanded" id="p2" maxlength="32" name="p2" oninput="password_check();" size="32" type="password" value="" data-validate="{ required: true, help: 'New password is required again.' }" autocomplete="off" spellcheck="false" required="required"></td></tr></tbody>
密码统一设置为 Huangkun729.（注意有一个点）

然后点击<div class="action"><button class="default" onclick="if (FW.Validate(this)) if (password_verify()) FW.Postback(this); return false;" type="submit">Set Password</button></div>
设置按钮
再次监听页面元素，往下滑，找到<p style="text-align: center;"><a href="//" id="start_application_link" onclick="return FW.Lazy.Popup(this);" data-href="?cmd=detail">Start New Application</a></p>
点击Start New Application后
会出现
<div style="width: 500px;"><form action="https://apply.osuokc.edu/apply/?cmd=detail" method="post" onsubmit="return false;" novalidate="novalidate" data-fw-form="1" autocomplete="off"><input name="id" type="hidden" value="80a13d66-a073-42a1-95ee-f785cdf20170"><div class="header ui-draggable-handle dialog_closeable">Start New Application<div class="dialog_close" aria-label="Close" role="button" aria-pressed="false" title="Close" data-clickable="1" tabindex="0"></div></div><div class="content" style="height: 200px;"><div style="padding: 15px 0 0 15px;"><strong></strong><p>Select an application type:</p><div style="margin: 15px 0 15px 0;"><select aria-label="Application Type" id="period" size="1" data-validate="{ required: true, help: 'Select an application type.' }" onchange="var val = $(this).val(); $('.period').showAndEnable(false); $('.period_' + val).showAndEnable(true);" autocomplete="off" required="required"><option></option><option value="5a01843c-8961-4f31-9792-58fc2b9e3e5b">2026 Applications</option><option value="1f8afb2d-cc3b-466f-90b9-85368ef45f1f">2025 Applications</option></select></div><div class="period_5a01843c-8961-4f31-9792-58fc2b9e3e5b period hidden" style="margin-left: 25px;" aria-hidden="true"><select aria-label="Application Subtype" id="round" name="round" size="1" data-validate="{ required: true, help: 'Select an application type.' }" disabled="" autocomplete="off" required="required"><option></option><option value="8ebb4bf2-88da-439f-8941-d0ddb617116c">Fall 2026 Application</option><option value="f227e667-d68e-45b2-af03-22b71e9e24b6">High School Concurrent Fall 2026</option><option value="fc50e2a2-d4de-4390-a45d-2634c3836896">High School Concurrent Spring 2026</option><option value="4162b0cf-299c-434a-ada3-a84c12040eee">Spring 2026 Application</option></select></div><div class="period_1f8afb2d-cc3b-466f-90b9-85368ef45f1f period hidden" style="margin-left: 25px;" aria-hidden="true"><select aria-label="Application Subtype" id="round" name="round" size="1" data-validate="{ required: true, help: 'Select an application type.' }" disabled="" autocomplete="off" required="required"><option></option><option value="000ca4be-4342-46c7-ab68-2d2f93bb1b35">High School Concurrent Fall 2025</option><option value="aa4e1454-eb6b-418f-8676-11d47a3021f7">Fall 2025 Application</option></select></div></div></div><div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Create Application</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div></form></div> 弹窗
我们选择2026 Applications 
选择后会出现另一个复选框（同页面），<div class="period_5a01843c-8961-4f31-9792-58fc2b9e3e5b period" style="margin-left: 25px;" aria-hidden="false"><select aria-label="Application Subtype" id="round" name="round" size="1" data-validate="{ required: true, help: 'Select an application type.' }" autocomplete="off" required="required"><option></option><option value="8ebb4bf2-88da-439f-8941-d0ddb617116c">Fall 2026 Application</option><option value="f227e667-d68e-45b2-af03-22b71e9e24b6">High School Concurrent Fall 2026</option><option value="fc50e2a2-d4de-4390-a45d-2634c3836896">High School Concurrent Spring 2026</option><option value="4162b0cf-299c-434a-ada3-a84c12040eee">Spring 2026 Application</option></select></div> 
我们选择Fall 2026 Application 
然后点击<div class="action"><button class="default" onclick="if (FW.Validate(this)) FW.Lazy.Commit(this, { cmd: 'save' });" type="button">Create Application</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div>
中的Create Application
再次监听页面元素
<div style="width: 500px;"><form method="post" onsubmit="return false;" action="https://apply.osuokc.edu/apply/?id=80a13d66-a073-42a1-95ee-f785cdf20170&amp;cmd=detail" novalidate="novalidate" data-fw-form="1" autocomplete="off"><div class="header ui-draggable-handle dialog_closeable">Application Details<div class="dialog_close" aria-label="Close" role="button" aria-pressed="false" title="Close" data-clickable="1" tabindex="0"></div></div><div class="content" style="height: 200px;"><table class="plain" role="none"><colgroup><col style="width: 100px;"><col></colgroup><tbody><tr><th>Started</th><td>08/10/2025</td></tr><tr><th>Status</th><td>In Progress</td></tr><tr><th></th><td>2026 Applications</td></tr><tr style="vertical-align: top;"><th></th><td><input name="round_existing" type="hidden" value="8ebb4bf2-88da-439f-8941-d0ddb617116c"><input id="round_1" name="round" type="radio" value="8ebb4bf2-88da-439f-8941-d0ddb617116c" checked="checked" autocomplete="off"><label for="round_1">Fall 2026 Application</label><br><input id="round_2" name="round" type="radio" value="f227e667-d68e-45b2-af03-22b71e9e24b6" autocomplete="off"><label for="round_2">High School Concurrent Fall 2026</label><br><input id="round_3" name="round" type="radio" value="4162b0cf-299c-434a-ada3-a84c12040eee" autocomplete="off"><label for="round_3">Spring 2026 Application</label><br><input id="round_4" name="round" type="radio" value="fc50e2a2-d4de-4390-a45d-2634c3836896" autocomplete="off"><label for="round_4">High School Concurrent Spring 2026</label><br></td></tr></tbody></table></div><div class="action"><button class="default" type="button" onclick="var el = $(this).parents('form').find('input[name = &quot;round&quot;]:enabled'); if (el.length &gt; 0) if (el.filter(':checked').length == 0) { alert('Please select an application round.'); el.eq(0).focus(); return; } FW.Lazy.Commit(this, { cmd: 'save' });">Open Application</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div></form></div> 
点击<div class="action"><button class="default" type="button" onclick="var el = $(this).parents('form').find('input[name = &quot;round&quot;]:enabled'); if (el.length &gt; 0) if (el.filter(':checked').length == 0) { alert('Please select an application round.'); el.eq(0).focus(); return; } FW.Lazy.Commit(this, { cmd: 'save' });">Open Application</button><button onclick="FW.Dialog.Unload();" type="button">Cancel</button></div> 中的Open Application

先做这么多，后续再继续，不要关闭浏览器


