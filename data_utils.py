#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理工具
从person_info.csv读取随机用户信息
防止重复使用相同数据
"""

import csv
import random
import os
import json

USED_DATA_FILE = "used_persons.json"

def load_used_persons():
    """加载已使用的人员信息"""
    try:
        if os.path.exists(USED_DATA_FILE):
            with open(USED_DATA_FILE, 'r', encoding='utf-8') as file:
                return set(json.load(file))
        return set()
    except Exception as e:
        print(f"⚠️ 加载已使用数据失败: {e}")
        return set()

def save_used_person(person_key):
    """保存已使用的人员信息"""
    try:
        used_persons = load_used_persons()
        used_persons.add(person_key)
        with open(USED_DATA_FILE, 'w', encoding='utf-8') as file:
            json.dump(list(used_persons), file, ensure_ascii=False, indent=2)
        print(f"📝 记录已使用人员: {person_key}")
    except Exception as e:
        print(f"⚠️ 保存已使用数据失败: {e}")

def read_person_data(csv_file="person_info.csv"):
    """从CSV文件读取人员数据"""
    try:
        if not os.path.exists(csv_file):
            print(f"❌ 找不到文件: {csv_file}")
            return []
        
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            data = list(reader)
            print(f"✅ 成功读取 {len(data)} 条人员数据")
            return data
    except Exception as e:
        print(f"❌ 读取CSV文件错误: {e}")
        return []

def get_random_person():
    """获取一个随机的人员信息（未使用过的）"""
    data = read_person_data()
    if not data:
        return None
    
    used_persons = load_used_persons()
    available_data = []
    
    # 筛选未使用的数据
    for person in data:
        firstname = person.get('Firstname', '').strip()
        lastname = person.get('Lastname', '').strip()
        if firstname and lastname:
            person_key = f"{firstname}_{lastname}"
            if person_key not in used_persons:
                available_data.append(person)
    
    if not available_data:
        print("❌ 所有人员数据都已使用，清空使用记录...")
        # 如果所有数据都用完了，清空记录重新开始
        try:
            os.remove(USED_DATA_FILE)
            return get_random_person()
        except:
            pass
        return None
    
    print(f"🎯 可用人员数据: {len(available_data)} 条")
    person = random.choice(available_data)
    
    # 提取需要的信息
    person_info = {
        'firstname': person.get('Firstname', '').strip(),
        'lastname': person.get('Lastname', '').strip(),
        'full_name': person.get('全名', '').strip(),
        'email': person.get('临时邮箱', '').strip() or person.get('谷歌临时邮箱', '').strip(),
        'phone': person.get('电话', '').strip(),
        'address': person.get('街道地址', '').strip(),
        'city': person.get('城市', '').strip(),
        'state': person.get('州', '').strip(),
        'zipcode': person.get('邮编', '').strip(),
        'birthday': person.get('生日', '').strip(),
        'gender': person.get('性别', '').strip(),
        'ssn': person.get('SSN社会保险号', '').strip()
    }
    
    # 记录为已使用
    person_key = f"{person_info['firstname']}_{person_info['lastname']}"
    save_used_person(person_key)
    
    print(f"🎯 选择人员: {person_info['firstname']} {person_info['lastname']}")
    return person_info

def generate_email_for_person(person_info, domain="caosi.fun"):
    """为人员生成邮箱地址"""
    if not person_info:
        return None
    
    # 生成邮箱用户名（名字+姓氏+随机数字）
    firstname = person_info.get('firstname', '').lower().replace(' ', '')
    lastname = person_info.get('lastname', '').lower().replace(' ', '')
    random_num = random.randint(100, 999)
    
    username = f"{firstname}{lastname}{random_num}"
    email = f"{username}@{domain}"
    
    print(f"📧 生成邮箱: {email}")
    return email

def get_formatted_birthdate():
    """获取格式化的生日（统一使用01/01/2005）"""
    return {
        'month': '01',  # January
        'day': '01',    # 1st
        'year': '2005'  # 2005
    }

if __name__ == "__main__":
    # 测试用例
    print("🧪 测试数据读取...")
    person = get_random_person()
    if person:
        print(f"姓名: {person['firstname']} {person['lastname']}")
        email = generate_email_for_person(person)
        print(f"邮箱: {email}")
        birthdate = get_formatted_birthdate()
        print(f"生日: {birthdate['month']}/{birthdate['day']}/{birthdate['year']}")
    else:
        print("❌ 无法获取人员数据") 