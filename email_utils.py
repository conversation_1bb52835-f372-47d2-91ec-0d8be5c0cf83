#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSU OKC 邮箱PIN码提取工具
基于现有邮箱系统，专门提取数字PIN码
"""

import requests
import time
import re
import random
import string

# 邮箱配置
CLOUDFLARE_DOMAIN = "caosi.fun"
TEMPMAIL_TARGET_EMAIL = "<EMAIL>"
TEMPMAIL_PLUS_EPIN = ""

TEMPMAIL_LIST_URL = f"https://tempmail.plus/api/mails?email={TEMPMAIL_TARGET_EMAIL}&first_id=0&epin={TEMPMAIL_PLUS_EPIN}"
TEMPMAIL_DETAIL_URL_TEMPLATE = f"https://tempmail.plus/api/mails/{{mail_id}}?email={TEMPMAIL_TARGET_EMAIL}&epin={TEMPMAIL_PLUS_EPIN}"

def generate_temp_email():
    """生成一个临时邮箱地址"""
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    return f"{username}@{CLOUDFLARE_DOMAIN}"

def extract_pin_from_email_content(email_content_html, email_content_text):
    """从邮件内容中提取PIN码（优化模式）"""
    print("🔍 开始提取PIN码...")
    
    # 优化PIN码提取模式 - 更精确的匹配
    pin_patterns = [
        r'<strong>(\d{6,12})</strong>',        # HTML strong标签中的数字
        r'<b>(\d{6,12})</b>',                  # HTML b标签中的数字
        r'code:.*?(\d{6,12})',                 # code: 后面的数字
        r'PIN:.*?(\d{6,12})',                  # PIN: 后面的数字
        r'temporary code.*?(\d{6,12})',        # temporary code 后面的数字
        r'activation code.*?(\d{6,12})',       # activation code 后面的数字
        r'verification code.*?(\d{6,12})',     # verification code 后面的数字
        r'use this.*?code.*?(\d{6,12})',       # use this code 后面的数字
        r'(\d{8,12})',                         # 8-12位数字（更严格）
    ]
    
    for content in [email_content_html, email_content_text]:
        if content:
            print(f"📝 检查邮件内容...")
            # 优先检查HTML内容中的特殊标签
            for i, pattern in enumerate(pin_patterns):
                matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                if matches:
                    # 过滤掉明显不是PIN码的数字（如电话号码、地址等）
                    for match in matches:
                        if 6 <= len(match) <= 12 and not match.startswith('0000'):
                            pin_code = match
                            print(f"✅ 成功提取PIN码: {pin_code} (模式 {i+1})")
                            return pin_code
    
    print("❌ 未找到PIN码")
    return None

def get_verification_email(registered_email_address, poll_timeout=90):
    """获取验证邮件（优化速度）"""
    print(f"📧 开始轮询验证邮件: {registered_email_address}")
    start_time = time.time()
    checked_mail_ids = set()
    poll_count = 0
    
    while time.time() - start_time < poll_timeout:
        try:
            poll_count += 1
            elapsed_time = int(time.time() - start_time)
            print(f"🔍 第 {poll_count} 次轮询 (已等待 {elapsed_time} 秒)...")
            
            response = requests.get(TEMPMAIL_LIST_URL, timeout=10)  # 减少超时时间
            response.raise_for_status()
            mail_data = response.json()
            
            if mail_data.get("result") and mail_data.get("mail_list"):
                print(f"📬 找到 {len(mail_data['mail_list'])} 封邮件")
                for mail_item in mail_data["mail_list"]:
                    mail_id = mail_item.get("mail_id")
                    
                    if mail_id in checked_mail_ids:
                        continue
                    
                    detail_url = TEMPMAIL_DETAIL_URL_TEMPLATE.format(mail_id=mail_id)
                    detail_response = requests.get(detail_url, timeout=10)
                    detail_response.raise_for_status()
                    email_detail = detail_response.json()
                    checked_mail_ids.add(mail_id)
                    
                    if email_detail.get("result"):
                        from_mail = email_detail.get("from_mail", "").lower()
                        subject = email_detail.get("subject", "").lower()
                        to_field = email_detail.get("to", "").lower()
                        
                        print(f"📩 检查邮件: {email_detail.get('subject', 'N/A')}")
                        
                        # 优化匹配逻辑 - 更宽泛的匹配
                        sender_match = any(keyword in from_mail for keyword in ["osu", "oklahoma", "okstate", "edu"])
                        subject_match = any(keyword in subject for keyword in ["registration", "account", "temporary", "pin", "code", "activation", "verify"])
                        recipient_match = registered_email_address.lower() in to_field
                        
                        if sender_match and subject_match and recipient_match:
                            print(f"✅ 成功匹配到验证邮件！")
                            return email_detail
                        elif recipient_match and any(keyword in subject for keyword in ["pin", "code", "temporary"]):
                            # 如果收件人匹配且主题包含PIN码相关关键词，也认为匹配
                            print(f"✅ 基于关键词匹配到验证邮件！")
                            return email_detail
            else:
                print(f"📭 邮箱中暂无邮件")
        
        except Exception as e:
            print(f"⚠️ 轮询错误: {e}")
        
        time.sleep(1.5)  # 减少轮询间隔
    
    print(f"⏰ 超时：未找到验证邮件")
    return None

def get_pin_code(registered_email_address):
    """获取PIN码的主函数"""
    print(f"🚀 开始获取PIN码: {registered_email_address}")
    
    # 获取验证邮件
    email_detail = get_verification_email(registered_email_address)
    if not email_detail:
        return None
    
    # 提取PIN码
    html_content = email_detail.get("html", "")
    text_content = email_detail.get("text", "")
    
    pin_code = extract_pin_from_email_content(html_content, text_content)
    if pin_code:
        print(f"🎉 成功获取PIN码: {pin_code}")
        return pin_code
    else:
        print("❌ 无法提取PIN码")
        return None

if __name__ == "__main__":
    # 测试用例
    test_email = "<EMAIL>"
    pin = get_pin_code(test_email)
    if pin:
        print(f"测试成功，PIN码: {pin}")
    else:
        print("测试失败") 