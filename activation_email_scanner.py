#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSU OKC 激活邮件扫描器
专门用于扫描和提取OSU OKC账户激活邮件的信息
"""

import requests
import time
import re
import html
from datetime import datetime
from typing import Optional, Dict, List
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class ActivationEmailScanner:
    """OSU OKC激活邮件扫描器"""
    
    def __init__(self, target_email: str = "<EMAIL>", epin: str = ""):
        """
        初始化扫描器
        
        Args:
            target_email: 目标邮箱地址
            epin: 邮箱访问PIN码
        """
        self.target_email = target_email
        self.epin = epin
        self.list_url = f"https://tempmail.plus/api/mails?email={target_email}&first_id=0&epin={epin}"
        self.detail_url_template = f"https://tempmail.plus/api/mails/{{mail_id}}?email={target_email}&epin={epin}"
        
        # OSU OKC激活邮件识别规则
        self.sender_keywords = ["oklahoma", "okstate", "osu", "edu", "okc.admissions", "webteam"]
        self.subject_keywords = ["activate", "activation", "account", "verify", "verification", "orange key", "o-key"]
        
        # 轮询配置（参考邮件取件系统文档）
        self.poll_interval = 2  # 2秒检查一次
        self.poll_timeout = 300  # 5分钟超时
        
        # 多线程配置
        self.max_workers = 10  # 默认10个线程
        self.result_lock = threading.Lock()  # 结果收集锁
        
        # 激活信息提取模式
        self.activation_patterns = {
            "activation_link": [
                # 改进的激活链接提取模式 - 更全面的匹配
                r'href="([^"]*(?:technolutions\.net|apply\.osuokc\.edu)[^"]*)"',
                r'<a[^>]+href="([^"]*activate[^"]*)"[^>]*>',
                r'href="([^"]*activate[^"]*)"',
                r'(https://[^"\s]*(?:technolutions\.net|apply\.osuokc\.edu)[^"\s]*)',
                r'(https://[^"\s]*activate[^"\s]*)',
                r'<([^>]*(?:technolutions\.net|apply\.osuokc\.edu)[^>]*)>',  # 从文本版本提取
                # Orange Key 激活链接模式
                r'copy and paste\s+([^\s]+)\s+into your web browser',
                r'visit\s+([^\s]*okey\.okstate\.edu[^\s]*)',
                r'(okey\.okstate\.edu)',
            ],
            "temporary_code": [
                r'temporary code[:\s]*(\d{6,12})',
                r'code[:\s]*(\d{6,12})',
                r'<strong>(\d{6,12})</strong>',
                r'<b>(\d{6,12})</b>',
                r'use this[^:]*code[:\s]*(\d{6,12})',
                # Orange Key PIN 码模式
                r'Your pin is\s+(\d{6})',
                r'pin[:\s]+(\d{6})',
                r'PIN[:\s]+(\d{6})',
            ],
            "recipient_name": [
                # 提取收件人姓名的模式
                r'Dear\s+([^,\n\r]+?)(?:,|\s*\n|\s*\r)',
                r'Hello\s+([^,\n\r]+?)(?:,|\s*\n|\s*\r)',
                r'Hi\s+([^,\n\r]+?)(?:,|\s*\n|\s*\r)',
            ]
        }
    
    def is_activation_email(self, email_detail: Dict) -> bool:
        """
        判断是否为OSU OKC激活邮件
        
        Args:
            email_detail: 邮件详情字典
            
        Returns:
            bool: 是否为激活邮件
        """
        from_email = email_detail.get("from_mail", "").lower()
        subject = email_detail.get("subject", "").lower()
        html_content = email_detail.get("html", "").lower()
        text_content = email_detail.get("text", "").lower()
        
        # 检查发送方
        sender_match = any(keyword in from_email for keyword in self.sender_keywords)
        
        # 检查主题
        subject_match = any(keyword in subject for keyword in self.subject_keywords)
        
        # 检查内容（HTML或文本）
        content_match = any(
            keyword in html_content or keyword in text_content 
            for keyword in ["activate", "activation", "temporary code", "application account"]
        )
        
        return sender_match and (subject_match or content_match)
    
    def extract_activation_info(self, email_detail: Dict) -> Dict:
        """
        从激活邮件中提取关键信息
        
        Args:
            email_detail: 邮件详情字典
            
        Returns:
            Dict: 提取的激活信息
        """
        html_content = email_detail.get("html", "")
        text_content = email_detail.get("text", "")
        
        activation_info = {
            "activation_link": None,
            "temporary_code": None,
            "recipient_name": None,
            "extraction_timestamp": datetime.now().isoformat(),
            "email_metadata": {
                "subject": email_detail.get("subject", ""),
                "from_mail": email_detail.get("from_mail", ""),
                "to_mail": email_detail.get("to", ""),
                "mail_id": email_detail.get("mail_id", ""),
                "date": email_detail.get("date", "")
            }
        }
        
        # 提取激活链接
        for i, pattern in enumerate(self.activation_patterns["activation_link"]):
            for content_type, content in [("HTML", html_content), ("TEXT", text_content)]:
                if content:
                    matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                    if matches:
                        # 解码HTML实体并清理链接
                        activation_link = html.unescape(matches[0]).strip()
                        
                        # 如果是title属性中的链接，优先使用
                        if 'title=' in activation_link:
                            title_match = re.search(r'title="([^"]+)"', activation_link)
                            if title_match:
                                activation_link = title_match.group(1)
                        
                        activation_info["activation_link"] = activation_link
                        break
            if activation_info["activation_link"]:
                break
        
        # 提取临时验证码
        for i, pattern in enumerate(self.activation_patterns["temporary_code"]):
            for content_type, content in [("HTML", html_content), ("TEXT", text_content)]:
                if content:
                    matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                    if matches:
                        # 选择最可能的验证码（6-12位数字）
                        for match in matches:
                            if 6 <= len(match) <= 12 and not match.startswith('0000'):
                                activation_info["temporary_code"] = match
                                break
                        if activation_info["temporary_code"]:
                            break
            if activation_info["temporary_code"]:
                break
        
        # 提取收件人姓名
        for i, pattern in enumerate(self.activation_patterns["recipient_name"]):
            for content_type, content in [("HTML", html_content), ("TEXT", text_content)]:
                if content:
                    matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                    if matches:
                        # 清理姓名（去除HTML标签和多余空格）
                        name = re.sub(r'<[^>]+>', '', matches[0]).strip()
                        if name and len(name) < 100:  # 确保是合理的姓名长度
                            activation_info["recipient_name"] = name
                            break
            if activation_info["recipient_name"]:
                break
        
        return activation_info
    
    def get_mail_list(self, last_id: Optional[str] = None, limit: int = 20) -> tuple[List[Dict], bool]:
        """
        获取邮箱中的邮件列表（支持分页）
        
        Args:
            last_id: 最后一个邮件ID，用于分页
            limit: 每页邮件数量
        
        Returns:
            tuple[List[Dict], bool]: (邮件列表, 是否还有更多邮件)
        """
        try:
            if last_id:
                # 分页获取：从指定ID开始向下获取
                url = f"https://tempmail.plus/api/mails?email={self.target_email}&last_id={last_id}&limit={limit}&epin={self.epin}"
            else:
                # 默认获取：获取最新邮件
                url = f"https://tempmail.plus/api/mails?email={self.target_email}&first_id=0&epin={self.epin}"
            
            response = requests.get(url, timeout=15)
            response.raise_for_status()
            
            mail_data = response.json()
            if mail_data.get("result") and mail_data.get("mail_list"):
                mail_list = mail_data["mail_list"]
                has_more = mail_data.get("more", False)
                return mail_list, has_more
            else:
                return [], False
                
        except Exception as e:
            print(f"❌ 获取邮件列表失败: {e}")
            return [], False
    
    def get_mail_detail(self, mail_id: str) -> Optional[Dict]:
        """
        获取指定邮件的详细内容
        
        Args:
            mail_id: 邮件ID
            
        Returns:
            Optional[Dict]: 邮件详情
        """
        try:
            detail_url = self.detail_url_template.format(mail_id=mail_id)
            response = requests.get(detail_url, timeout=15)
            response.raise_for_status()
            
            email_detail = response.json()
            if email_detail.get("result"):
                return email_detail
            else:
                return None
                
        except Exception as e:
            return None
    
    def process_single_email(self, mail_item: Dict, thread_id: str) -> Optional[Dict]:
        """
        处理单封邮件（多线程调用）
        
        Args:
            mail_item: 邮件基本信息
            thread_id: 线程ID
            
        Returns:
            Optional[Dict]: 激活邮件信息，如果不是激活邮件则返回None
        """
        mail_id = mail_item.get("mail_id")
        if not mail_id:
            return None
        
        # 获取邮件详情
        email_detail = self.get_mail_detail(mail_id)
        if not email_detail:
            return None
        
        # 检查是否为激活邮件
        if self.is_activation_email(email_detail):
            print(f"[{thread_id}] 🎯 发现激活邮件 ID: {mail_id}")
            
            # 提取激活信息
            activation_info = self.extract_activation_info(email_detail)
            
            # 保存完整邮件内容用于调试
            activation_info["raw_email"] = {
                "html": email_detail.get("html", ""),
                "text": email_detail.get("text", ""),
                "full_detail": email_detail
            }
            
            # 线程安全输出
            recipient = activation_info.get('recipient_name', 'N/A')
            code = activation_info.get('temporary_code', 'N/A')
            print(f"[{thread_id}] ✅ 提取完成: {recipient} - 验证码: {code}")
            
            return activation_info
        
        return None
    
    def scan_limited_emails_concurrent(self, max_emails: int) -> List[Dict]:
        """
        多线程并发扫描指定数量的邮件
        
        Args:
            max_emails: 最大邮件数量
            
        Returns:
            List[Dict]: 激活邮件信息列表
        """
        print(f"🚀 启动多线程扫描（{self.max_workers}个线程）")
        print(f"🎯 扫描最新 {max_emails} 封邮件...")
        print(f"📬 正在获取邮件列表: {self.target_email}")
        
        activation_emails = []
        processed_count = 0
        last_id = None
        page_count = 0
        
        # 收集所有要处理的邮件
        all_mail_items = []
        
        while processed_count < max_emails:
            page_count += 1
            print(f"\n📄 获取第 {page_count} 页邮件...")
            
            # 获取邮件列表（支持分页）
            mail_list, has_more = self.get_mail_list(last_id, limit=50)  # 增加每页数量
            
            if not mail_list:
                print("📭 没有更多邮件了")
                break
            
            print(f"📧 获取到 {len(mail_list)} 封邮件，还有更多: {has_more}")
            
            # 添加邮件到处理队列
            for mail_item in mail_list:
                if processed_count >= max_emails:
                    break
                
                mail_id = mail_item.get("mail_id")
                if mail_id:
                    all_mail_items.append(mail_item)
                    processed_count += 1
                    last_id = mail_id  # 更新last_id用于下次分页
            
            # 如果没有更多邮件了，停止翻页
            if not has_more:
                print("📄 已到达邮件列表末尾")
                break
        
        print(f"\n🔄 开始多线程处理 {len(all_mail_items)} 封邮件...")
        
        # 多线程并发处理邮件
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_mail = {
                executor.submit(self.process_single_email, mail_item, f"T{i%self.max_workers+1:02d}"): mail_item
                for i, mail_item in enumerate(all_mail_items)
            }
            
            # 收集结果
            completed_count = 0
            for future in as_completed(future_to_mail):
                completed_count += 1
                mail_item = future_to_mail[future]
                
                try:
                    result = future.result()
                    if result:  # 如果是激活邮件
                        with self.result_lock:
                            activation_emails.append(result)
                        
                    # 显示进度
                    if completed_count % 20 == 0 or completed_count == len(all_mail_items):
                        print(f"📊 进度: {completed_count}/{len(all_mail_items)} - 已找到激活邮件: {len(activation_emails)} 封")
                        
                except Exception as e:
                    print(f"⚠️ 处理邮件 {mail_item.get('mail_id')} 时出错: {e}")
        
        print(f"\n🎉 多线程扫描完成！")
        print(f"📊 统计结果:")
        print(f"   处理邮件数: {len(all_mail_items)}")
        print(f"   翻页次数: {page_count}")
        print(f"   激活邮件数: {len(activation_emails)}")
        print(f"   使用线程数: {self.max_workers}")
        
        if activation_emails:
            print(f"\n📋 激活邮件详情:")
            for i, email in enumerate(activation_emails, 1):
                print(f"   {i}. 收件人: {email.get('recipient_name', 'N/A')}")
                print(f"      验证码: {email.get('temporary_code', 'N/A')}")
                print(f"      链接: {'有' if email.get('activation_link') else '无'}")
        
        return activation_emails
    
    def scan_single_email(self, target_email_address: str, poll_timeout: int = 120) -> Optional[Dict]:
        """
        监控指定邮箱地址，等待并扫描激活邮件
        
        Args:
            target_email_address: 目标邮箱地址
            poll_timeout: 轮询超时时间（秒）
            
        Returns:
            Optional[Dict]: 找到的激活邮件信息
        """
        print(f"🎯 开始监控邮箱 {target_email_address} 的激活邮件...")
        start_time = time.time()
        checked_mail_ids = set()
        
        while time.time() - start_time < poll_timeout:
            mail_list, has_more = self.get_mail_list()
            
            for mail_item in mail_list:
                mail_id = mail_item.get("mail_id")
                if mail_id in checked_mail_ids:
                    continue
                
                checked_mail_ids.add(mail_id)
                email_detail = self.get_mail_detail(mail_id)
                
                if email_detail and self.is_activation_email(email_detail):
                    # 检查收件方是否匹配
                    to_field = email_detail.get("to", "").lower()
                    if target_email_address.lower() in to_field:
                        print(f"✅ 找到目标邮箱 {target_email_address} 的激活邮件！")
                        activation_info = self.extract_activation_info(email_detail)
                        activation_info["raw_email"] = {
                            "html": email_detail.get("html", ""),
                            "text": email_detail.get("text", ""),
                            "full_detail": email_detail
                        }
                        return activation_info
            
            elapsed = int(time.time() - start_time)
            print(f"⏰ 已等待 {elapsed}s，继续监控...")
            time.sleep(2)
        
        print(f"⏰ 超时：未找到 {target_email_address} 的激活邮件")
        return None

if __name__ == "__main__":
    # 测试用例
    scanner = ActivationEmailScanner()
    
    print("选择扫描模式:")
    print("1. 快速扫描（只扫描当前邮件）")
    print("2. 持续扫描（持续监控新邮件）")
    print("3. 多线程并发扫描（扫描指定数量邮件）")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        activation_emails = scanner.scan_activation_emails()
    elif choice == "2":
        timeout = int(input("请输入超时时间（秒，默认300）: ") or "300")
        activation_emails = scanner.scan_continuous_emails(poll_timeout=timeout)
    elif choice == "3":
        max_emails = int(input("请输入要扫描的最大邮件数量: ") or "100")
        activation_emails = scanner.scan_limited_emails_concurrent(max_emails)
    else:
        print("无效选择，使用快速扫描")
        activation_emails = scanner.scan_activation_emails()
    
    if activation_emails:
        print(f"\n📊 扫描结果摘要:")
        for i, email in enumerate(activation_emails, 1):
            print(f"{i}. 主题: {email['email_metadata']['subject']}")
            print(f"   收件人: {email.get('recipient_name', 'N/A')}")
            print(f"   激活链接: {email['activation_link'][:80] if email['activation_link'] else 'N/A'}...")
            print(f"   验证码: {email['temporary_code'] or 'N/A'}")
    else:
        print("😞 未找到任何激活邮件") 